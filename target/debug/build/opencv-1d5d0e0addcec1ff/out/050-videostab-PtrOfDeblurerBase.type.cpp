extern "C" {
	const cv::videostab::DeblurerBase* cv_PtrLcv_videostab_DeblurerBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::DeblurerBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::DeblurerBase* cv_PtrLcv_videostab_DeblurerBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::DeblurerBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_DeblurerBaseG_delete(cv::Ptr<cv::videostab::DeblurerBase>* instance) {
			delete instance;
	}
	
}

