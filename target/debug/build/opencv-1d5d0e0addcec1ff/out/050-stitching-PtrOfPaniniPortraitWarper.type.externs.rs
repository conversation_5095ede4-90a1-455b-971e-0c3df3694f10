pub fn cv_PtrLcv_PaniniPortraitWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_PaniniPortraitWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_PaniniPortraitWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_PaniniPortraitWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_PaniniPortraitWarperG_new_const_PaniniPortraitWarper(val: *mut c_void) -> *mut c_void;
