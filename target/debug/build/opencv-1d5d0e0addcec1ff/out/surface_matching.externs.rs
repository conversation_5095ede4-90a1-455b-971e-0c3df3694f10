pub fn cv_ppf_match_3d_ICP_ICP(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_ICP_ICP_const_int_const_float_const_float_const_int_const_int_const_int(iterations: i32, tolerence: f32, rejection_scale: f32, num_levels: i32, sample_type: i32, num_max_corr: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_ICP_ICP_const_int(iterations: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_ICP_registerModelToScene_const_MatR_const_MatR_doubleR_Matx44dR(instance: *mut c_void, src_pc: *const c_void, dst_pc: *const c_void, residual: *mut f64, pose: *mut core::Matx44d, ocvrs_return: *mut Result<i32>);
pub fn cv_ppf_match_3d_ICP_registerModelToScene_const_MatR_const_MatR_vectorLPose3DPtrGR(instance: *mut c_void, src_pc: *const c_void, dst_pc: *const c_void, poses: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ppf_match_3d_ICP_delete(instance: *mut c_void);
pub fn cv_ppf_match_3d_PPF3DDetector_PPF3DDetector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_PPF3DDetector_PPF3DDetector_const_double_const_double_const_double(relative_sampling_step: f64, relative_distance_step: f64, num_angles: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_PPF3DDetector_PPF3DDetector_const_double(relative_sampling_step: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_PPF3DDetector_setSearchParams_const_double_const_double_const_bool(instance: *mut c_void, position_threshold: f64, rotation_threshold: f64, use_weighted_clustering: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_PPF3DDetector_setSearchParams(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_PPF3DDetector_trainModel_const_MatR(instance: *mut c_void, model: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_PPF3DDetector_match_const_MatR_vectorLPose3DPtrGR_const_double_const_double(instance: *mut c_void, scene: *const c_void, results: *mut c_void, relative_scene_sample_step: f64, relative_scene_distance: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_PPF3DDetector_match_const_MatR_vectorLPose3DPtrGR(instance: *mut c_void, scene: *const c_void, results: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_PPF3DDetector_delete(instance: *mut c_void);
pub fn cv_ppf_match_3d_Pose3D_Pose3D(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_Pose3D_Pose3D_double_size_t_size_t(alpha: f64, model_index: size_t, num_votes: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_Pose3D_Pose3D_double(alpha: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_Pose3D_updatePose_Matx44dR(instance: *mut c_void, new_pose: *mut core::Matx44d, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_Pose3D_updatePose_Matx33dR_Vec3dR(instance: *mut c_void, new_r: *mut core::Matx33d, new_t: *mut core::Vec3d, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_Pose3D_updatePoseQuat_Vec4dR_Vec3dR(instance: *mut c_void, q: *mut core::Vec4d, new_t: *mut core::Vec3d, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_Pose3D_appendPose_Matx44dR(instance: *mut c_void, incremental_pose: *mut core::Matx44d, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_Pose3D_printPose(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_Pose3D_clone(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_Pose3D_writePose_const_stringR(instance: *mut c_void, file_name: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_ppf_match_3d_Pose3D_readPose_const_stringR(instance: *mut c_void, file_name: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_ppf_match_3d_Pose3D_propAlpha_const(instance: *const c_void) -> f64;
pub fn cv_ppf_match_3d_Pose3D_propAlpha_const_double(instance: *mut c_void, val: f64);
pub fn cv_ppf_match_3d_Pose3D_propResidual_const(instance: *const c_void) -> f64;
pub fn cv_ppf_match_3d_Pose3D_propResidual_const_double(instance: *mut c_void, val: f64);
pub fn cv_ppf_match_3d_Pose3D_propModelIndex_const(instance: *const c_void) -> size_t;
pub fn cv_ppf_match_3d_Pose3D_propModelIndex_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_ppf_match_3d_Pose3D_propNumVotes_const(instance: *const c_void) -> size_t;
pub fn cv_ppf_match_3d_Pose3D_propNumVotes_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_ppf_match_3d_Pose3D_propPose_const(instance: *const c_void, ocvrs_return: *mut core::Matx44d);
pub fn cv_ppf_match_3d_Pose3D_propPose_const_Matx44d(instance: *mut c_void, val: *const core::Matx44d);
pub fn cv_ppf_match_3d_Pose3D_propAngle_const(instance: *const c_void) -> f64;
pub fn cv_ppf_match_3d_Pose3D_propAngle_const_double(instance: *mut c_void, val: f64);
pub fn cv_ppf_match_3d_Pose3D_propT_const(instance: *const c_void, ocvrs_return: *mut core::Vec3d);
pub fn cv_ppf_match_3d_Pose3D_propT_const_Vec3d(instance: *mut c_void, val: *const core::Vec3d);
pub fn cv_ppf_match_3d_Pose3D_propQ_const(instance: *const c_void, ocvrs_return: *mut core::Vec4d);
pub fn cv_ppf_match_3d_Pose3D_propQ_const_Vec4d(instance: *mut c_void, val: *const core::Vec4d);
pub fn cv_ppf_match_3d_Pose3D_delete(instance: *mut c_void);
pub fn cv_ppf_match_3d_PoseCluster3D_PoseCluster3D(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_PoseCluster3D_PoseCluster3D_Pose3DPtr(new_pose: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_PoseCluster3D_PoseCluster3D_Pose3DPtr_int(new_pose: *mut c_void, new_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ppf_match_3d_PoseCluster3D_addPose_Pose3DPtr(instance: *mut c_void, new_pose: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ppf_match_3d_PoseCluster3D_writePoseCluster_const_stringR(instance: *mut c_void, file_name: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_ppf_match_3d_PoseCluster3D_readPoseCluster_const_stringR(instance: *mut c_void, file_name: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_ppf_match_3d_PoseCluster3D_propPoseList_const(instance: *const c_void) -> *mut c_void;
pub fn cv_ppf_match_3d_PoseCluster3D_propPoseList_const_vectorLPose3DPtrG(instance: *mut c_void, val: *const c_void);
pub fn cv_ppf_match_3d_PoseCluster3D_propNumVotes_const(instance: *const c_void) -> size_t;
pub fn cv_ppf_match_3d_PoseCluster3D_propNumVotes_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_ppf_match_3d_PoseCluster3D_propId_const(instance: *const c_void) -> i32;
pub fn cv_ppf_match_3d_PoseCluster3D_propId_const_int(instance: *mut c_void, val: i32);
pub fn cv_ppf_match_3d_PoseCluster3D_delete(instance: *mut c_void);
