pub fn cv_PtrLcv_text_OCRBeamSearchDecoderG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRBeamSearchDecoderG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRBeamSearchDecoderG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_text_OCRBeamSearchDecoderG_to_PtrOfBaseOCR(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRBeamSearchDecoderG_new_const_OCRBeamSearchDecoder(val: *mut c_void) -> *mut c_void;
