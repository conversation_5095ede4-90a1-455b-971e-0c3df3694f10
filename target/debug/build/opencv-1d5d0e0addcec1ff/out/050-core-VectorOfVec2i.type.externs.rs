pub fn std_vectorLcv_Vec2iG_new_const() -> *mut c_void;
pub fn std_vectorLcv_Vec2iG_delete(instance: *mut c_void);
pub fn std_vectorLcv_Vec2iG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec2iG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_Vec2iG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec2iG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_Vec2iG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_Vec2iG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_Vec2iG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_Vec2iG_clear(instance: *mut c_void);
pub fn std_vectorLcv_Vec2iG_push_const_Vec2i(instance: *mut c_void, val: *const core::Vec2i);
pub fn std_vectorLcv_Vec2iG_insert_size_t_const_Vec2i(instance: *mut c_void, index: size_t, val: *const core::Vec2i);
pub fn std_vectorLcv_Vec2iG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Vec2i);
pub fn std_vectorLcv_Vec2iG_set_size_t_const_Vec2i(instance: *mut c_void, index: size_t, val: *const core::Vec2i);
pub fn std_vectorLcv_Vec2iG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_Vec2iG_data_const(instance: *const c_void) -> *const core::Vec2i;
pub fn std_vectorLcv_Vec2iG_dataMut(instance: *mut c_void) -> *mut core::Vec2i;
pub fn cv_fromSlice_const_const_Vec2iX_size_t(data: *const core::Vec2i, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_Vec2iG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec2iG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec2iG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
