pub fn cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_to_PtrOfISparseOptFlowEstimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_to_PtrOfPyrLkOptFlowEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_new_const_SparsePyrLkOptFlowEstimator(val: *mut c_void) -> *mut c_void;
