pub type VectorOfBarcodeType = core::Vector<crate::barcode::BarcodeType>;

impl core::Vector<crate::barcode::BarcodeType> {
	pub fn as_raw_VectorOfBarcodeType(&self) -> extern_send!(Self) { self.as_raw() }
	pub fn as_raw_mut_VectorOfBarcodeType(&mut self) -> extern_send!(mut Self) { self.as_raw_mut() }
}

vector_extern! { crate::barcode::BarcodeType,
	std_vectorLcv_barcode_BarcodeTypeG_new_const, std_vectorLcv_barcode_BarcodeTypeG_delete,
	std_vectorLcv_barcode_BarcodeTypeG_len_const, std_vectorLcv_barcode_BarcodeTypeG_isEmpty_const,
	std_vectorLcv_barcode_BarcodeTypeG_capacity_const, std_vectorLcv_barcode_BarcodeTypeG_shrinkToFit,
	std_vectorLcv_barcode_BarcodeTypeG_reserve_size_t, std_vectorLcv_barcode_BarcodeTypeG_remove_size_t,
	std_vectorLcv_barcode_BarcodeTypeG_swap_size_t_size_t, std_vectorLcv_barcode_BarcodeTypeG_clear,
	std_vectorLcv_barcode_BarcodeTypeG_get_const_size_t, std_vectorLcv_barcode_BarcodeTypeG_set_size_t_const_BarcodeType,
	std_vectorLcv_barcode_BarcodeTypeG_push_const_BarcodeType, std_vectorLcv_barcode_BarcodeTypeG_insert_size_t_const_BarcodeType,
}
vector_copy_non_bool! { crate::barcode::BarcodeType,
	std_vectorLcv_barcode_BarcodeTypeG_data_const, std_vectorLcv_barcode_BarcodeTypeG_dataMut, cv_fromSlice_const_const_BarcodeTypeX_size_t,
	std_vectorLcv_barcode_BarcodeTypeG_clone_const,
}

