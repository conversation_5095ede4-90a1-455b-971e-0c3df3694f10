pub fn cv_PtrLcv_detail_AffineBasedEstimatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_AffineBasedEstimatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_AffineBasedEstimatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_AffineBasedEstimatorG_to_PtrOfDetail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_AffineBasedEstimatorG_new_const_AffineBasedEstimator(val: *mut c_void) -> *mut c_void;
