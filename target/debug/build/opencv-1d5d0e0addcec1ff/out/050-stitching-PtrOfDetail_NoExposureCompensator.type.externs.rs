pub fn cv_PtrLcv_detail_NoExposureCompensatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoExposureCompensatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoExposureCompensatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_NoExposureCompensatorG_to_PtrOfDetail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoExposureCompensatorG_new_const_NoExposureCompensator(val: *mut c_void) -> *mut c_void;
