extern "C" {
	const cv::dnn::DataAugmentationLayer* cv_PtrLcv_dnn_DataAugmentationLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::DataAugmentationLayer* cv_PtrLcv_dnn_DataAugmentationLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_DataAugmentationLayerG_delete(cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_DataAugmentationLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_DataAugmentationLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::DataAugmentationLayer>* cv_PtrLcv_dnn_DataAugmentationLayerG_new_const_DataAugmentationLayer(cv::dnn::DataAugmentationLayer* val) {
			return new cv::Ptr<cv::dnn::DataAugmentationLayer>(val);
	}
	
}

