extern "C" {
	const cv::videostab::IFrameSource* cv_PtrLcv_videostab_IFrameSourceG_getInnerPtr_const(const cv::Ptr<cv::videostab::IFrameSource>* instance) {
			return instance->get();
	}
	
	cv::videostab::IFrameSource* cv_PtrLcv_videostab_IFrameSourceG_getInnerPtrMut(cv::Ptr<cv::videostab::IFrameSource>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_IFrameSourceG_delete(cv::Ptr<cv::videostab::IFrameSource>* instance) {
			delete instance;
	}
	
}

