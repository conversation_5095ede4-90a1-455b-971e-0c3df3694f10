pub fn cv_PtrLcv_videostab_GaussianMotionFilterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_GaussianMotionFilterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_GaussianMotionFilterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_GaussianMotionFilterG_to_PtrOfIMotionStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_GaussianMotionFilterG_to_PtrOfMotionFilterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_GaussianMotionFilterG_new_const_GaussianMotionFilter(val: *mut c_void) -> *mut c_void;
