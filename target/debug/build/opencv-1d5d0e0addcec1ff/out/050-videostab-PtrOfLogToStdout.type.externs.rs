pub fn cv_PtrLcv_videostab_LogToStdoutG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_LogToStdoutG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_LogToStdoutG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_LogToStdoutG_to_PtrOfILog(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_LogToStdoutG_new_const_LogToStdout(val: *mut c_void) -> *mut c_void;
