pub fn cv_PtrLcv_detail_BundleAdjusterRayG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterRayG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterRayG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_BundleAdjusterRayG_to_PtrOfDetail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterRayG_to_PtrOfDetail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterRayG_new_const_BundleAdjusterRay(val: *mut c_void) -> *mut c_void;
