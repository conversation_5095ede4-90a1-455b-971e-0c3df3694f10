pub fn std_vectorLcv_KeyPointG_new_const() -> *mut c_void;
pub fn std_vectorLcv_KeyPointG_delete(instance: *mut c_void);
pub fn std_vectorLcv_KeyPointG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_KeyPointG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_KeyPointG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_KeyPointG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_KeyPointG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_KeyPointG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_KeyPointG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_KeyPointG_clear(instance: *mut c_void);
pub fn std_vectorLcv_KeyPointG_push_const_KeyPoint(instance: *mut c_void, val: *const c_void);
pub fn std_vectorLcv_KeyPointG_insert_size_t_const_KeyPoint(instance: *mut c_void, index: size_t, val: *const c_void);
pub fn std_vectorLcv_KeyPointG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut *mut c_void);
pub fn std_vectorLcv_KeyPointG_set_size_t_const_KeyPoint(instance: *mut c_void, index: size_t, val: *const c_void);
