pub type VectorOfLineMod_Template = core::Vector<crate::rgbd::LineMod_Template>;

impl core::Vector<crate::rgbd::LineMod_Template> {
	pub fn as_raw_VectorOfLineMod_Template(&self) -> extern_send!(Self) { self.as_raw() }
	pub fn as_raw_mut_VectorOfLineMod_Template(&mut self) -> extern_send!(mut Self) { self.as_raw_mut() }
}

vector_extern! { crate::rgbd::LineMod_Template,
	std_vectorLcv_linemod_TemplateG_new_const, std_vectorLcv_linemod_TemplateG_delete,
	std_vectorLcv_linemod_TemplateG_len_const, std_vectorLcv_linemod_TemplateG_isEmpty_const,
	std_vectorLcv_linemod_TemplateG_capacity_const, std_vectorLcv_linemod_TemplateG_shrinkToFit,
	std_vectorLcv_linemod_TemplateG_reserve_size_t, std_vectorLcv_linemod_TemplateG_remove_size_t,
	std_vectorLcv_linemod_TemplateG_swap_size_t_size_t, std_vectorLcv_linemod_TemplateG_clear,
	std_vectorLcv_linemod_TemplateG_get_const_size_t, std_vectorLcv_linemod_TemplateG_set_size_t_const_Template,
	std_vectorLcv_linemod_TemplateG_push_const_Template, std_vectorLcv_linemod_TemplateG_insert_size_t_const_Template,
}
vector_non_copy_or_bool! { clone crate::rgbd::LineMod_Template }

