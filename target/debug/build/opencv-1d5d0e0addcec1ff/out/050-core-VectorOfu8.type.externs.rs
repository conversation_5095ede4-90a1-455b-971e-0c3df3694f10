pub fn std_vectorLunsigned_charG_new_const() -> *mut c_void;
pub fn std_vectorLunsigned_charG_delete(instance: *mut c_void);
pub fn std_vectorLunsigned_charG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLunsigned_charG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLunsigned_charG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLunsigned_charG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLunsigned_charG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLunsigned_charG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLunsigned_charG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLunsigned_charG_clear(instance: *mut c_void);
pub fn std_vectorLunsigned_charG_push_const_unsigned_char(instance: *mut c_void, val: u8);
pub fn std_vectorLunsigned_charG_insert_size_t_const_unsigned_char(instance: *mut c_void, index: size_t, val: u8);
pub fn std_vectorLunsigned_charG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut u8);
pub fn std_vectorLunsigned_charG_set_size_t_const_unsigned_char(instance: *mut c_void, index: size_t, val: u8);
pub fn std_vectorLunsigned_charG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLunsigned_charG_data_const(instance: *const c_void) -> *const u8;
pub fn std_vectorLunsigned_charG_dataMut(instance: *mut c_void) -> *mut u8;
pub fn cv_fromSlice_const_const_unsigned_charX_size_t(data: *const u8, len: size_t) -> *mut c_void;
pub fn std_vectorLunsigned_charG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLunsigned_charG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLunsigned_charG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
