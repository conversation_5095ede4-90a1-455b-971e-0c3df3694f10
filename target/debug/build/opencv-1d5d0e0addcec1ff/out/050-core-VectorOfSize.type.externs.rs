pub fn std_vectorLcv_SizeG_new_const() -> *mut c_void;
pub fn std_vectorLcv_SizeG_delete(instance: *mut c_void);
pub fn std_vectorLcv_SizeG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_SizeG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_SizeG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_SizeG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_SizeG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_SizeG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_SizeG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_SizeG_clear(instance: *mut c_void);
pub fn std_vectorLcv_SizeG_push_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn std_vectorLcv_SizeG_insert_size_t_const_Size(instance: *mut c_void, index: size_t, val: *const core::Size);
pub fn std_vectorLcv_SizeG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Size);
pub fn std_vectorLcv_SizeG_set_size_t_const_Size(instance: *mut c_void, index: size_t, val: *const core::Size);
pub fn std_vectorLcv_SizeG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_SizeG_data_const(instance: *const c_void) -> *const core::Size;
pub fn std_vectorLcv_SizeG_dataMut(instance: *mut c_void) -> *mut core::Size;
pub fn cv_fromSlice_const_const_SizeX_size_t(data: *const core::Size, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_SizeG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_SizeG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_SizeG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
