pub fn std_vectorLcv_Point2fG_new_const() -> *mut c_void;
pub fn std_vectorLcv_Point2fG_delete(instance: *mut c_void);
pub fn std_vectorLcv_Point2fG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Point2fG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_Point2fG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Point2fG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_Point2fG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_Point2fG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_Point2fG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_Point2fG_clear(instance: *mut c_void);
pub fn std_vectorLcv_Point2fG_push_const_Point2f(instance: *mut c_void, val: *const core::Point2f);
pub fn std_vectorLcv_Point2fG_insert_size_t_const_Point2f(instance: *mut c_void, index: size_t, val: *const core::Point2f);
pub fn std_vectorLcv_Point2fG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Point2f);
pub fn std_vectorLcv_Point2fG_set_size_t_const_Point2f(instance: *mut c_void, index: size_t, val: *const core::Point2f);
pub fn std_vectorLcv_Point2fG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_Point2fG_data_const(instance: *const c_void) -> *const core::Point2f;
pub fn std_vectorLcv_Point2fG_dataMut(instance: *mut c_void) -> *mut core::Point2f;
pub fn cv_fromSlice_const_const_Point2fX_size_t(data: *const core::Point2f, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_Point2fG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Point2fG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Point2fG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
