pub fn cv_PtrLcv_ml_RTreesG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_RTreesG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_RTreesG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ml_RTreesG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_RTreesG_to_PtrOfDTrees(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_RTreesG_to_PtrOfStatModel(instance: *mut c_void) -> *mut c_void;
