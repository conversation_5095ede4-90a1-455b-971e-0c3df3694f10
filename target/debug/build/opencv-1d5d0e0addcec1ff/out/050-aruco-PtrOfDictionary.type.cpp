extern "C" {
	const cv::aruco::Dictionary* cv_PtrLcv_aruco_DictionaryG_getInnerPtr_const(const cv::Ptr<cv::aruco::Dictionary>* instance) {
			return instance->get();
	}
	
	cv::aruco::Dictionary* cv_PtrLcv_aruco_DictionaryG_getInnerPtrMut(cv::Ptr<cv::aruco::Dictionary>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_DictionaryG_delete(cv::Ptr<cv::aruco::Dictionary>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::Dictionary>* cv_PtrLcv_aruco_DictionaryG_new_const_Dictionary(cv::aruco::Dictionary* val) {
			return new cv::Ptr<cv::aruco::Dictionary>(val);
	}
	
}

