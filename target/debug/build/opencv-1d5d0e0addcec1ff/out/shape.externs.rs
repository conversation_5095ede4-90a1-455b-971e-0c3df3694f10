pub fn cv_EMDL1_const__InputArrayR_const__InputArrayR(signature1: *const c_void, signature2: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_createAffineTransformer_bool(full_affine: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createChiHistogramCostExtractor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createChiHistogramCostExtractor_int_float(n_dummies: i32, default_cost: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createEMDHistogramCostExtractor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createEMDHistogramCostExtractor_int_int_float(flag: i32, n_dummies: i32, default_cost: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createEMDL1HistogramCostExtractor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createEMDL1HistogramCostExtractor_int_float(n_dummies: i32, default_cost: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createHausdorffDistanceExtractor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createHausdorffDistanceExtractor_int_float(distance_flag: i32, rank_prop: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createNormHistogramCostExtractor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createNormHistogramCostExtractor_int_int_float(flag: i32, n_dummies: i32, default_cost: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createShapeContextDistanceExtractor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createShapeContextDistanceExtractor_int_int_float_float_int_const_PtrLHistogramCostExtractorGR_const_PtrLShapeTransformerGR(n_angular_bins: i32, n_radial_bins: i32, inner_radius: f32, outer_radius: f32, iterations: i32, comparer: *const c_void, transformer: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createThinPlateSplineShapeTransformer(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createThinPlateSplineShapeTransformer_double(regularization_parameter: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AffineTransformer_setFullAffine_bool(instance: *mut c_void, full_affine: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_AffineTransformer_getFullAffine_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_AffineTransformer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_AffineTransformer_to_ShapeTransformer(instance: *mut c_void) -> *mut c_void;
pub fn cv_AffineTransformer_delete(instance: *mut c_void);
pub fn cv_ChiHistogramCostExtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ChiHistogramCostExtractor_to_HistogramCostExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_ChiHistogramCostExtractor_delete(instance: *mut c_void);
pub fn cv_EMDHistogramCostExtractor_setNormFlag_int(instance: *mut c_void, flag: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_EMDHistogramCostExtractor_getNormFlag_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_EMDHistogramCostExtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_EMDHistogramCostExtractor_to_HistogramCostExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_EMDHistogramCostExtractor_delete(instance: *mut c_void);
pub fn cv_EMDL1HistogramCostExtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_EMDL1HistogramCostExtractor_to_HistogramCostExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_EMDL1HistogramCostExtractor_delete(instance: *mut c_void);
pub fn cv_HausdorffDistanceExtractor_setDistanceFlag_int(instance: *mut c_void, distance_flag: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_HausdorffDistanceExtractor_getDistanceFlag_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_HausdorffDistanceExtractor_setRankProportion_float(instance: *mut c_void, rank_proportion: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_HausdorffDistanceExtractor_getRankProportion_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_HausdorffDistanceExtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_HausdorffDistanceExtractor_to_ShapeDistanceExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_HausdorffDistanceExtractor_delete(instance: *mut c_void);
pub fn cv_HistogramCostExtractor_buildCostMatrix_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, descriptors1: *const c_void, descriptors2: *const c_void, cost_matrix: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HistogramCostExtractor_setNDummies_int(instance: *mut c_void, n_dummies: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_HistogramCostExtractor_getNDummies_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_HistogramCostExtractor_setDefaultCost_float(instance: *mut c_void, default_cost: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_HistogramCostExtractor_getDefaultCost_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_HistogramCostExtractor_to_ChiHistogramCostExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_HistogramCostExtractor_to_EMDHistogramCostExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_HistogramCostExtractor_to_EMDL1HistogramCostExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_HistogramCostExtractor_to_NormHistogramCostExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_HistogramCostExtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_HistogramCostExtractor_delete(instance: *mut c_void);
pub fn cv_NormHistogramCostExtractor_setNormFlag_int(instance: *mut c_void, flag: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_NormHistogramCostExtractor_getNormFlag_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_NormHistogramCostExtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_NormHistogramCostExtractor_to_HistogramCostExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_NormHistogramCostExtractor_delete(instance: *mut c_void);
pub fn cv_ShapeContextDistanceExtractor_setAngularBins_int(instance: *mut c_void, n_angular_bins: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getAngularBins_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ShapeContextDistanceExtractor_setRadialBins_int(instance: *mut c_void, n_radial_bins: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getRadialBins_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ShapeContextDistanceExtractor_setInnerRadius_float(instance: *mut c_void, inner_radius: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getInnerRadius_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeContextDistanceExtractor_setOuterRadius_float(instance: *mut c_void, outer_radius: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getOuterRadius_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeContextDistanceExtractor_setRotationInvariant_bool(instance: *mut c_void, rotation_invariant: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getRotationInvariant_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ShapeContextDistanceExtractor_setShapeContextWeight_float(instance: *mut c_void, shape_context_weight: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getShapeContextWeight_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeContextDistanceExtractor_setImageAppearanceWeight_float(instance: *mut c_void, image_appearance_weight: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getImageAppearanceWeight_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeContextDistanceExtractor_setBendingEnergyWeight_float(instance: *mut c_void, bending_energy_weight: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getBendingEnergyWeight_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeContextDistanceExtractor_setImages_const__InputArrayR_const__InputArrayR(instance: *mut c_void, image1: *const c_void, image2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getImages_const_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, image1: *const c_void, image2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_setIterations_int(instance: *mut c_void, iterations: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ShapeContextDistanceExtractor_setCostExtractor_PtrLHistogramCostExtractorG(instance: *mut c_void, comparer: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getCostExtractor_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ShapeContextDistanceExtractor_setStdDev_float(instance: *mut c_void, sigma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getStdDev_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeContextDistanceExtractor_setTransformAlgorithm_PtrLShapeTransformerG(instance: *mut c_void, transformer: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeContextDistanceExtractor_getTransformAlgorithm_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ShapeContextDistanceExtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ShapeContextDistanceExtractor_to_ShapeDistanceExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_ShapeContextDistanceExtractor_delete(instance: *mut c_void);
pub fn cv_ShapeDistanceExtractor_computeDistance_const__InputArrayR_const__InputArrayR(instance: *mut c_void, contour1: *const c_void, contour2: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeDistanceExtractor_to_HausdorffDistanceExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_ShapeDistanceExtractor_to_ShapeContextDistanceExtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_ShapeDistanceExtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ShapeDistanceExtractor_delete(instance: *mut c_void);
pub fn cv_ShapeTransformer_estimateTransformation_const__InputArrayR_const__InputArrayR_vectorLDMatchGR(instance: *mut c_void, transforming_shape: *const c_void, target_shape: *const c_void, matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeTransformer_applyTransformation_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, input: *const c_void, output: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeTransformer_applyTransformation_const__InputArrayR(instance: *mut c_void, input: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ShapeTransformer_warpImage_const_const__InputArrayR_const__OutputArrayR_int_int_const_ScalarR(instance: *const c_void, transforming_image: *const c_void, output: *const c_void, flags: i32, border_mode: i32, border_value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeTransformer_warpImage_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, transforming_image: *const c_void, output: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ShapeTransformer_to_AffineTransformer(instance: *mut c_void) -> *mut c_void;
pub fn cv_ShapeTransformer_to_ThinPlateSplineShapeTransformer(instance: *mut c_void) -> *mut c_void;
pub fn cv_ShapeTransformer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ShapeTransformer_delete(instance: *mut c_void);
pub fn cv_ThinPlateSplineShapeTransformer_setRegularizationParameter_double(instance: *mut c_void, beta: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ThinPlateSplineShapeTransformer_getRegularizationParameter_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ThinPlateSplineShapeTransformer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ThinPlateSplineShapeTransformer_to_ShapeTransformer(instance: *mut c_void) -> *mut c_void;
pub fn cv_ThinPlateSplineShapeTransformer_delete(instance: *mut c_void);
