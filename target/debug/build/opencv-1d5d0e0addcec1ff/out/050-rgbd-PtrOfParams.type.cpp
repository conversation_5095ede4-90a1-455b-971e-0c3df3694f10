extern "C" {
	const cv::large_kinfu::Params* cv_PtrLcv_large_kinfu_ParamsG_getInnerPtr_const(const cv::Ptr<cv::large_kinfu::Params>* instance) {
			return instance->get();
	}
	
	cv::large_kinfu::Params* cv_PtrLcv_large_kinfu_ParamsG_getInnerPtrMut(cv::Ptr<cv::large_kinfu::Params>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_large_kinfu_ParamsG_delete(cv::Ptr<cv::large_kinfu::Params>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::large_kinfu::Params>* cv_PtrLcv_large_kinfu_ParamsG_new_const_Params(cv::large_kinfu::Params* val) {
			return new cv::Ptr<cv::large_kinfu::Params>(val);
	}
	
}

