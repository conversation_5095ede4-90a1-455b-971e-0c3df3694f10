pub fn cv_PtrLcv_detail_GraphCutSeamFinderG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_GraphCutSeamFinderG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_GraphCutSeamFinderG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_GraphCutSeamFinderG_to_PtrOfDetail_GraphCutSeamFinderBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_GraphCutSeamFinderG_to_PtrOfDetail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_GraphCutSeamFinderG_new_const_GraphCutSeamFinder(val: *mut c_void) -> *mut c_void;
