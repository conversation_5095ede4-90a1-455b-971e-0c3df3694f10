pub fn cv_ximgproc_BrightEdges_MatR_MatR(_original: *mut c_void, _edgeview: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_BrightEdges_MatR_MatR_int_int_int(_original: *mut c_void, _edgeview: *mut c_void, contrast: i32, shortrange: i32, longrange: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_FastHoughTransform_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, dst_mat_depth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_FastHoughTransform_const__InputArrayR_const__OutputArrayR_int_int_int_int(src: *const c_void, dst: *const c_void, dst_mat_depth: i32, angle_range: i32, op: i32, make_skew: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_GradientDericheX_const__InputArrayR_const__OutputArrayR_double_double(op: *const c_void, dst: *const c_void, alpha: f64, omega: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_GradientDericheY_const__InputArrayR_const__OutputArrayR_double_double(op: *const c_void, dst: *const c_void, alpha: f64, omega: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_GradientPaillouX_const__InputArrayR_const__OutputArrayR_double_double(op: *const c_void, _dst: *const c_void, alpha: f64, omega: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_GradientPaillouY_const__InputArrayR_const__OutputArrayR_double_double(op: *const c_void, _dst: *const c_void, alpha: f64, omega: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_HoughPoint2Line_const_PointR_const__InputArrayR(hough_point: *const core::Point, src_img_info: *const c_void, ocvrs_return: *mut Result<core::Vec4i>);
pub fn cv_ximgproc_HoughPoint2Line_const_PointR_const__InputArrayR_int_int_int(hough_point: *const core::Point, src_img_info: *const c_void, angle_range: i32, make_skew: i32, rules: i32, ocvrs_return: *mut Result<core::Vec4i>);
pub fn cv_ximgproc_PeiLinNormalization_const__InputArrayR(i: *const c_void, ocvrs_return: *mut Result<core::Matx23d>);
pub fn cv_ximgproc_PeiLinNormalization_const__InputArrayR_const__OutputArrayR(i: *const c_void, t: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_amFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double(joint: *const c_void, src: *const c_void, dst: *const c_void, sigma_s: f64, sigma_r: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_amFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double_bool(joint: *const c_void, src: *const c_void, dst: *const c_void, sigma_s: f64, sigma_r: f64, adjust_outliers: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_anisotropicDiffusion_const__InputArrayR_const__OutputArrayR_float_float_int(src: *const c_void, dst: *const c_void, alpha: f32, k: f32, niters: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_bilateralTextureFilter_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_bilateralTextureFilter_const__InputArrayR_const__OutputArrayR_int_int_double_double(src: *const c_void, dst: *const c_void, fr: i32, num_iter: i32, sigma_alpha: f64, sigma_avg: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_colorMatchTemplate_const__InputArrayR_const__InputArrayR_const__OutputArrayR(img: *const c_void, templ: *const c_void, result: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_computeBadPixelPercent_const__InputArrayR_const__InputArrayR_Rect(gt: *const c_void, src: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<f64>);
pub fn cv_ximgproc_computeBadPixelPercent_const__InputArrayR_const__InputArrayR_Rect_int(gt: *const c_void, src: *const c_void, roi: *const core::Rect, thresh: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_ximgproc_computeMSE_const__InputArrayR_const__InputArrayR_Rect(gt: *const c_void, src: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<f64>);
pub fn cv_ximgproc_contourSampling_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, out: *const c_void, nb_elt: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_covarianceEstimation_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, window_rows: i32, window_cols: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_createAMFilter_double_double(sigma_s: f64, sigma_r: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createAMFilter_double_double_bool(sigma_s: f64, sigma_r: f64, adjust_outliers: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createContourFitting(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createContourFitting_int_int(ctr: i32, fd: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createDTFilter_const__InputArrayR_double_double(guide: *const c_void, sigma_spatial: f64, sigma_color: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createDTFilter_const__InputArrayR_double_double_int_int(guide: *const c_void, sigma_spatial: f64, sigma_color: f64, mode: i32, num_iters: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createDisparityWLSFilterGeneric_bool(use_confidence: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createDisparityWLSFilter_PtrLStereoMatcherG(matcher_left: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createEdgeAwareInterpolator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createEdgeBoxes(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createEdgeBoxes_float_float_float_float_int_float_float_float_float_float_float_float(alpha: f32, beta: f32, eta: f32, min_score: f32, max_boxes: i32, edge_min_mag: f32, edge_merge_thr: f32, cluster_min_mag: f32, max_aspect_ratio: f32, min_box_area: f32, gamma: f32, kappa: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createEdgeDrawing(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createFastBilateralSolverFilter_const__InputArrayR_double_double_double(guide: *const c_void, sigma_spatial: f64, sigma_luma: f64, sigma_chroma: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createFastBilateralSolverFilter_const__InputArrayR_double_double_double_double_int_double(guide: *const c_void, sigma_spatial: f64, sigma_luma: f64, sigma_chroma: f64, lambda: f64, num_iter: i32, max_tol: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createFastGlobalSmootherFilter_const__InputArrayR_double_double(guide: *const c_void, lambda: f64, sigma_color: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createFastGlobalSmootherFilter_const__InputArrayR_double_double_double_int(guide: *const c_void, lambda: f64, sigma_color: f64, lambda_attenuation: f64, num_iter: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createFastLineDetector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createFastLineDetector_int_float_double_double_int_bool(length_threshold: i32, distance_threshold: f32, canny_th1: f64, canny_th2: f64, canny_aperture_size: i32, do_merge: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createGuidedFilter_const__InputArrayR_int_double(guide: *const c_void, radius: i32, eps: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createQuaternionImage_const__InputArrayR_const__OutputArrayR(img: *const c_void, qimg: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_createRFFeatureGetter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createRICInterpolator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createRightMatcher_PtrLStereoMatcherG(matcher_left: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createStructuredEdgeDetection_const_StringR(model: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createStructuredEdgeDetection_const_StringR_PtrLconst_RFFeatureGetterG(model: *const c_char, how_to_get_features: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createSuperpixelLSC_const__InputArrayR(image: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createSuperpixelLSC_const__InputArrayR_int_float(image: *const c_void, region_size: i32, ratio: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createSuperpixelSEEDS_int_int_int_int_int(image_width: i32, image_height: i32, image_channels: i32, num_superpixels: i32, num_levels: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createSuperpixelSEEDS_int_int_int_int_int_int_int_bool(image_width: i32, image_height: i32, image_channels: i32, num_superpixels: i32, num_levels: i32, prior: i32, histogram_bins: i32, double_step: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createSuperpixelSLIC_const__InputArrayR(image: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_createSuperpixelSLIC_const__InputArrayR_int_int_float(image: *const c_void, algorithm: i32, region_size: i32, ruler: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_dtFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double(guide: *const c_void, src: *const c_void, dst: *const c_void, sigma_spatial: f64, sigma_color: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_dtFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double_int_int(guide: *const c_void, src: *const c_void, dst: *const c_void, sigma_spatial: f64, sigma_color: f64, mode: i32, num_iters: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_edgePreservingFilter_const__InputArrayR_const__OutputArrayR_int_double(src: *const c_void, dst: *const c_void, d: i32, threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_fastBilateralSolverFilter_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(guide: *const c_void, src: *const c_void, confidence: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_fastBilateralSolverFilter_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double_double_double_int_double(guide: *const c_void, src: *const c_void, confidence: *const c_void, dst: *const c_void, sigma_spatial: f64, sigma_luma: f64, sigma_chroma: f64, lambda: f64, num_iter: i32, max_tol: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_fastGlobalSmootherFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double(guide: *const c_void, src: *const c_void, dst: *const c_void, lambda: f64, sigma_color: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_fastGlobalSmootherFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double_double_int(guide: *const c_void, src: *const c_void, dst: *const c_void, lambda: f64, sigma_color: f64, lambda_attenuation: f64, num_iter: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_fourierDescriptor_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_fourierDescriptor_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, nb_elt: i32, nb_fd: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_getDisparityVis_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_getDisparityVis_const__InputArrayR_const__OutputArrayR_double(src: *const c_void, dst: *const c_void, scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_guidedFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double(guide: *const c_void, src: *const c_void, dst: *const c_void, radius: i32, eps: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_guidedFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double_int(guide: *const c_void, src: *const c_void, dst: *const c_void, radius: i32, eps: f64, d_depth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_jointBilateralFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double_double(joint: *const c_void, src: *const c_void, dst: *const c_void, d: i32, sigma_color: f64, sigma_space: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_jointBilateralFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double_double_int(joint: *const c_void, src: *const c_void, dst: *const c_void, d: i32, sigma_color: f64, sigma_space: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_l0Smooth_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_l0Smooth_const__InputArrayR_const__OutputArrayR_double_double(src: *const c_void, dst: *const c_void, lambda: f64, kappa: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_niBlackThreshold_const__InputArrayR_const__OutputArrayR_double_int_int_double(_src: *const c_void, _dst: *const c_void, max_value: f64, typ: i32, block_size: i32, k: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_niBlackThreshold_const__InputArrayR_const__OutputArrayR_double_int_int_double_int_double(_src: *const c_void, _dst: *const c_void, max_value: f64, typ: i32, block_size: i32, k: f64, binarization_method: i32, r: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_qconj_const__InputArrayR_const__OutputArrayR(qimg: *const c_void, qcimg: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_qdft_const__InputArrayR_const__OutputArrayR_int_bool(img: *const c_void, qimg: *const c_void, flags: i32, side_left: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_qmultiply_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_qunitary_const__InputArrayR_const__OutputArrayR(qimg: *const c_void, qnimg: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_readGT_String_const__OutputArrayR(src_path: *const c_char, dst: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_rl_createRLEImage_const_vectorLPoint3iGR_const__OutputArrayR(runs: *const c_void, res: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_createRLEImage_const_vectorLPoint3iGR_const__OutputArrayR_Size(runs: *const c_void, res: *const c_void, size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_dilate_const__InputArrayR_const__OutputArrayR_const__InputArrayR(rl_src: *const c_void, rl_dest: *const c_void, rl_kernel: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_dilate_const__InputArrayR_const__OutputArrayR_const__InputArrayR_Point(rl_src: *const c_void, rl_dest: *const c_void, rl_kernel: *const c_void, anchor: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_erode_const__InputArrayR_const__OutputArrayR_const__InputArrayR(rl_src: *const c_void, rl_dest: *const c_void, rl_kernel: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_erode_const__InputArrayR_const__OutputArrayR_const__InputArrayR_bool_Point(rl_src: *const c_void, rl_dest: *const c_void, rl_kernel: *const c_void, b_boundary_on: bool, anchor: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_getStructuringElement_int_Size(shape: i32, ksize: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_rl_isRLMorphologyPossible_const__InputArrayR(rl_structuring_element: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ximgproc_rl_morphologyEx_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR(rl_src: *const c_void, rl_dest: *const c_void, op: i32, rl_kernel: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_morphologyEx_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR_bool_Point(rl_src: *const c_void, rl_dest: *const c_void, op: i32, rl_kernel: *const c_void, b_boundary_on_for_erosion: bool, anchor: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_paint_const__InputOutputArrayR_const__InputArrayR_const_ScalarR(image: *const c_void, rl_src: *const c_void, value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rl_threshold_const__InputArrayR_const__OutputArrayR_double_int(src: *const c_void, rl_dest: *const c_void, thresh: f64, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rollingGuidanceFilter_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_rollingGuidanceFilter_const__InputArrayR_const__OutputArrayR_int_double_double_int_int(src: *const c_void, dst: *const c_void, d: i32, sigma_color: f64, sigma_space: f64, num_of_iter: i32, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_createGraphSegmentation(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createGraphSegmentation_double_float_int(sigma: f64, k: f32, min_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentation(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategyColor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategyFill(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategyMultiple(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategyMultiple_PtrLSelectiveSearchSegmentationStrategyG(s1: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategyMultiple_PtrLSelectiveSearchSegmentationStrategyG_PtrLSelectiveSearchSegmentationStrategyG(s1: *mut c_void, s2: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategyMultiple_PtrLSelectiveSearchSegmentationStrategyG_PtrLSelectiveSearchSegmentationStrategyG_PtrLSelectiveSearchSegmentationStrategyG(s1: *mut c_void, s2: *mut c_void, s3: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategyMultiple_PtrLSelectiveSearchSegmentationStrategyG_PtrLSelectiveSearchSegmentationStrategyG_PtrLSelectiveSearchSegmentationStrategyG_PtrLSelectiveSearchSegmentationStrategyG(s1: *mut c_void, s2: *mut c_void, s3: *mut c_void, s4: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategySize(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_segmentation_createSelectiveSearchSegmentationStrategyTexture(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_thinning_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_thinning_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, thinning_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_transformFD_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src: *const c_void, t: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_transformFD_const__InputArrayR_const__InputArrayR_const__OutputArrayR_bool(src: *const c_void, t: *const c_void, dst: *const c_void, fd_contour: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_weightedMedianFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int(joint: *const c_void, src: *const c_void, dst: *const c_void, r: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_weightedMedianFilter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double_int_const__InputArrayR(joint: *const c_void, src: *const c_void, dst: *const c_void, r: i32, sigma: f64, weight_type: i32, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_filter_const__InputArrayR_const__OutputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, joint: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_filter_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_collectGarbage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_AdaptiveManifoldFilter_getSigmaS_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ximgproc_AdaptiveManifoldFilter_setSigmaS_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_getSigmaR_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ximgproc_AdaptiveManifoldFilter_setSigmaR_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_getTreeHeight_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_AdaptiveManifoldFilter_setTreeHeight_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_getPCAIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_AdaptiveManifoldFilter_setPCAIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_getAdjustOutliers_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ximgproc_AdaptiveManifoldFilter_setAdjustOutliers_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_getUseRNG_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ximgproc_AdaptiveManifoldFilter_setUseRNG_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_AdaptiveManifoldFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_AdaptiveManifoldFilter_delete(instance: *mut c_void);
pub fn cv_ximgproc_ContourFitting_ContourFitting_int_int(ctr: i32, fd: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_ContourFitting_ContourFitting(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_ContourFitting_estimateTransformation_const__InputArrayR_const__InputArrayR_const__OutputArrayR_doubleX_bool(instance: *mut c_void, src: *const c_void, dst: *const c_void, alpha_phi_st: *const c_void, dist: *mut f64, fd_contour: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_ContourFitting_estimateTransformation_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, alpha_phi_st: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_ContourFitting_estimateTransformation_const__InputArrayR_const__InputArrayR_const__OutputArrayR_doubleR_bool(instance: *mut c_void, src: *const c_void, dst: *const c_void, alpha_phi_st: *const c_void, dist: *mut f64, fd_contour: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_ContourFitting_estimateTransformation_const__InputArrayR_const__InputArrayR_const__OutputArrayR_doubleR(instance: *mut c_void, src: *const c_void, dst: *const c_void, alpha_phi_st: *const c_void, dist: *mut f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_ContourFitting_setCtrSize_int(instance: *mut c_void, n: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_ContourFitting_setFDSize_int(instance: *mut c_void, n: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_ContourFitting_getCtrSize(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_ContourFitting_getFDSize(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_ContourFitting_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_ContourFitting_delete(instance: *mut c_void);
pub fn cv_ximgproc_DTFilter_filter_const__InputArrayR_const__OutputArrayR_int(instance: *mut c_void, src: *const c_void, dst: *const c_void, d_depth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_DTFilter_filter_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_DTFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_DTFilter_delete(instance: *mut c_void);
pub fn cv_ximgproc_DisparityFilter_filter_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR_Rect_const__InputArrayR(instance: *mut c_void, disparity_map_left: *const c_void, left_view: *const c_void, filtered_disparity_map: *const c_void, disparity_map_right: *const c_void, roi: *const core::Rect, right_view: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_DisparityFilter_filter_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, disparity_map_left: *const c_void, left_view: *const c_void, filtered_disparity_map: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_DisparityFilter_to_DisparityWLSFilter(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_DisparityFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_DisparityFilter_delete(instance: *mut c_void);
pub fn cv_ximgproc_DisparityWLSFilter_getLambda(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ximgproc_DisparityWLSFilter_setLambda_double(instance: *mut c_void, _lambda: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_DisparityWLSFilter_getSigmaColor(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ximgproc_DisparityWLSFilter_setSigmaColor_double(instance: *mut c_void, _sigma_color: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_DisparityWLSFilter_getLRCthresh(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_DisparityWLSFilter_setLRCthresh_int(instance: *mut c_void, _lrc_thresh: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_DisparityWLSFilter_getDepthDiscontinuityRadius(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_DisparityWLSFilter_setDepthDiscontinuityRadius_int(instance: *mut c_void, _disc_radius: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_DisparityWLSFilter_getConfidenceMap(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_DisparityWLSFilter_getROI(instance: *mut c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_ximgproc_DisparityWLSFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_DisparityWLSFilter_to_DisparityFilter(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_DisparityWLSFilter_delete(instance: *mut c_void);
pub fn cv_ximgproc_EdgeAwareInterpolator_setCostMap_const_MatR(instance: *mut c_void, _cost_map: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeAwareInterpolator_setK_int(instance: *mut c_void, _k: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeAwareInterpolator_getK(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_EdgeAwareInterpolator_setSigma_float(instance: *mut c_void, _sigma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeAwareInterpolator_getSigma(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeAwareInterpolator_setLambda_float(instance: *mut c_void, _lambda: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeAwareInterpolator_getLambda(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeAwareInterpolator_setUsePostProcessing_bool(instance: *mut c_void, _use_post_proc: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeAwareInterpolator_getUsePostProcessing(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ximgproc_EdgeAwareInterpolator_setFGSLambda_float(instance: *mut c_void, _lambda: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeAwareInterpolator_getFGSLambda(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeAwareInterpolator_setFGSSigma_float(instance: *mut c_void, _sigma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeAwareInterpolator_getFGSSigma(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeAwareInterpolator_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_EdgeAwareInterpolator_to_SparseMatchInterpolator(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_EdgeAwareInterpolator_delete(instance: *mut c_void);
pub fn cv_ximgproc_EdgeBoxes_getBoundingBoxes_const__InputArrayR_const__InputArrayR_vectorLRectGR_const__OutputArrayR(instance: *mut c_void, edge_map: *const c_void, orientation_map: *const c_void, boxes: *mut c_void, scores: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getBoundingBoxes_const__InputArrayR_const__InputArrayR_vectorLRectGR(instance: *mut c_void, edge_map: *const c_void, orientation_map: *const c_void, boxes: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getAlpha_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setAlpha_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getBeta_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setBeta_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getEta_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setEta_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getMinScore_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setMinScore_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getMaxBoxes_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_EdgeBoxes_setMaxBoxes_int(instance: *mut c_void, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getEdgeMinMag_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setEdgeMinMag_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getEdgeMergeThr_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setEdgeMergeThr_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getClusterMinMag_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setClusterMinMag_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getMaxAspectRatio_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setMaxAspectRatio_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getMinBoxArea_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setMinBoxArea_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getGamma_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setGamma_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_getKappa_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_EdgeBoxes_setKappa_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeBoxes_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_EdgeBoxes_delete(instance: *mut c_void);
pub fn cv_ximgproc_EdgeDrawing_detectEdges_const__InputArrayR(instance: *mut c_void, src: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeDrawing_getEdgeImage_const__OutputArrayR(instance: *mut c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeDrawing_getGradientImage_const__OutputArrayR(instance: *mut c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeDrawing_getSegments(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_EdgeDrawing_detectLines_const__OutputArrayR(instance: *mut c_void, lines: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeDrawing_detectEllipses_const__OutputArrayR(instance: *mut c_void, ellipses: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeDrawing_setParams_const_ParamsR(instance: *mut c_void, parameters: *const crate::ximgproc::EdgeDrawing_Params, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeDrawing_propParams_const(instance: *const c_void, ocvrs_return: *mut crate::ximgproc::EdgeDrawing_Params);
pub fn cv_ximgproc_EdgeDrawing_propParams_const_Params(instance: *mut c_void, val: *const crate::ximgproc::EdgeDrawing_Params);
pub fn cv_ximgproc_EdgeDrawing_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_EdgeDrawing_delete(instance: *mut c_void);
pub fn cv_ximgproc_EdgeDrawing_Params_Params(ocvrs_return: *mut Result<crate::ximgproc::EdgeDrawing_Params>);
pub fn cv_ximgproc_EdgeDrawing_Params_read_const_FileNodeR(instance: *const crate::ximgproc::EdgeDrawing_Params, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_EdgeDrawing_Params_write_const_FileStorageR(instance: *const crate::ximgproc::EdgeDrawing_Params, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_FastBilateralSolverFilter_filter_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, confidence: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_FastBilateralSolverFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_FastBilateralSolverFilter_delete(instance: *mut c_void);
pub fn cv_ximgproc_FastGlobalSmootherFilter_filter_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_FastGlobalSmootherFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_FastGlobalSmootherFilter_delete(instance: *mut c_void);
pub fn cv_ximgproc_FastLineDetector_detect_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, lines: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_FastLineDetector_drawSegments_const__InputOutputArrayR_const__InputArrayR_bool_Scalar_int(instance: *mut c_void, image: *const c_void, lines: *const c_void, draw_arrow: bool, linecolor: *const core::Scalar, linethickness: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_FastLineDetector_drawSegments_const__InputOutputArrayR_const__InputArrayR(instance: *mut c_void, image: *const c_void, lines: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_FastLineDetector_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_FastLineDetector_delete(instance: *mut c_void);
pub fn cv_ximgproc_GuidedFilter_filter_const__InputArrayR_const__OutputArrayR_int(instance: *mut c_void, src: *const c_void, dst: *const c_void, d_depth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_GuidedFilter_filter_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_GuidedFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_GuidedFilter_delete(instance: *mut c_void);
pub fn cv_ximgproc_RFFeatureGetter_getFeatures_const_const_MatR_MatR_const_int_const_int_const_int_const_int_const_int(instance: *const c_void, src: *const c_void, features: *mut c_void, gnrm_rad: i32, gsmth_rad: i32, shrink: i32, out_num: i32, grad_num: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RFFeatureGetter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_RFFeatureGetter_delete(instance: *mut c_void);
pub fn cv_ximgproc_RICInterpolator_setK_int(instance: *mut c_void, k: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setK(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getK_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_RICInterpolator_setCostMap_const_MatR(instance: *mut c_void, cost_map: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setSuperpixelSize_int(instance: *mut c_void, sp_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setSuperpixelSize(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getSuperpixelSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_RICInterpolator_setSuperpixelNNCnt_int(instance: *mut c_void, sp_nn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setSuperpixelNNCnt(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getSuperpixelNNCnt_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_RICInterpolator_setSuperpixelRuler_float(instance: *mut c_void, ruler: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setSuperpixelRuler(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getSuperpixelRuler_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_RICInterpolator_setSuperpixelMode_int(instance: *mut c_void, mode: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setSuperpixelMode(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getSuperpixelMode_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_RICInterpolator_setAlpha_float(instance: *mut c_void, alpha: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setAlpha(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getAlpha_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_RICInterpolator_setModelIter_int(instance: *mut c_void, model_iter: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setModelIter(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getModelIter_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_RICInterpolator_setRefineModels_bool(instance: *mut c_void, refine_modles: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setRefineModels(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getRefineModels_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ximgproc_RICInterpolator_setMaxFlow_float(instance: *mut c_void, max_flow: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setMaxFlow(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getMaxFlow_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_RICInterpolator_setUseVariationalRefinement_bool(instance: *mut c_void, use_variational_refinement: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setUseVariationalRefinement(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getUseVariationalRefinement_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ximgproc_RICInterpolator_setUseGlobalSmootherFilter_bool(instance: *mut c_void, use_fgs: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setUseGlobalSmootherFilter(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getUseGlobalSmootherFilter_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ximgproc_RICInterpolator_setFGSLambda_float(instance: *mut c_void, lambda: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setFGSLambda(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getFGSLambda_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_RICInterpolator_setFGSSigma_float(instance: *mut c_void, sigma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_setFGSSigma(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RICInterpolator_getFGSSigma_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_RICInterpolator_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_RICInterpolator_to_SparseMatchInterpolator(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_RICInterpolator_delete(instance: *mut c_void);
pub fn cv_ximgproc_RidgeDetectionFilter_create_int_int_int_int_int_double_double_int(ddepth: i32, dx: i32, dy: i32, ksize: i32, out_dtype: i32, scale: f64, delta: f64, border_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_RidgeDetectionFilter_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ximgproc_RidgeDetectionFilter_getRidgeFilteredImage_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, _img: *const c_void, out: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_RidgeDetectionFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_RidgeDetectionFilter_delete(instance: *mut c_void);
pub fn cv_ximgproc_SparseMatchInterpolator_interpolate_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, from_image: *const c_void, from_points: *const c_void, to_image: *const c_void, to_points: *const c_void, dense_flow: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SparseMatchInterpolator_to_EdgeAwareInterpolator(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_SparseMatchInterpolator_to_RICInterpolator(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_SparseMatchInterpolator_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_SparseMatchInterpolator_delete(instance: *mut c_void);
pub fn cv_ximgproc_StructuredEdgeDetection_detectEdges_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, _src: *const c_void, _dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_StructuredEdgeDetection_computeOrientation_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, _src: *const c_void, _dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_StructuredEdgeDetection_edgesNms_const_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_int_float_bool(instance: *const c_void, edge_image: *const c_void, orientation_image: *const c_void, _dst: *const c_void, r: i32, s: i32, m: f32, is_parallel: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_StructuredEdgeDetection_edgesNms_const_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *const c_void, edge_image: *const c_void, orientation_image: *const c_void, _dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_StructuredEdgeDetection_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_StructuredEdgeDetection_delete(instance: *mut c_void);
pub fn cv_ximgproc_SuperpixelLSC_getNumberOfSuperpixels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_SuperpixelLSC_iterate_int(instance: *mut c_void, num_iterations: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelLSC_iterate(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelLSC_getLabels_const_const__OutputArrayR(instance: *const c_void, labels_out: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelLSC_getLabelContourMask_const_const__OutputArrayR_bool(instance: *const c_void, image: *const c_void, thick_line: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelLSC_getLabelContourMask_const_const__OutputArrayR(instance: *const c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelLSC_enforceLabelConnectivity_int(instance: *mut c_void, min_element_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelLSC_enforceLabelConnectivity(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelLSC_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_SuperpixelLSC_delete(instance: *mut c_void);
pub fn cv_ximgproc_SuperpixelSEEDS_getNumberOfSuperpixels(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_SuperpixelSEEDS_iterate_const__InputArrayR_int(instance: *mut c_void, img: *const c_void, num_iterations: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSEEDS_iterate_const__InputArrayR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSEEDS_getLabels_const__OutputArrayR(instance: *mut c_void, labels_out: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSEEDS_getLabelContourMask_const__OutputArrayR_bool(instance: *mut c_void, image: *const c_void, thick_line: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSEEDS_getLabelContourMask_const__OutputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSEEDS_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_SuperpixelSEEDS_delete(instance: *mut c_void);
pub fn cv_ximgproc_SuperpixelSLIC_getNumberOfSuperpixels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_SuperpixelSLIC_iterate_int(instance: *mut c_void, num_iterations: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSLIC_iterate(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSLIC_getLabels_const_const__OutputArrayR(instance: *const c_void, labels_out: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSLIC_getLabelContourMask_const_const__OutputArrayR_bool(instance: *const c_void, image: *const c_void, thick_line: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSLIC_getLabelContourMask_const_const__OutputArrayR(instance: *const c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSLIC_enforceLabelConnectivity_int(instance: *mut c_void, min_element_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSLIC_enforceLabelConnectivity(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_SuperpixelSLIC_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_SuperpixelSLIC_delete(instance: *mut c_void);
pub fn cv_ximgproc_segmentation_GraphSegmentation_processImage_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_GraphSegmentation_setSigma_double(instance: *mut c_void, sigma: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_GraphSegmentation_getSigma(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ximgproc_segmentation_GraphSegmentation_setK_float(instance: *mut c_void, k: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_GraphSegmentation_getK(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_segmentation_GraphSegmentation_setMinSize_int(instance: *mut c_void, min_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_GraphSegmentation_getMinSize(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ximgproc_segmentation_GraphSegmentation_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_GraphSegmentation_delete(instance: *mut c_void);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_setBaseImage_const__InputArrayR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_switchToSingleStrategy_int_float(instance: *mut c_void, k: i32, sigma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_switchToSingleStrategy(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_switchToSelectiveSearchFast_int_int_float(instance: *mut c_void, base_k: i32, inc_k: i32, sigma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_switchToSelectiveSearchFast(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_switchToSelectiveSearchQuality_int_int_float(instance: *mut c_void, base_k: i32, inc_k: i32, sigma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_switchToSelectiveSearchQuality(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_addImage_const__InputArrayR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_clearImages(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_addGraphSegmentation_PtrLGraphSegmentationG(instance: *mut c_void, g: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_clearGraphSegmentations(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_addStrategy_PtrLSelectiveSearchSegmentationStrategyG(instance: *mut c_void, s: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_clearStrategies(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_process_vectorLRectGR(instance: *mut c_void, rects: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentation_delete(instance: *mut c_void);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_setImage_const__InputArrayR_const__InputArrayR_const__InputArrayR_int(instance: *mut c_void, img: *const c_void, regions: *const c_void, sizes: *const c_void, image_id: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_setImage_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, img: *const c_void, regions: *const c_void, sizes: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_get_int_int(instance: *mut c_void, r1: i32, r2: i32, ocvrs_return: *mut Result<f32>);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_merge_int_int(instance: *mut c_void, r1: i32, r2: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_to_SelectiveSearchSegmentationStrategyColor(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_to_SelectiveSearchSegmentationStrategyFill(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_to_SelectiveSearchSegmentationStrategyMultiple(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_to_SelectiveSearchSegmentationStrategySize(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_to_SelectiveSearchSegmentationStrategyTexture(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategy_delete(instance: *mut c_void);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyColor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyColor_to_SelectiveSearchSegmentationStrategy(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyColor_delete(instance: *mut c_void);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFill_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFill_to_SelectiveSearchSegmentationStrategy(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFill_delete(instance: *mut c_void);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultiple_addStrategy_PtrLSelectiveSearchSegmentationStrategyG_float(instance: *mut c_void, g: *mut c_void, weight: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultiple_clearStrategies(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultiple_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultiple_to_SelectiveSearchSegmentationStrategy(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultiple_delete(instance: *mut c_void);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategySize_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategySize_to_SelectiveSearchSegmentationStrategy(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategySize_delete(instance: *mut c_void);
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyTexture_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyTexture_to_SelectiveSearchSegmentationStrategy(instance: *mut c_void) -> *mut c_void;
pub fn cv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyTexture_delete(instance: *mut c_void);
