pub fn cv_PtrLcv_detail_BundleAdjusterAffinePartialG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterAffinePartialG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterAffinePartialG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_BundleAdjusterAffinePartialG_to_PtrOfDetail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterAffinePartialG_to_PtrOfDetail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterAffinePartialG_new_const_BundleAdjusterAffinePartial(val: *mut c_void) -> *mut c_void;
