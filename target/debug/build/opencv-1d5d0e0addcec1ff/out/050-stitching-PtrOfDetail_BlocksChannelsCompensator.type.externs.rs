pub fn cv_PtrLcv_detail_BlocksChannelsCompensatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BlocksChannelsCompensatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BlocksChannelsCompensatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_BlocksChannelsCompensatorG_to_PtrOfDetail_BlocksCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BlocksChannelsCompensatorG_to_PtrOfDetail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BlocksChannelsCompensatorG_new_const_BlocksChannelsCompensator(val: *mut c_void) -> *mut c_void;
