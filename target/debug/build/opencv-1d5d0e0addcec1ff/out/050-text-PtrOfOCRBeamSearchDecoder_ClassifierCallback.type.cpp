extern "C" {
	const cv::text::OCRBeamSearchDecoder::ClassifierCallback* cv_PtrLcv_text_OCRBeamSearchDecoder_ClassifierCallbackG_getInnerPtr_const(const cv::Ptr<cv::text::OCRBeamSearchDecoder::ClassifierCallback>* instance) {
			return instance->get();
	}
	
	cv::text::OCRBeamSearchDecoder::ClassifierCallback* cv_PtrLcv_text_OCRBeamSearchDecoder_ClassifierCallbackG_getInnerPtrMut(cv::Ptr<cv::text::OCRBeamSearchDecoder::ClassifierCallback>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_text_OCRBeamSearchDecoder_ClassifierCallbackG_delete(cv::Ptr<cv::text::OCRBeamSearchDecoder::ClassifierCallback>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::text::OCRBeamSearchDecoder::ClassifierCallback>* cv_PtrLcv_text_OCRBeamSearchDecoder_ClassifierCallbackG_new_const_ClassifierCallback(cv::text::OCRBeamSearchDecoder::ClassifierCallback* val) {
			return new cv::Ptr<cv::text::OCRBeamSearchDecoder::ClassifierCallback>(val);
	}
	
}

