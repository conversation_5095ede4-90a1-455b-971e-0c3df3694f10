extern "C" {
	const cv::text::OCRHMMDecoder::ClassifierCallback* cv_PtrLcv_text_OCRHMMDecoder_ClassifierCallbackG_getInnerPtr_const(const cv::Ptr<cv::text::OCRHMMDecoder::ClassifierCallback>* instance) {
			return instance->get();
	}
	
	cv::text::OCRHMMDecoder::ClassifierCallback* cv_PtrLcv_text_OCRHMMDecoder_ClassifierCallbackG_getInnerPtrMut(cv::Ptr<cv::text::OCRHMMDecoder::ClassifierCallback>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_text_OCRHMMDecoder_ClassifierCallbackG_delete(cv::Ptr<cv::text::OCRHMMDecoder::ClassifierCallback>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::text::OCRHMMDecoder::ClassifierCallback>* cv_PtrLcv_text_OCRHMMDecoder_ClassifierCallbackG_new_const_ClassifierCallback(cv::text::OCRHMMDecoder::ClassifierCallback* val) {
			return new cv::Ptr<cv::text::OCRHMMDecoder::ClassifierCallback>(val);
	}
	
}

