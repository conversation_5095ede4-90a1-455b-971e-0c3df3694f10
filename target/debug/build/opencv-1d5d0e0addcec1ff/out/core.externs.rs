pub fn cv_Cho<PERSON>ky_doubleX_size_t_int_doubleX_size_t_int(a: *mut f64, astep: size_t, m: i32, b: *mut f64, bstep: size_t, n: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_Cholesky_floatX_size_t_int_floatX_size_t_int(a: *mut f32, astep: size_t, m: i32, b: *mut f32, bstep: size_t, n: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_LUT_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src: *const c_void, lut: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_LU_doubleX_size_t_int_doubleX_size_t_int(a: *mut f64, astep: size_t, m: i32, b: *mut f64, bstep: size_t, n: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_LU_floatX_size_t_int_floatX_size_t_int(a: *mut f32, astep: size_t, m: i32, b: *mut f32, bstep: size_t, n: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Mahalanobis_const__InputArrayR_const__InputArrayR_const__InputArrayR(v1: *const c_void, v2: *const c_void, icovar: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_PCABackProject_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(data: *const c_void, mean: *const c_void, eigenvectors: *const c_void, result: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PCACompute_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR(data: *const c_void, mean: *const c_void, eigenvectors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PCACompute_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(data: *const c_void, mean: *const c_void, eigenvectors: *const c_void, eigenvalues: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PCACompute_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_double(data: *const c_void, mean: *const c_void, eigenvectors: *const c_void, eigenvalues: *const c_void, retained_variance: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_PCACompute_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_int(data: *const c_void, mean: *const c_void, eigenvectors: *const c_void, eigenvalues: *const c_void, max_components: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_PCACompute_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_double(data: *const c_void, mean: *const c_void, eigenvectors: *const c_void, retained_variance: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_PCACompute_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_int(data: *const c_void, mean: *const c_void, eigenvectors: *const c_void, max_components: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_PCAProject_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(data: *const c_void, mean: *const c_void, eigenvectors: *const c_void, result: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PSNR_const__InputArrayR_const__InputArrayR(src1: *const c_void, src2: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_PSNR_const__InputArrayR_const__InputArrayR_double(src1: *const c_void, src2: *const c_void, r: f64, ocvrs_return: *mut Result<f64>);
pub fn cv_SVBackSubst_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(w: *const c_void, u: *const c_void, vt: *const c_void, rhs: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SVDecomp_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, w: *const c_void, u: *const c_void, vt: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SVDecomp_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int(src: *const c_void, w: *const c_void, u: *const c_void, vt: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_abs_const_MatExprR(e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_abs_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_absdiff_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_addWeighted_const__InputArrayR_double_const__InputArrayR_double_double_const__OutputArrayR(src1: *const c_void, alpha: f64, src2: *const c_void, beta: f64, gamma: f64, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_addWeighted_const__InputArrayR_double_const__InputArrayR_double_double_const__OutputArrayR_int(src1: *const c_void, alpha: f64, src2: *const c_void, beta: f64, gamma: f64, dst: *const c_void, dtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_add_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_add_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR_int(src1: *const c_void, src2: *const c_void, dst: *const c_void, mask: *const c_void, dtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_batchDistance_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dist: *const c_void, dtype: i32, nidx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_batchDistance_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_const__OutputArrayR_int_int_const__InputArrayR_int_bool(src1: *const c_void, src2: *const c_void, dist: *const c_void, dtype: i32, nidx: *const c_void, norm_type: i32, k: i32, mask: *const c_void, update: i32, crosscheck: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_bitwise_and_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bitwise_and_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bitwise_not_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bitwise_not_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bitwise_or_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bitwise_or_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bitwise_xor_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bitwise_xor_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_borderInterpolate_int_int_int(p: i32, len: i32, border_type: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_calcCovarMatrix_const__InputArrayR_const__OutputArrayR_const__InputOutputArrayR_int(samples: *const c_void, covar: *const c_void, mean: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_calcCovarMatrix_const__InputArrayR_const__OutputArrayR_const__InputOutputArrayR_int_int(samples: *const c_void, covar: *const c_void, mean: *const c_void, flags: i32, ctype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cartToPolar_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(x: *const c_void, y: *const c_void, magnitude: *const c_void, angle: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cartToPolar_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_bool(x: *const c_void, y: *const c_void, magnitude: *const c_void, angle: *const c_void, angle_in_degrees: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_checkHardwareSupport_int(feature: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_checkRange_const__InputArrayR(a: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_checkRange_const__InputArrayR_bool_PointX_double_double(a: *const c_void, quiet: bool, pos: *mut core::Point, min_val: f64, max_val: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_compare_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int(src1: *const c_void, src2: *const c_void, dst: *const c_void, cmpop: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_completeSymm_const__InputOutputArrayR(m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_completeSymm_const__InputOutputArrayR_bool(m: *const c_void, lower_to_upper: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_convertFp16_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_convertScaleAbs_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_convertScaleAbs_const__InputArrayR_const__OutputArrayR_double_double(src: *const c_void, dst: *const c_void, alpha: f64, beta: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_copyMakeBorder_const__InputArrayR_const__OutputArrayR_int_int_int_int_int(src: *const c_void, dst: *const c_void, top: i32, bottom: i32, left: i32, right: i32, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_copyMakeBorder_const__InputArrayR_const__OutputArrayR_int_int_int_int_int_const_ScalarR(src: *const c_void, dst: *const c_void, top: i32, bottom: i32, left: i32, right: i32, border_type: i32, value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_copyTo_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_countNonZero_const__InputArrayR(src: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cubeRoot_float(val: f32, ocvrs_return: *mut Result<f32>);
pub fn cv_cuda_createContinuous_int_int_int_const__OutputArrayR(rows: i32, cols: i32, typ: i32, arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_deviceSupports_FeatureSet(feature_set: core::FeatureSet, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_ensureSizeIsEnough_int_int_int_const__OutputArrayR(rows: i32, cols: i32, typ: i32, arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_getCudaEnabledDeviceCount(ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_getDevice(ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_printCudaDeviceInfo_int(device: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_printShortCudaDeviceInfo_int(device: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_registerPageLocked_MatR(m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_resetDevice(ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_setBufferPoolConfig_int_size_t_int(device_id: i32, stack_size: size_t, stack_count: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_setBufferPoolUsage_bool(on: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_setDevice_int(device: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_setGlDevice(ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_setGlDevice_int(device: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_unregisterPageLocked_MatR(m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dct_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dct_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_depthToString_int(depth: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_check_failed_MatChannels_const_int_const_CheckContextR(v: i32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_MatChannels_const_int_const_int_const_CheckContextR(v1: i32, v2: i32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_MatDepth_const_int_const_CheckContextR(v: i32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_MatDepth_const_int_const_int_const_CheckContextR(v1: i32, v2: i32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_MatType_const_int_const_CheckContextR(v: i32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_MatType_const_int_const_int_const_CheckContextR(v1: i32, v2: i32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_Size_LintG_const_CheckContextR(v: *const core::Size_<i32>, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_Size_LintG_const_Size_LintG_const_CheckContextR(v1: *const core::Size_<i32>, v2: *const core::Size_<i32>, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_double_const_CheckContextR(v: f64, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_double_const_double_const_CheckContextR(v1: f64, v2: f64, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_float_const_CheckContextR(v: f32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_float_const_float_const_CheckContextR(v1: f32, v2: f32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_int_const_CheckContextR(v: i32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_int_const_int_const_CheckContextR(v1: i32, v2: i32, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_size_t_const_CheckContextR(v: size_t, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_size_t_const_size_t_const_CheckContextR(v1: size_t, v2: size_t, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_check_failed_auto_const_stringR_const_CheckContextR(v1: *const c_char, ctx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_determinant_const__InputArrayR(mtx: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_dft_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dft_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, flags: i32, nonzero_rows: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_directx_getTypeFromD3DFORMAT_const_int(id_3d_format: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_directx_getTypeFromDXGI_FORMAT_const_int(i_dxgi_format: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_divide_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_divide_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_int(src1: *const c_void, src2: *const c_void, dst: *const c_void, scale: f64, dtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_divide_double_const__InputArrayR_const__OutputArrayR(scale: f64, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_divide_double_const__InputArrayR_const__OutputArrayR_int(scale: f64, src2: *const c_void, dst: *const c_void, dtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_eigenNonSymmetric_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, eigenvalues: *const c_void, eigenvectors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_eigen_const__InputArrayR_const__OutputArrayR(src: *const c_void, eigenvalues: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_eigen_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, eigenvalues: *const c_void, eigenvectors: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_error_const_ExceptionR(exc: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_error_int_const_StringR_const_charX_const_charX_int(_code: i32, _err: *const c_char, _func: *const c_char, _file: *const c_char, _line: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_exp_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_extractChannel_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, coi: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastAtan2_float_float(y: f32, x: f32, ocvrs_return: *mut Result<f32>);
pub fn cv_findNonZero_const__InputArrayR_const__OutputArrayR(src: *const c_void, idx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_flip_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, flip_code: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_gemm_const__InputArrayR_const__InputArrayR_double_const__InputArrayR_double_const__OutputArrayR(src1: *const c_void, src2: *const c_void, alpha: f64, src3: *const c_void, beta: f64, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_gemm_const__InputArrayR_const__InputArrayR_double_const__InputArrayR_double_const__OutputArrayR_int(src1: *const c_void, src2: *const c_void, alpha: f64, src3: *const c_void, beta: f64, dst: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_getBuildInformation(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getCPUFeaturesLine(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getCPUTickCount(ocvrs_return: *mut Result<i64>);
pub fn cv_getElemSize_int(typ: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_getHardwareFeatureName_int(feature: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getLogLevel(ocvrs_return: *mut Result<i32>);
pub fn cv_getNumThreads(ocvrs_return: *mut Result<i32>);
pub fn cv_getNumberOfCPUs(ocvrs_return: *mut Result<i32>);
pub fn cv_getOptimalDFTSize_int(vecsize: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_getThreadNum(ocvrs_return: *mut Result<i32>);
pub fn cv_getTickCount(ocvrs_return: *mut Result<i64>);
pub fn cv_getTickFrequency(ocvrs_return: *mut Result<f64>);
pub fn cv_getVersionMajor() -> i32;
pub fn cv_getVersionMinor() -> i32;
pub fn cv_getVersionRevision() -> i32;
pub fn cv_getVersionString(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_glob_String_vectorLStringGR(pattern: *const c_char, result: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_glob_String_vectorLStringGR_bool(pattern: *const c_char, result: *mut c_void, recursive: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_haveOpenVX(ocvrs_return: *mut Result<bool>);
pub fn cv_hconcat_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hconcat_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_idct_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_idct_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_idft_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_idft_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, flags: i32, nonzero_rows: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_inRange_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src: *const c_void, lowerb: *const c_void, upperb: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_insertChannel_const__InputArrayR_const__InputOutputArrayR_int(src: *const c_void, dst: *const c_void, coi: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_instr_getFlags(ocvrs_return: *mut Result<core::FLAGS>);
pub fn cv_instr_resetTrace(ocvrs_return: *mut ResultVoid);
pub fn cv_instr_setFlags_FLAGS(mode_flags: core::FLAGS, ocvrs_return: *mut ResultVoid);
pub fn cv_instr_setUseInstrumentation_bool(flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_instr_useInstrumentation(ocvrs_return: *mut Result<bool>);
pub fn cv_invert_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_invert_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, flags: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_ipp_getIppErrorLocation(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ipp_getIppFeatures(ocvrs_return: *mut Result<u64>);
pub fn cv_ipp_getIppStatus(ocvrs_return: *mut Result<i32>);
pub fn cv_ipp_getIppVersion(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ipp_setIppStatus_int(status: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ipp_setIppStatus_int_const_charX_const_charX_int(status: i32, funcname: *const c_char, filename: *const c_char, line: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ipp_setUseIPP_NotExact_bool(flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ipp_setUseIPP_bool(flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ipp_useIPP(ocvrs_return: *mut Result<bool>);
pub fn cv_ipp_useIPP_NotExact(ocvrs_return: *mut Result<bool>);
pub fn cv_kmeans_const__InputArrayR_int_const__InputOutputArrayR_TermCriteria_int_int(data: *const c_void, k: i32, best_labels: *const c_void, criteria: *const core::TermCriteria, attempts: i32, flags: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_kmeans_const__InputArrayR_int_const__InputOutputArrayR_TermCriteria_int_int_const__OutputArrayR(data: *const c_void, k: i32, best_labels: *const c_void, criteria: *const core::TermCriteria, attempts: i32, flags: i32, centers: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_log_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_magnitude_const__InputArrayR_const__InputArrayR_const__OutputArrayR(x: *const c_void, y: *const c_void, magnitude: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_max_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_max_const_MatR_const_MatR_MatR(src1: *const c_void, src2: *const c_void, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_max_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_max_const_UMatR_const_UMatR_UMatR(src1: *const c_void, src2: *const c_void, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_max_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_max_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_meanStdDev_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, mean: *const c_void, stddev: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_meanStdDev_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__InputArrayR(src: *const c_void, mean: *const c_void, stddev: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_mean_const__InputArrayR(src: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_mean_const__InputArrayR_const__InputArrayR(src: *const c_void, mask: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_merge_const__InputArrayR_const__OutputArrayR(mv: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_minMaxIdx_const__InputArrayR_doubleX(src: *const c_void, min_val: *mut f64, ocvrs_return: *mut ResultVoid);
pub fn cv_minMaxIdx_const__InputArrayR_doubleX_doubleX_intX_intX_const__InputArrayR(src: *const c_void, min_val: *mut f64, max_val: *mut f64, min_idx: *mut i32, max_idx: *mut i32, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_minMaxLoc_const_SparseMatR_doubleX_doubleX(a: *const c_void, min_val: *mut f64, max_val: *mut f64, ocvrs_return: *mut ResultVoid);
pub fn cv_minMaxLoc_const_SparseMatR_doubleX_doubleX_intX_intX(a: *const c_void, min_val: *mut f64, max_val: *mut f64, min_idx: *mut i32, max_idx: *mut i32, ocvrs_return: *mut ResultVoid);
pub fn cv_minMaxLoc_const__InputArrayR_doubleX(src: *const c_void, min_val: *mut f64, ocvrs_return: *mut ResultVoid);
pub fn cv_minMaxLoc_const__InputArrayR_doubleX_doubleX_PointX_PointX_const__InputArrayR(src: *const c_void, min_val: *mut f64, max_val: *mut f64, min_loc: *mut core::Point, max_loc: *mut core::Point, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_min_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_min_const_MatR_const_MatR_MatR(src1: *const c_void, src2: *const c_void, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_min_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_min_const_UMatR_const_UMatR_UMatR(src1: *const c_void, src2: *const c_void, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_min_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_min_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mixChannels_const__InputArrayR_const__InputOutputArrayR_const_intX_size_t(src: *const c_void, dst: *const c_void, from_to: *const i32, npairs: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_mixChannels_const__InputArrayR_const__InputOutputArrayR_const_vectorLintGR(src: *const c_void, dst: *const c_void, from_to: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_mulSpectrums_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int(a: *const c_void, b: *const c_void, c: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_mulSpectrums_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_bool(a: *const c_void, b: *const c_void, c: *const c_void, flags: i32, conj_b: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_mulTransposed_const__InputArrayR_const__OutputArrayR_bool(src: *const c_void, dst: *const c_void, a_ta: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_mulTransposed_const__InputArrayR_const__OutputArrayR_bool_const__InputArrayR_double_int(src: *const c_void, dst: *const c_void, a_ta: bool, delta: *const c_void, scale: f64, dtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_multiply_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_multiply_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_int(src1: *const c_void, src2: *const c_void, dst: *const c_void, scale: f64, dtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_noArray() -> *mut c_void;
pub fn cv_norm_const_SparseMatR_int(src: *const c_void, norm_type: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_norm_const__InputArrayR(src1: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_norm_const__InputArrayR_const__InputArrayR(src1: *const c_void, src2: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_norm_const__InputArrayR_const__InputArrayR_int_const__InputArrayR(src1: *const c_void, src2: *const c_void, norm_type: i32, mask: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_norm_const__InputArrayR_int_const__InputArrayR(src1: *const c_void, norm_type: i32, mask: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_normalize_const_SparseMatR_SparseMatR_double_int(src: *const c_void, dst: *mut c_void, alpha: f64, norm_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_normalize_const__InputArrayR_const__InputOutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_normalize_const__InputArrayR_const__InputOutputArrayR_double_double_int_int_const__InputArrayR(src: *const c_void, dst: *const c_void, alpha: f64, beta: f64, norm_type: i32, dtype: i32, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_attachContext_const_StringR_voidX_voidX_voidX(platform_name: *const c_char, platform_id: *mut c_void, context: *mut c_void, device_id: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_buildOptionsAddMatrixDescription_StringR_const_StringR_const__InputArrayR(build_options: *mut *mut c_void, name: *const c_char, _m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_checkOptimalVectorWidth_const_intX_const__InputArrayR(vector_widths: *const i32, src1: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_checkOptimalVectorWidth_const_intX_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_OclVectorStrategy(vector_widths: *const i32, src1: *const c_void, src2: *const c_void, src3: *const c_void, src4: *const c_void, src5: *const c_void, src6: *const c_void, src7: *const c_void, src8: *const c_void, src9: *const c_void, strat: core::OclVectorStrategy, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_convertFromBuffer_voidX_size_t_int_int_int_UMatR(cl_mem_buffer: *mut c_void, step: size_t, rows: i32, cols: i32, typ: i32, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_convertFromImage_voidX_UMatR(cl_mem_image: *mut c_void, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_convertTypeStr_int_int_int_charX(sdepth: i32, ddepth: i32, cn: i32, buf: *mut *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_finish(ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_getOpenCLErrorString_int(error_code: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_getPlatfomsInfo_vectorLPlatformInfoGR(platform_info: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_haveAmdBlas(ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_haveAmdFft(ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_haveOpenCL(ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_haveSVM(ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_kernelToStr_const__InputArrayR(_kernel: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_kernelToStr_const__InputArrayR_int_const_charX(_kernel: *const c_void, ddepth: i32, name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_memopTypeToStr_int(t: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_predictOptimalVectorWidthMax_const__InputArrayR(src1: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_predictOptimalVectorWidthMax_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(src1: *const c_void, src2: *const c_void, src3: *const c_void, src4: *const c_void, src5: *const c_void, src6: *const c_void, src7: *const c_void, src8: *const c_void, src9: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_predictOptimalVectorWidth_const__InputArrayR(src1: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_predictOptimalVectorWidth_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_OclVectorStrategy(src1: *const c_void, src2: *const c_void, src3: *const c_void, src4: *const c_void, src5: *const c_void, src6: *const c_void, src7: *const c_void, src8: *const c_void, src9: *const c_void, strat: core::OclVectorStrategy, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_setUseOpenCL_bool(flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_typeToStr_int(t: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_useOpenCL(ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_vecopTypeToStr_int(t: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_convertFromGLTexture2D_const_Texture2DR_const__OutputArrayR(texture: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_convertToGLTexture2D_const__InputArrayR_Texture2DR(src: *const c_void, texture: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_mapGLBuffer_const_BufferR(buffer: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_mapGLBuffer_const_BufferR_AccessFlag(buffer: *const c_void, access_flags: core::AccessFlag, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_ocl_initializeContextFromGL(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_render_const_ArraysR(arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_render_const_ArraysR_const__InputArrayR(arr: *const c_void, indices: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_render_const_ArraysR_const__InputArrayR_int_Scalar(arr: *const c_void, indices: *const c_void, mode: i32, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_render_const_ArraysR_int_Scalar(arr: *const c_void, mode: i32, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_render_const_Texture2DR(tex: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_render_const_Texture2DR_Rect_LdoubleG_Rect_LdoubleG(tex: *const c_void, wnd_rect: *const core::Rect_<f64>, tex_rect: *const core::Rect_<f64>, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_unmapGLBuffer_UMatR(u: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_operatorA_const_MatExprR_const_MatExprR(e1: *const c_void, e2: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorA_const_MatExprR_const_MatR(e: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorA_const_MatExprR_const_ScalarR(e: *const c_void, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorA_const_MatR_const_MatExprR(m: *const c_void, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorA_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorA_const_MatR_const_ScalarR(a: *const c_void, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorA_const_ScalarR_const_MatExprR(s: *const core::Scalar, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorA_const_ScalarR_const_MatR(s: *const core::Scalar, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorD_const_MatExprR_const_MatExprR(e1: *const c_void, e2: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorD_const_MatExprR_const_MatR(e: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorD_const_MatExprR_double(e: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorD_const_MatR_const_MatExprR(m: *const c_void, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorD_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorD_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorD_double_const_MatExprR(s: f64, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorD_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorEQ_const_FileNodeIteratorR_const_FileNodeIteratorR(it1: *const c_void, it2: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_operatorEQ_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorEQ_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorEQ_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorGE_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorGE_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorGE_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorG_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorG_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorG_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorLE_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorLE_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorLE_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorL_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorL_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorL_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorNE_const_FileNodeIteratorR_const_FileNodeIteratorR(it1: *const c_void, it2: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_operatorNE_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorNE_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorNE_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorNOTB_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorOR_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorOR_const_MatR_const_ScalarR(a: *const c_void, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorOR_const_ScalarR_const_MatR(s: *const core::Scalar, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorR_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorR_const_MatR_const_ScalarR(a: *const c_void, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorR_const_ScalarR_const_MatR(s: *const core::Scalar, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_MatExprR(e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_MatExprR_const_MatExprR(e1: *const c_void, e2: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_MatExprR_const_MatR(e: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_MatExprR_const_ScalarR(e: *const c_void, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_MatR_const_MatExprR(m: *const c_void, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_MatR_const_ScalarR(a: *const c_void, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_ScalarR_const_MatExprR(s: *const core::Scalar, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorS_const_ScalarR_const_MatR(s: *const core::Scalar, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorXOR_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorXOR_const_MatR_const_ScalarR(a: *const c_void, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorXOR_const_ScalarR_const_MatR(s: *const core::Scalar, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorX_const_MatExprR_const_MatExprR(e1: *const c_void, e2: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorX_const_MatExprR_const_MatR(e: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorX_const_MatExprR_double(e: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorX_const_MatR_const_MatExprR(m: *const c_void, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorX_const_MatR_const_MatR(a: *const c_void, b: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorX_const_MatR_double(a: *const c_void, s: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorX_double_const_MatExprR(s: f64, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_operatorX_double_const_MatR(s: f64, a: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_parallel_for__const_RangeR_const_ParallelLoopBodyR(range: *const c_void, body: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_parallel_for__const_RangeR_const_ParallelLoopBodyR_double(range: *const c_void, body: *const c_void, nstripes: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_patchNaNs_const__InputOutputArrayR(a: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_patchNaNs_const__InputOutputArrayR_double(a: *const c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_perspectiveTransform_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_phase_const__InputArrayR_const__InputArrayR_const__OutputArrayR(x: *const c_void, y: *const c_void, angle: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_phase_const__InputArrayR_const__InputArrayR_const__OutputArrayR_bool(x: *const c_void, y: *const c_void, angle: *const c_void, angle_in_degrees: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_polarToCart_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(magnitude: *const c_void, angle: *const c_void, x: *const c_void, y: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_polarToCart_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_bool(magnitude: *const c_void, angle: *const c_void, x: *const c_void, y: *const c_void, angle_in_degrees: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_pow_const__InputArrayR_double_const__OutputArrayR(src: *const c_void, power: f64, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_randShuffle_const__InputOutputArrayR(dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_randShuffle_const__InputOutputArrayR_double_RNGX(dst: *const c_void, iter_factor: f64, rng: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_randn_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR(dst: *const c_void, mean: *const c_void, stddev: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_randu_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR(dst: *const c_void, low: *const c_void, high: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_DMatchR_const_DMatchR(node: *const c_void, value: *mut core::DMatch, default_value: *const core::DMatch, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_KeyPointR_const_KeyPointR(node: *const c_void, value: *mut c_void, default_value: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_MatR(node: *const c_void, mat: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_MatR_const_MatR(node: *const c_void, mat: *mut c_void, default_mat: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_SparseMatR(node: *const c_void, mat: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_SparseMatR_const_SparseMatR(node: *const c_void, mat: *mut c_void, default_mat: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_doubleR_double(node: *const c_void, value: *mut f64, default_value: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_floatR_float(node: *const c_void, value: *mut f32, default_value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_intR_int(node: *const c_void, value: *mut i32, default_value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_stringR_const_stringR(node: *const c_void, value: *mut *mut c_void, default_value: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_vectorLDMatchGR(node: *const c_void, matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_read_const_FileNodeR_vectorLKeyPointGR(node: *const c_void, keypoints: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_reduce_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, dim: i32, rtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_reduce_const__InputArrayR_const__OutputArrayR_int_int_int(src: *const c_void, dst: *const c_void, dim: i32, rtype: i32, dtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_repeat_const_MatR_int_int(src: *const c_void, ny: i32, nx: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_repeat_const__InputArrayR_int_int_const__OutputArrayR(src: *const c_void, ny: i32, nx: i32, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rotate_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, rotate_code: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_samples_addSamplesDataSearchPath_const_StringR(path: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_samples_addSamplesDataSearchSubDirectory_const_StringR(subdir: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_samples_findFileOrKeep_const_StringR(relative_path: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_samples_findFileOrKeep_const_StringR_bool(relative_path: *const c_char, silent_mode: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_samples_findFile_const_StringR(relative_path: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_samples_findFile_const_StringR_bool_bool(relative_path: *const c_char, required: bool, silent_mode: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_scaleAdd_const__InputArrayR_double_const__InputArrayR_const__OutputArrayR(src1: *const c_void, alpha: f64, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_setBreakOnError_bool(flag: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_setIdentity_const__InputOutputArrayR(mtx: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_setIdentity_const__InputOutputArrayR_const_ScalarR(mtx: *const c_void, s: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_setLogLevel_int(level: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_setNumThreads_int(nthreads: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_setRNGSeed_int(seed: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_setUseOpenVX_bool(flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_setUseOptimized_bool(onoff: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_solveCubic_const__InputArrayR_const__OutputArrayR(coeffs: *const c_void, roots: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_solveLP_const__InputArrayR_const__InputArrayR_const__OutputArrayR(func: *const c_void, constr: *const c_void, z: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_solvePoly_const__InputArrayR_const__OutputArrayR(coeffs: *const c_void, roots: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_solvePoly_const__InputArrayR_const__OutputArrayR_int(coeffs: *const c_void, roots: *const c_void, max_iters: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_solve_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_solve_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int(src1: *const c_void, src2: *const c_void, dst: *const c_void, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_sortIdx_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_sort_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_split_const_MatR_MatX(src: *const c_void, mvbegin: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_split_const__InputArrayR_const__OutputArrayR(m: *const c_void, mv: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_sqrt_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_subtract_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_subtract_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR_int(src1: *const c_void, src2: *const c_void, dst: *const c_void, mask: *const c_void, dtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_sum_const__InputArrayR(src: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_swap_MatR_MatR(a: *mut c_void, b: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_swap_UMatR_UMatR(a: *mut c_void, b: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_tempfile(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_tempfile_const_charX(suffix: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_theRNG(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_trace_const__InputArrayR(mtx: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_transform_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_transpose_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_typeToString_int(typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_useOpenVX(ocvrs_return: *mut Result<bool>);
pub fn cv_useOptimized(ocvrs_return: *mut Result<bool>);
pub fn cv_utils_dumpBool_bool(argument: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpCString_const_charX(argument: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpDouble_double(argument: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpFloat_float(argument: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpInputArrayOfArrays_const__InputArrayR(argument: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpInputArray_const__InputArrayR(argument: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpInputOutputArrayOfArrays_const__InputOutputArrayR(argument: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpInputOutputArray_const__InputOutputArrayR(argument: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpInt_int(argument: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpRange_const_RangeR(argument: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpRect_const_RectR(argument: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpRotatedRect_const_RotatedRectR(argument: *const core::RotatedRect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpSizeT_size_t(argument: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpString_const_StringR(argument: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpTermCriteria_const_TermCriteriaR(argument: *const core::TermCriteria, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpVectorOfDouble_const_vectorLdoubleGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpVectorOfInt_const_vectorLintGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_dumpVectorOfRect_const_vectorLRectGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_fs_getCacheDirectoryForDownloads(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_generateVectorOfInt_size_t_vectorLintGR(len: size_t, vec: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_utils_generateVectorOfMat_size_t_int_int_int_vectorLMatGR(len: size_t, rows: i32, cols: i32, dtype: i32, vec: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_utils_generateVectorOfRect_size_t_vectorLRectGR(len: size_t, vec: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_utils_getThreadID(ocvrs_return: *mut Result<i32>);
pub fn cv_utils_logging_getLogLevel(ocvrs_return: *mut Result<core::LogLevel>);
pub fn cv_utils_logging_getLogTagLevel_const_charX(tag: *const c_char, ocvrs_return: *mut Result<core::LogLevel>);
pub fn cv_utils_logging_internal_getGlobalLogTag(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_logging_internal_writeLogMessageEx_LogLevel_const_charX_const_charX_int_const_charX_const_charX(log_level: core::LogLevel, tag: *const c_char, file: *const c_char, line: i32, func: *const c_char, message: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_utils_logging_internal_writeLogMessage_LogLevel_const_charX(log_level: core::LogLevel, message: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_utils_logging_registerLogTag_LogTagX(plogtag: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_utils_logging_setLogLevel_LogLevel(log_level: core::LogLevel, ocvrs_return: *mut Result<core::LogLevel>);
pub fn cv_utils_logging_setLogTagLevel_const_charX_LogLevel(tag: *const c_char, level: core::LogLevel, ocvrs_return: *mut ResultVoid);
pub fn cv_utils_testAsyncArray_const__InputArrayR(argument: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_testAsyncException(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_testOverloadResolution_const_RectR(rect: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_testOverloadResolution_int(value: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_testOverloadResolution_int_const_PointR(value: i32, point: *const core::Point, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_testOverwriteNativeMethod_int(argument: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_utils_testRaiseGeneralException(ocvrs_return: *mut ResultVoid);
pub fn cv_utils_testReservedKeywordConversion_int(positional_argument: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_testReservedKeywordConversion_int_int_int(positional_argument: i32, lambda: i32, from: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_va_intel_convertFromVASurface_VADisplay_VASurfaceID_Size_const__OutputArrayR(display: *mut c_void, surface: core::va_surface_id, size: *const core::Size, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_va_intel_convertToVASurface_VADisplay_const__InputArrayR_VASurfaceID_Size(display: *mut c_void, src: *const c_void, surface: core::va_surface_id, size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_va_intel_ocl_initializeContextFromVA_VADisplay(display: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_va_intel_ocl_initializeContextFromVA_VADisplay_bool(display: *mut c_void, try_interop: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_vconcat_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_vconcat_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_writeScalar_FileStorageR_const_StringR(fs: *mut c_void, value: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_writeScalar_FileStorageR_double(fs: *mut c_void, value: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_writeScalar_FileStorageR_float(fs: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_writeScalar_FileStorageR_int(fs: *mut c_void, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_const_MatR(fs: *mut c_void, name: *const c_char, value: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_const_SparseMatR(fs: *mut c_void, name: *const c_char, value: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_const_StringR(fs: *mut c_void, name: *const c_char, value: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_const_vectorLDMatchGR(fs: *mut c_void, name: *const c_char, value: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_const_vectorLKeyPointGR(fs: *mut c_void, name: *const c_char, value: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_double(fs: *mut c_void, name: *const c_char, value: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_float(fs: *mut c_void, name: *const c_char, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_int(fs: *mut c_void, name: *const c_char, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Algorithm_Algorithm(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Algorithm_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Algorithm_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Algorithm_write_const_const_PtrLFileStorageGR_const_StringR(instance: *const c_void, fs: *const c_void, name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_Algorithm_write_const_const_PtrLFileStorageGR(instance: *const c_void, fs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Algorithm_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Algorithm_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_Algorithm_save_const_const_StringR(instance: *const c_void, filename: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_Algorithm_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Algorithm_to_ConjGradSolver(instance: *mut c_void) -> *mut c_void;
pub fn cv_Algorithm_to_DownhillSolver(instance: *mut c_void) -> *mut c_void;
pub fn cv_Algorithm_to_MinProblemSolver(instance: *mut c_void) -> *mut c_void;
pub fn cv_Algorithm_delete(instance: *mut c_void);
pub fn cv_AsyncArray_AsyncArray() -> *mut c_void;
pub fn cv_AsyncArray_AsyncArray_const_AsyncArrayR(o: *const c_void) -> *mut c_void;
pub fn cv_AsyncArray_operatorST_const_AsyncArrayR(instance: *mut c_void, o: *const c_void);
pub fn cv_AsyncArray_release(instance: *mut c_void);
pub fn cv_AsyncArray_get_const_const__OutputArrayR(instance: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AsyncArray_get_const_const__OutputArrayR_int64_t(instance: *const c_void, dst: *const c_void, timeout_ns: i64, ocvrs_return: *mut Result<bool>);
pub fn cv_AsyncArray_get_const_const__OutputArrayR_double(instance: *const c_void, dst: *const c_void, timeout_ns: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_AsyncArray_wait_for_const_int64_t(instance: *const c_void, timeout_ns: i64, ocvrs_return: *mut Result<bool>);
pub fn cv_AsyncArray_wait_for_const_double(instance: *const c_void, timeout_ns: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_AsyncArray_valid_const(instance: *const c_void) -> bool;
pub fn cv_AsyncArray_AsyncArray_AsyncArrayRR(o: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AsyncArray_operatorST_AsyncArrayRR(instance: *mut c_void, o: *mut c_void);
pub fn cv_AsyncArray_delete(instance: *mut c_void);
pub fn cv_AsyncPromise_AsyncPromise() -> *mut c_void;
pub fn cv_AsyncPromise_AsyncPromise_const_AsyncPromiseR(o: *const c_void) -> *mut c_void;
pub fn cv_AsyncPromise_operatorST_const_AsyncPromiseR(instance: *mut c_void, o: *const c_void);
pub fn cv_AsyncPromise_release(instance: *mut c_void);
pub fn cv_AsyncPromise_getArrayResult(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AsyncPromise_setValue_const__InputArrayR(instance: *mut c_void, value: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AsyncPromise_setException_const_ExceptionR(instance: *mut c_void, exception: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AsyncPromise_AsyncPromise_AsyncPromiseRR(o: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AsyncPromise_operatorST_AsyncPromiseRR(instance: *mut c_void, o: *mut c_void);
pub fn cv_AsyncPromise__getImpl_const(instance: *const c_void) -> *mut c_void;
pub fn cv_AsyncPromise_delete(instance: *mut c_void);
pub fn cv_CommandLineParser_CommandLineParser_int_const_charXX_const_StringR(argc: i32, argv: *const *const c_char, keys: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CommandLineParser_CommandLineParser_const_CommandLineParserR(parser: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CommandLineParser_operatorST_const_CommandLineParserR(instance: *mut c_void, parser: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CommandLineParser_getPathToApplication_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CommandLineParser_get_bool_const_const_StringR_bool(instance: *const c_void, name: *const c_char, space_delete: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_CommandLineParser_get_bool_const_const_StringR(instance: *const c_void, name: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_CommandLineParser_get_int_const_const_StringR_bool(instance: *const c_void, name: *const c_char, space_delete: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_CommandLineParser_get_int_const_const_StringR(instance: *const c_void, name: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_CommandLineParser_get_double_const_const_StringR_bool(instance: *const c_void, name: *const c_char, space_delete: bool, ocvrs_return: *mut Result<f64>);
pub fn cv_CommandLineParser_get_double_const_const_StringR(instance: *const c_void, name: *const c_char, ocvrs_return: *mut Result<f64>);
pub fn cv_CommandLineParser_get_cv_String_const_const_StringR_bool(instance: *const c_void, name: *const c_char, space_delete: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CommandLineParser_get_cv_String_const_const_StringR(instance: *const c_void, name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CommandLineParser_get_uint64_t_const_const_StringR_bool(instance: *const c_void, name: *const c_char, space_delete: bool, ocvrs_return: *mut Result<u64>);
pub fn cv_CommandLineParser_get_uint64_t_const_const_StringR(instance: *const c_void, name: *const c_char, ocvrs_return: *mut Result<u64>);
pub fn cv_CommandLineParser_get_bool_const_int_bool(instance: *const c_void, index: i32, space_delete: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_CommandLineParser_get_bool_const_int(instance: *const c_void, index: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_CommandLineParser_get_int_const_int_bool(instance: *const c_void, index: i32, space_delete: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_CommandLineParser_get_int_const_int(instance: *const c_void, index: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_CommandLineParser_get_double_const_int_bool(instance: *const c_void, index: i32, space_delete: bool, ocvrs_return: *mut Result<f64>);
pub fn cv_CommandLineParser_get_double_const_int(instance: *const c_void, index: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_CommandLineParser_get_cv_String_const_int_bool(instance: *const c_void, index: i32, space_delete: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CommandLineParser_get_cv_String_const_int(instance: *const c_void, index: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CommandLineParser_get_uint64_t_const_int_bool(instance: *const c_void, index: i32, space_delete: bool, ocvrs_return: *mut Result<u64>);
pub fn cv_CommandLineParser_get_uint64_t_const_int(instance: *const c_void, index: i32, ocvrs_return: *mut Result<u64>);
pub fn cv_CommandLineParser_has_const_const_StringR(instance: *const c_void, name: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_CommandLineParser_check_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_CommandLineParser_about_const_StringR(instance: *mut c_void, message: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_CommandLineParser_printMessage_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CommandLineParser_printErrors_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CommandLineParser_delete(instance: *mut c_void);
pub fn cv_ConjGradSolver_create_const_PtrLFunctionGR_TermCriteria(f: *const c_void, termcrit: *const core::TermCriteria, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ConjGradSolver_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ConjGradSolver_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ConjGradSolver_to_MinProblemSolver(instance: *mut c_void) -> *mut c_void;
pub fn cv_ConjGradSolver_delete(instance: *mut c_void);
pub fn cv_DMatch_DMatch(ocvrs_return: *mut Result<core::DMatch>);
pub fn cv_DMatch_DMatch_int_int_float(_query_idx: i32, _train_idx: i32, _distance: f32, ocvrs_return: *mut Result<core::DMatch>);
pub fn cv_DMatch_DMatch_int_int_int_float(_query_idx: i32, _train_idx: i32, _img_idx: i32, _distance: f32, ocvrs_return: *mut Result<core::DMatch>);
pub fn cv_DMatch_operatorL_const_const_DMatchR(instance: *const core::DMatch, m: *const core::DMatch, ocvrs_return: *mut Result<bool>);
pub fn cv_DownhillSolver_getInitStep_const_const__OutputArrayR(instance: *const c_void, step: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DownhillSolver_setInitStep_const__InputArrayR(instance: *mut c_void, step: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DownhillSolver_create_const_PtrLFunctionGR_const__InputArrayR_TermCriteria(f: *const c_void, init_step: *const c_void, termcrit: *const core::TermCriteria, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DownhillSolver_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DownhillSolver_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_DownhillSolver_to_MinProblemSolver(instance: *mut c_void) -> *mut c_void;
pub fn cv_DownhillSolver_delete(instance: *mut c_void);
pub fn cv_Exception_Exception(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Exception_Exception_int_const_StringR_const_StringR_const_StringR_int(_code: i32, _err: *const c_char, _func: *const c_char, _file: *const c_char, _line: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Exception_what_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Exception_formatMessage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Exception_propMsg_const(instance: *const c_void) -> *mut c_void;
pub fn cv_Exception_propMsg_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_Exception_propCode_const(instance: *const c_void) -> i32;
pub fn cv_Exception_propCode_const_int(instance: *mut c_void, val: i32);
pub fn cv_Exception_propErr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_Exception_propErr_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_Exception_propFunc_const(instance: *const c_void) -> *mut c_void;
pub fn cv_Exception_propFunc_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_Exception_propFile_const(instance: *const c_void) -> *mut c_void;
pub fn cv_Exception_propFile_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_Exception_propLine_const(instance: *const c_void) -> i32;
pub fn cv_Exception_propLine_const_int(instance: *mut c_void, val: i32);
pub fn cv_Exception_delete(instance: *mut c_void);
pub fn cv_FileNode_FileNode(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_FileNode_const_FileStorageX_size_t_size_t(fs: *const c_void, block_idx: size_t, ofs: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_FileNode_const_FileNodeR(node: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_operatorST_const_FileNodeR(instance: *mut c_void, node: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FileNode_operator___const_const_StringR(instance: *const c_void, nodename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_operator___const_const_charX(instance: *const c_void, nodename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_operator___const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_keys_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FileNode_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isNone_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isSeq_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isMap_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isInt_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isReal_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isString_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isNamed_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_name_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_size_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_FileNode_rawSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_FileNode_operator_int_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FileNode_operator_float_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_FileNode_operator_double_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_FileNode_operator_std_string_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_isMap_int(flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isSeq_int(flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isCollection_int(flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isEmptyCollection_int(flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_isFlow_int(flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNode_ptr(instance: *mut c_void, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_FileNode_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*const u8>);
pub fn cv_FileNode_begin_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_end_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_readRaw_const_const_StringR_voidX_size_t(instance: *const c_void, fmt: *const c_char, vec: *mut c_void, len: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_FileNode_setValue_int_const_voidX_int(instance: *mut c_void, typ: i32, value: *const c_void, len: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FileNode_setValue_int_const_voidX(instance: *mut c_void, typ: i32, value: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FileNode_real_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_FileNode_string_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_mat_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNode_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_FileNode_propBlockIdx_const(instance: *const c_void) -> size_t;
pub fn cv_FileNode_propBlockIdx_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_FileNode_propOfs_const(instance: *const c_void) -> size_t;
pub fn cv_FileNode_propOfs_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_FileNode_delete(instance: *mut c_void);
pub fn cv_FileNodeIterator_FileNodeIterator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNodeIterator_FileNodeIterator_const_FileNodeR_bool(node: *const c_void, seek_end: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNodeIterator_FileNodeIterator_const_FileNodeIteratorR(it: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNodeIterator_operatorST_const_FileNodeIteratorR(instance: *mut c_void, it: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FileNodeIterator_operatorX_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNodeIterator_operatorAA(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNodeIterator_readRaw_const_StringR_voidX_size_t(instance: *mut c_void, fmt: *const c_char, vec: *mut c_void, len: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNodeIterator_readRaw_const_StringR_voidX(instance: *mut c_void, fmt: *const c_char, vec: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileNodeIterator_remaining_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_FileNodeIterator_equalTo_const_const_FileNodeIteratorR(instance: *const c_void, it: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileNodeIterator_delete(instance: *mut c_void);
pub fn cv_FileStorage_FileStorage(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_FileStorage_const_StringR_int_const_StringR(filename: *const c_char, flags: i32, encoding: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_FileStorage_const_StringR_int(filename: *const c_char, flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_open_const_StringR_int_const_StringR(instance: *mut c_void, filename: *const c_char, flags: i32, encoding: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_FileStorage_open_const_StringR_int(instance: *mut c_void, filename: *const c_char, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_FileStorage_isOpened_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FileStorage_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_releaseAndGetString(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_getFirstTopLevelNode_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_root_const_int(instance: *const c_void, streamidx: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_root_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_operator___const_const_StringR(instance: *const c_void, nodename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_operator___const_const_charX(instance: *const c_void, nodename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_write_const_StringR_int(instance: *mut c_void, name: *const c_char, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_write_const_StringR_double(instance: *mut c_void, name: *const c_char, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_write_const_StringR_const_StringR(instance: *mut c_void, name: *const c_char, val: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_write_const_StringR_const_MatR(instance: *mut c_void, name: *const c_char, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_write_const_StringR_const_vectorLStringGR(instance: *mut c_void, name: *const c_char, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_writeRaw_const_StringR_const_voidX_size_t(instance: *mut c_void, fmt: *const c_char, vec: *const c_void, len: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_writeComment_const_StringR_bool(instance: *mut c_void, comment: *const c_char, append: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_writeComment_const_StringR(instance: *mut c_void, comment: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_startWriteStruct_const_StringR_int_const_StringR(instance: *mut c_void, name: *const c_char, flags: i32, type_name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_startWriteStruct_const_StringR_int(instance: *mut c_void, name: *const c_char, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_endWriteStruct(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FileStorage_getDefaultObjectName_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FileStorage_getFormat_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FileStorage_propState_const(instance: *const c_void) -> i32;
pub fn cv_FileStorage_propState_const_int(instance: *mut c_void, val: i32);
pub fn cv_FileStorage_propElname_const(instance: *const c_void) -> *mut c_void;
pub fn cv_FileStorage_propElname_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_FileStorage_delete(instance: *mut c_void);
pub fn cv_Formatted_next(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Formatted_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatted_delete(instance: *mut c_void);
pub fn cv_Formatter_format_const_const_MatR(instance: *const c_void, mtx: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Formatter_set16fPrecision_int(instance: *mut c_void, p: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatter_set16fPrecision(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatter_set32fPrecision_int(instance: *mut c_void, p: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatter_set32fPrecision(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatter_set64fPrecision_int(instance: *mut c_void, p: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatter_set64fPrecision(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatter_setMultiline_bool(instance: *mut c_void, ml: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatter_setMultiline(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Formatter_get_FormatType(fmt: core::Formatter_FormatType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Formatter_get(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Formatter_delete(instance: *mut c_void);
pub fn cv_Hamming_operator___const_const_unsigned_charX_const_unsigned_charX_int(instance: *const c_void, a: *const u8, b: *const u8, size: i32, ocvrs_return: *mut Result<core::Hamming_result_type>);
pub fn cv_Hamming_delete(instance: *mut c_void);
pub fn cv_KeyPoint_KeyPoint(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KeyPoint_KeyPoint_Point2f_float_float_float_int_int(pt: *const core::Point2f, size: f32, angle: f32, response: f32, octave: i32, class_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KeyPoint_KeyPoint_Point2f_float(pt: *const core::Point2f, size: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KeyPoint_KeyPoint_float_float_float_float_float_int_int(x: f32, y: f32, size: f32, angle: f32, response: f32, octave: i32, class_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KeyPoint_KeyPoint_float_float_float(x: f32, y: f32, size: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KeyPoint_hash_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_KeyPoint_convert_const_vectorLKeyPointGR_vectorLPoint2fGR_const_vectorLintGR(keypoints: *const c_void, points2f: *mut c_void, keypoint_indexes: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPoint_convert_const_vectorLKeyPointGR_vectorLPoint2fGR(keypoints: *const c_void, points2f: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPoint_convert_const_vectorLPoint2fGR_vectorLKeyPointGR_float_float_int_int(points2f: *const c_void, keypoints: *mut c_void, size: f32, response: f32, octave: i32, class_id: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPoint_convert_const_vectorLPoint2fGR_vectorLKeyPointGR(points2f: *const c_void, keypoints: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPoint_overlap_const_KeyPointR_const_KeyPointR(kp1: *const c_void, kp2: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_KeyPoint_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KeyPoint_propPt_const(instance: *const c_void, ocvrs_return: *mut core::Point2f);
pub fn cv_KeyPoint_propPt_const_Point2f(instance: *mut c_void, val: *const core::Point2f);
pub fn cv_KeyPoint_propSize_const(instance: *const c_void) -> f32;
pub fn cv_KeyPoint_propSize_const_float(instance: *mut c_void, val: f32);
pub fn cv_KeyPoint_propAngle_const(instance: *const c_void) -> f32;
pub fn cv_KeyPoint_propAngle_const_float(instance: *mut c_void, val: f32);
pub fn cv_KeyPoint_propResponse_const(instance: *const c_void) -> f32;
pub fn cv_KeyPoint_propResponse_const_float(instance: *mut c_void, val: f32);
pub fn cv_KeyPoint_propOctave_const(instance: *const c_void) -> i32;
pub fn cv_KeyPoint_propOctave_const_int(instance: *mut c_void, val: i32);
pub fn cv_KeyPoint_propClass_id_const(instance: *const c_void) -> i32;
pub fn cv_KeyPoint_propClass_id_const_int(instance: *mut c_void, val: i32);
pub fn cv_KeyPoint_delete(instance: *mut c_void);
pub fn cv_LDA_LDA_int(num_components: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_LDA(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_LDA_const__InputArrayR_const__InputArrayR_int(src: *const c_void, labels: *const c_void, num_components: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_LDA_const__InputArrayR_const__InputArrayR(src: *const c_void, labels: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_save_const_const_StringR(instance: *const c_void, filename: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_LDA_load_const_StringR(instance: *mut c_void, filename: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_LDA_save_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_LDA_load_const_FileStorageR(instance: *mut c_void, node: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_LDA_compute_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, labels: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_LDA_project_const__InputArrayR(instance: *mut c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_reconstruct_const__InputArrayR(instance: *mut c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_eigenvectors_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_eigenvalues_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_subspaceProject_const__InputArrayR_const__InputArrayR_const__InputArrayR(w: *const c_void, mean: *const c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_subspaceReconstruct_const__InputArrayR_const__InputArrayR_const__InputArrayR(w: *const c_void, mean: *const c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LDA_delete(instance: *mut c_void);
pub fn cv_Mat_Mat() -> *mut c_void;
pub fn cv_Mat_Mat_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_int_int_int_const_ScalarR(rows: i32, cols: i32, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_Size_int_const_ScalarR(size: *const core::Size, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_int_const_intX_int(ndims: i32, sizes: *const i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_vectorLintGR_int(sizes: *const c_void, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_int_const_intX_int_const_ScalarR(ndims: i32, sizes: *const i32, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_vectorLintGR_int_const_ScalarR(sizes: *const c_void, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_int_int_int_voidX_size_t(rows: i32, cols: i32, typ: i32, data: *mut c_void, step: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_int_int_int_voidX(rows: i32, cols: i32, typ: i32, data: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_Size_int_voidX_size_t(size: *const core::Size, typ: i32, data: *mut c_void, step: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_Size_int_voidX(size: *const core::Size, typ: i32, data: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_int_const_intX_int_voidX_const_size_tX(ndims: i32, sizes: *const i32, typ: i32, data: *mut c_void, steps: *const size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_int_const_intX_int_voidX(ndims: i32, sizes: *const i32, typ: i32, data: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_vectorLintGR_int_voidX_const_size_tX(sizes: *const c_void, typ: i32, data: *mut c_void, steps: *const size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_vectorLintGR_int_voidX(sizes: *const c_void, typ: i32, data: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_MatR_const_RangeR_const_RangeR(m: *const c_void, row_range: *const c_void, col_range: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_MatR_const_RangeR(m: *const c_void, row_range: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_MatR_const_RectR(m: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_MatR_const_vectorLRangeGR(m: *const c_void, ranges: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_Mat_const_GpuMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_operatorST_const_MatR(instance: *mut c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_operatorST_const_MatExprR(instance: *mut c_void, expr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_getUMat_const_AccessFlag_UMatUsageFlags(instance: *const c_void, access_flags: core::AccessFlag, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_getUMat_const_AccessFlag(instance: *const c_void, access_flags: core::AccessFlag, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_row_const_int(instance: *const c_void, y: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_col_const_int(instance: *const c_void, x: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_rowRange_const_int_int(instance: *const c_void, startrow: i32, endrow: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_rowRange_const_const_RangeR(instance: *const c_void, r: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_colRange_const_int_int(instance: *const c_void, startcol: i32, endcol: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_colRange_const_const_RangeR(instance: *const c_void, r: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_diag_const_int(instance: *const c_void, d: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_diag_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_diag_const_MatR(d: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_copyTo_const_const__OutputArrayR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_copyTo_const_const__OutputArrayR_const__InputArrayR(instance: *const c_void, m: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_convertTo_const_const__OutputArrayR_int_double_double(instance: *const c_void, m: *const c_void, rtype: i32, alpha: f64, beta: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_convertTo_const_const__OutputArrayR_int(instance: *const c_void, m: *const c_void, rtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_assignTo_const_MatR_int(instance: *const c_void, m: *mut c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_assignTo_const_MatR(instance: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_operatorST_const_ScalarR(instance: *mut c_void, s: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_setTo_const__InputArrayR_const__InputArrayR(instance: *mut c_void, value: *const c_void, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_setTo_const__InputArrayR(instance: *mut c_void, value: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_reshape_const_int_int(instance: *const c_void, cn: i32, rows: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_reshape_const_int(instance: *const c_void, cn: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_reshape_const_int_int_const_intX(instance: *const c_void, cn: i32, newndims: i32, newsz: *const i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_reshape_const_int_const_vectorLintGR(instance: *const c_void, cn: i32, newshape: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_t_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_inv_const_int(instance: *const c_void, method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_inv_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_mul_const_const__InputArrayR_double(instance: *const c_void, m: *const c_void, scale: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_mul_const_const__InputArrayR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_cross_const_const__InputArrayR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_dot_const_const__InputArrayR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_Mat_zeros_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_zeros_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_zeros_int_const_intX_int(ndims: i32, sz: *const i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_ones_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_ones_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_ones_int_const_intX_int(ndims: i32, sz: *const i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_eye_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_eye_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_create_int_int_int(instance: *mut c_void, rows: i32, cols: i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_create_Size_int(instance: *mut c_void, size: *const core::Size, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_create_int_const_intX_int(instance: *mut c_void, ndims: i32, sizes: *const i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_create_const_vectorLintGR_int(instance: *mut c_void, sizes: *const c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_addref(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_deallocate(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_reserve_size_t(instance: *mut c_void, sz: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_reserveBuffer_size_t(instance: *mut c_void, sz: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_resize_size_t(instance: *mut c_void, sz: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_resize_size_t_const_ScalarR(instance: *mut c_void, sz: size_t, s: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_push_back_const_MatR(instance: *mut c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_pop_back_size_t(instance: *mut c_void, nelems: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_pop_back(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_locateROI_const_SizeR_PointR(instance: *const c_void, whole_size: *mut core::Size, ofs: *mut core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_adjustROI_int_int_int_int(instance: *mut c_void, dtop: i32, dbottom: i32, dleft: i32, dright: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_operator___const_Range_Range(instance: *const c_void, row_range: *mut c_void, col_range: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_operator___const_const_RectR(instance: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_operator___const_const_RangeX(instance: *const c_void, ranges: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_operator___const_const_vectorLRangeGR(instance: *const c_void, ranges: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_isContinuous_const(instance: *const c_void) -> bool;
pub fn cv_Mat_isSubmatrix_const(instance: *const c_void) -> bool;
pub fn cv_Mat_elemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_Mat_elemSize1_const(instance: *const c_void) -> size_t;
pub fn cv_Mat_type_const(instance: *const c_void) -> i32;
pub fn cv_Mat_depth_const(instance: *const c_void) -> i32;
pub fn cv_Mat_channels_const(instance: *const c_void) -> i32;
pub fn cv_Mat_step1_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_Mat_step1_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_Mat_empty_const(instance: *const c_void) -> bool;
pub fn cv_Mat_total_const(instance: *const c_void) -> size_t;
pub fn cv_Mat_total_const_int_int(instance: *const c_void, start_dim: i32, end_dim: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_Mat_total_const_int(instance: *const c_void, start_dim: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_Mat_checkVector_const_int_int_bool(instance: *const c_void, elem_channels: i32, depth: i32, require_continuous: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_Mat_checkVector_const_int(instance: *const c_void, elem_channels: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Mat_ptr_int(instance: *mut c_void, i0: i32, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_Mat_ptr(instance: *mut c_void, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_Mat_ptr_const_int(instance: *const c_void, i0: i32, ocvrs_return: *mut Result<*const u8>);
pub fn cv_Mat_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*const u8>);
pub fn cv_Mat_ptr_int_int(instance: *mut c_void, row: i32, col: i32, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_Mat_ptr_const_int_int(instance: *const c_void, row: i32, col: i32, ocvrs_return: *mut Result<*const u8>);
pub fn cv_Mat_ptr_int_int_int(instance: *mut c_void, i0: i32, i1: i32, i2: i32, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_Mat_ptr_const_int_int_int(instance: *const c_void, i0: i32, i1: i32, i2: i32, ocvrs_return: *mut Result<*const u8>);
pub fn cv_Mat_ptr_const_intX(instance: *mut c_void, idx: *const i32, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_Mat_ptr_const_const_intX(instance: *const c_void, idx: *const i32, ocvrs_return: *mut Result<*const u8>);
pub fn cv_Mat_Mat_MatRR(m: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Mat_operatorST_MatRR(instance: *mut c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_updateContinuityFlag(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Mat_size_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_Mat_propFlags_const(instance: *const c_void) -> i32;
pub fn cv_Mat_propFlags_const_int(instance: *mut c_void, val: i32);
pub fn cv_Mat_propDims_const(instance: *const c_void) -> i32;
pub fn cv_Mat_propDims_const_int(instance: *mut c_void, val: i32);
pub fn cv_Mat_propRows_const(instance: *const c_void) -> i32;
pub fn cv_Mat_propRows_const_int(instance: *mut c_void, val: i32);
pub fn cv_Mat_propCols_const(instance: *const c_void) -> i32;
pub fn cv_Mat_propCols_const_int(instance: *mut c_void, val: i32);
pub fn cv_Mat_propData_const(instance: *const c_void) -> *const u8;
pub fn cv_Mat_propData(instance: *mut c_void) -> *mut u8;
pub fn cv_Mat_propData_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_Mat_propDatastart_const(instance: *const c_void) -> *const u8;
pub fn cv_Mat_propDataend_const(instance: *const c_void) -> *const u8;
pub fn cv_Mat_propDatalimit_const(instance: *const c_void) -> *const u8;
pub fn cv_Mat_propU(instance: *mut c_void) -> *mut c_void;
pub fn cv_Mat_propU_UMatDataX(instance: *mut c_void, val: *const c_void);
pub fn cv_Mat_propSize_const(instance: *const c_void) -> *mut c_void;
pub fn cv_Mat_propSize_const_MatSize(instance: *mut c_void, val: *const c_void);
pub fn cv_Mat_propStep_const(instance: *const c_void) -> *mut c_void;
pub fn cv_Mat_delete(instance: *mut c_void);
pub fn cv_MatConstIterator_MatConstIterator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatConstIterator_MatConstIterator_const_MatX(_m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatConstIterator_MatConstIterator_const_MatX_int_int(_m: *const c_void, _row: i32, _col: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatConstIterator_MatConstIterator_const_MatX_int(_m: *const c_void, _row: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatConstIterator_MatConstIterator_const_MatX_Point(_m: *const c_void, _pt: *const core::Point, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatConstIterator_MatConstIterator_const_MatConstIteratorR(it: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatConstIterator_operatorST_const_MatConstIteratorR(instance: *mut c_void, it: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatConstIterator_operatorX_const(instance: *const c_void, ocvrs_return: *mut Result<*const u8>);
pub fn cv_MatConstIterator_operator___const_ptrdiff_t(instance: *const c_void, i: ptrdiff_t, ocvrs_return: *mut Result<*const u8>);
pub fn cv_MatConstIterator_operatorSS(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatConstIterator_operatorAA(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatConstIterator_pos_const(instance: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_MatConstIterator_pos_const_intX(instance: *const c_void, _idx: *mut i32, ocvrs_return: *mut ResultVoid);
pub fn cv_MatConstIterator_lpos_const(instance: *const c_void, ocvrs_return: *mut Result<ptrdiff_t>);
pub fn cv_MatConstIterator_seek_ptrdiff_t_bool(instance: *mut c_void, ofs: ptrdiff_t, relative: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_MatConstIterator_seek_ptrdiff_t(instance: *mut c_void, ofs: ptrdiff_t, ocvrs_return: *mut ResultVoid);
pub fn cv_MatConstIterator_seek_const_intX_bool(instance: *mut c_void, _idx: *const i32, relative: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_MatConstIterator_seek_const_intX(instance: *mut c_void, _idx: *const i32, ocvrs_return: *mut ResultVoid);
pub fn cv_MatConstIterator_type_const(instance: *const c_void) -> i32;
pub fn cv_MatConstIterator_propM_const(instance: *const c_void) -> *mut c_void;
pub fn cv_MatConstIterator_propElemSize_const(instance: *const c_void) -> size_t;
pub fn cv_MatConstIterator_propElemSize_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_MatConstIterator_propPtr_const(instance: *const c_void) -> *const u8;
pub fn cv_MatConstIterator_propSliceStart_const(instance: *const c_void) -> *const u8;
pub fn cv_MatConstIterator_propSliceEnd_const(instance: *const c_void) -> *const u8;
pub fn cv_MatConstIterator_delete(instance: *mut c_void);
pub fn cv_MatExpr_MatExpr(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_MatExpr_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_MatExpr_const_MatOpX_int_const_MatR_const_MatR_const_MatR_double_double_const_ScalarR(_op: *const c_void, _flags: i32, _a: *const c_void, _b: *const c_void, _c: *const c_void, _alpha: f64, _beta: f64, _s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_MatExpr_const_MatOpX_int(_op: *const c_void, _flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_operator_cv_Mat_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_size_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_MatExpr_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_MatExpr_row_const_int(instance: *const c_void, y: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_col_const_int(instance: *const c_void, x: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_diag_const_int(instance: *const c_void, d: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_diag_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_operator___const_const_RangeR_const_RangeR(instance: *const c_void, row_range: *const c_void, col_range: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_operator___const_const_RectR(instance: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_t_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_inv_const_int(instance: *const c_void, method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_inv_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_mul_const_const_MatExprR_double(instance: *const c_void, e: *const c_void, scale: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_mul_const_const_MatExprR(instance: *const c_void, e: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_mul_const_const_MatR_double(instance: *const c_void, m: *const c_void, scale: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_mul_const_const_MatR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_cross_const_const_MatR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MatExpr_dot_const_const_MatR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_MatExpr_swap_MatExprR(instance: *mut c_void, b: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatExpr_propFlags_const(instance: *const c_void) -> i32;
pub fn cv_MatExpr_propFlags_const_int(instance: *mut c_void, val: i32);
pub fn cv_MatExpr_propA_const(instance: *const c_void) -> *mut c_void;
pub fn cv_MatExpr_propA_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_MatExpr_propB_const(instance: *const c_void) -> *mut c_void;
pub fn cv_MatExpr_propB_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_MatExpr_propC_const(instance: *const c_void) -> *mut c_void;
pub fn cv_MatExpr_propC_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_MatExpr_propAlpha_const(instance: *const c_void) -> f64;
pub fn cv_MatExpr_propAlpha_const_double(instance: *mut c_void, val: f64);
pub fn cv_MatExpr_propBeta_const(instance: *const c_void) -> f64;
pub fn cv_MatExpr_propBeta_const_double(instance: *mut c_void, val: f64);
pub fn cv_MatExpr_propS_const(instance: *const c_void, ocvrs_return: *mut core::Scalar);
pub fn cv_MatExpr_propS_const_Scalar(instance: *mut c_void, val: *const core::Scalar);
pub fn cv_MatExpr_delete(instance: *mut c_void);
pub fn cv_MatOp_elementWise_const_const_MatExprR(instance: *const c_void, expr: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_MatOp_assign_const_const_MatExprR_MatR_int(instance: *const c_void, expr: *const c_void, m: *mut c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_assign_const_const_MatExprR_MatR(instance: *const c_void, expr: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_roi_const_const_MatExprR_const_RangeR_const_RangeR_MatExprR(instance: *const c_void, expr: *const c_void, row_range: *const c_void, col_range: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_diag_const_const_MatExprR_int_MatExprR(instance: *const c_void, expr: *const c_void, d: i32, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_augAssignAdd_const_const_MatExprR_MatR(instance: *const c_void, expr: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_augAssignSubtract_const_const_MatExprR_MatR(instance: *const c_void, expr: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_augAssignMultiply_const_const_MatExprR_MatR(instance: *const c_void, expr: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_augAssignDivide_const_const_MatExprR_MatR(instance: *const c_void, expr: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_augAssignAnd_const_const_MatExprR_MatR(instance: *const c_void, expr: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_augAssignOr_const_const_MatExprR_MatR(instance: *const c_void, expr: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_augAssignXor_const_const_MatExprR_MatR(instance: *const c_void, expr: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_add_const_const_MatExprR_const_MatExprR_MatExprR(instance: *const c_void, expr1: *const c_void, expr2: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_add_const_const_MatExprR_const_ScalarR_MatExprR(instance: *const c_void, expr1: *const c_void, s: *const core::Scalar, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_subtract_const_const_MatExprR_const_MatExprR_MatExprR(instance: *const c_void, expr1: *const c_void, expr2: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_subtract_const_const_ScalarR_const_MatExprR_MatExprR(instance: *const c_void, s: *const core::Scalar, expr: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_multiply_const_const_MatExprR_const_MatExprR_MatExprR_double(instance: *const c_void, expr1: *const c_void, expr2: *const c_void, res: *mut c_void, scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_multiply_const_const_MatExprR_const_MatExprR_MatExprR(instance: *const c_void, expr1: *const c_void, expr2: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_multiply_const_const_MatExprR_double_MatExprR(instance: *const c_void, expr1: *const c_void, s: f64, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_divide_const_const_MatExprR_const_MatExprR_MatExprR_double(instance: *const c_void, expr1: *const c_void, expr2: *const c_void, res: *mut c_void, scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_divide_const_const_MatExprR_const_MatExprR_MatExprR(instance: *const c_void, expr1: *const c_void, expr2: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_divide_const_double_const_MatExprR_MatExprR(instance: *const c_void, s: f64, expr: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_abs_const_const_MatExprR_MatExprR(instance: *const c_void, expr: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_transpose_const_const_MatExprR_MatExprR(instance: *const c_void, expr: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_matmul_const_const_MatExprR_const_MatExprR_MatExprR(instance: *const c_void, expr1: *const c_void, expr2: *const c_void, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_invert_const_const_MatExprR_int_MatExprR(instance: *const c_void, expr: *const c_void, method: i32, res: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MatOp_size_const_const_MatExprR(instance: *const c_void, expr: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_MatOp_type_const_const_MatExprR(instance: *const c_void, expr: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_MatOp_delete(instance: *mut c_void);
pub fn cv_MatSize_MatSize_intX(_p: *mut i32) -> *mut c_void;
pub fn cv_MatSize_dims_const(instance: *const c_void) -> i32;
pub fn cv_MatSize_operator___const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_MatSize_operator___const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_MatSize_operator___int(instance: *mut c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_MatSize_operator_const_intX_const(instance: *const c_void) -> *const i32;
pub fn cv_MatSize_operatorEQ_const_const_MatSizeR(instance: *const c_void, sz: *const c_void) -> bool;
pub fn cv_MatSize_operatorNE_const_const_MatSizeR(instance: *const c_void, sz: *const c_void) -> bool;
pub fn cv_MatSize_propP_const(instance: *const c_void) -> *const i32;
pub fn cv_MatSize_propP(instance: *mut c_void) -> *mut i32;
pub fn cv_MatSize_propP_intX(instance: *mut c_void, val: *const i32);
pub fn cv_MatSize_delete(instance: *mut c_void);
pub fn cv_MatStep_MatStep() -> *mut c_void;
pub fn cv_MatStep_MatStep_size_t(s: size_t) -> *mut c_void;
pub fn cv_MatStep_operator___const_int(instance: *const c_void, i: i32) -> size_t;
pub fn cv_MatStep_operator___int(instance: *mut c_void, i: i32) -> size_t;
pub fn cv_MatStep_operator_size_t_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_MatStep_operatorST_size_t(instance: *mut c_void, s: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_MatStep_propP_const(instance: *const c_void) -> *const size_t;
pub fn cv_MatStep_propP(instance: *mut c_void) -> *mut size_t;
pub fn cv_MatStep_propP_size_tX(instance: *mut c_void, val: *const size_t);
pub fn cv_MatStep_propBuf_const(instance: *const c_void) -> *const [size_t; 2];
pub fn cv_MatStep_propBuf(instance: *mut c_void) -> *mut [size_t; 2];
pub fn cv_MatStep_delete(instance: *mut c_void);
pub fn cv_Matx_AddOp_Matx_AddOp(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_AddOp_Matx_AddOp_const_Matx_AddOpR(unnamed: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_AddOp_delete(instance: *mut c_void);
pub fn cv_Matx_DivOp_Matx_DivOp(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_DivOp_Matx_DivOp_const_Matx_DivOpR(unnamed: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_DivOp_delete(instance: *mut c_void);
pub fn cv_Matx_MatMulOp_Matx_MatMulOp(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_MatMulOp_Matx_MatMulOp_const_Matx_MatMulOpR(unnamed: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_MatMulOp_delete(instance: *mut c_void);
pub fn cv_Matx_MulOp_Matx_MulOp(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_MulOp_Matx_MulOp_const_Matx_MulOpR(unnamed: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_MulOp_delete(instance: *mut c_void);
pub fn cv_Matx_ScaleOp_Matx_ScaleOp(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_ScaleOp_Matx_ScaleOp_const_Matx_ScaleOpR(unnamed: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_ScaleOp_delete(instance: *mut c_void);
pub fn cv_Matx_SubOp_Matx_SubOp(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_SubOp_Matx_SubOp_const_Matx_SubOpR(unnamed: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_SubOp_delete(instance: *mut c_void);
pub fn cv_Matx_TOp_Matx_TOp(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_TOp_Matx_TOp_const_Matx_TOpR(unnamed: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Matx_TOp_delete(instance: *mut c_void);
pub fn cv_MinProblemSolver_getFunction_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MinProblemSolver_setFunction_const_PtrLFunctionGR(instance: *mut c_void, f: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MinProblemSolver_getTermCriteria_const(instance: *const c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_MinProblemSolver_setTermCriteria_const_TermCriteriaR(instance: *mut c_void, termcrit: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_MinProblemSolver_minimize_const__InputOutputArrayR(instance: *mut c_void, x: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_MinProblemSolver_to_ConjGradSolver(instance: *mut c_void) -> *mut c_void;
pub fn cv_MinProblemSolver_to_DownhillSolver(instance: *mut c_void) -> *mut c_void;
pub fn cv_MinProblemSolver_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_MinProblemSolver_delete(instance: *mut c_void);
pub fn cv_MinProblemSolver_Function_getDims_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_MinProblemSolver_Function_getGradientEps_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_MinProblemSolver_Function_calc_const_const_doubleX(instance: *const c_void, x: *const f64, ocvrs_return: *mut Result<f64>);
pub fn cv_MinProblemSolver_Function_getGradient_const_doubleX_doubleX(instance: *mut c_void, x: *const f64, grad: *mut f64, ocvrs_return: *mut ResultVoid);
pub fn cv_MinProblemSolver_Function_delete(instance: *mut c_void);
pub fn cv_Moments_Moments(ocvrs_return: *mut Result<core::Moments>);
pub fn cv_Moments_Moments_double_double_double_double_double_double_double_double_double_double(m00: f64, m10: f64, m01: f64, m20: f64, m11: f64, m02: f64, m30: f64, m21: f64, m12: f64, m03: f64, ocvrs_return: *mut Result<core::Moments>);
pub fn cv_PCA_PCA(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_PCA_const__InputArrayR_const__InputArrayR_int_int(data: *const c_void, mean: *const c_void, flags: i32, max_components: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_PCA_const__InputArrayR_const__InputArrayR_int(data: *const c_void, mean: *const c_void, flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_PCA_const__InputArrayR_const__InputArrayR_int_double(data: *const c_void, mean: *const c_void, flags: i32, retained_variance: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_operator___const__InputArrayR_const__InputArrayR_int_int(instance: *mut c_void, data: *const c_void, mean: *const c_void, flags: i32, max_components: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_operator___const__InputArrayR_const__InputArrayR_int(instance: *mut c_void, data: *const c_void, mean: *const c_void, flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_operator___const__InputArrayR_const__InputArrayR_int_double(instance: *mut c_void, data: *const c_void, mean: *const c_void, flags: i32, retained_variance: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_project_const_const__InputArrayR(instance: *const c_void, vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_project_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, vec: *const c_void, result: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PCA_backProject_const_const__InputArrayR(instance: *const c_void, vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PCA_backProject_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, vec: *const c_void, result: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PCA_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PCA_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PCA_propEigenvectors_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PCA_propEigenvectors_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_PCA_propEigenvalues_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PCA_propEigenvalues_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_PCA_propMean_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PCA_propMean_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_PCA_delete(instance: *mut c_void);
pub fn cv_ParallelLoopBody_operator___const_const_RangeR(instance: *const c_void, range: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ParallelLoopBody_delete(instance: *mut c_void);
pub fn cv_RNG_RNG(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_RNG_RNG_uint64_t(state: u64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_RNG_next(instance: *mut c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_RNG_operator_unsigned_char(instance: *mut c_void, ocvrs_return: *mut Result<u8>);
pub fn cv_RNG_operator_signed_char(instance: *mut c_void, ocvrs_return: *mut Result<i8>);
pub fn cv_RNG_operator_unsigned_short(instance: *mut c_void, ocvrs_return: *mut Result<u16>);
pub fn cv_RNG_operator_short(instance: *mut c_void, ocvrs_return: *mut Result<i16>);
pub fn cv_RNG_operator_unsigned_int(instance: *mut c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_RNG_operator_int(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_RNG_operator_float(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_RNG_operator_double(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_RNG_operator__(instance: *mut c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_RNG_operator___unsigned_int(instance: *mut c_void, n: u32, ocvrs_return: *mut Result<u32>);
pub fn cv_RNG_uniform_int_int(instance: *mut c_void, a: i32, b: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_RNG_uniform_float_float(instance: *mut c_void, a: f32, b: f32, ocvrs_return: *mut Result<f32>);
pub fn cv_RNG_uniform_double_double(instance: *mut c_void, a: f64, b: f64, ocvrs_return: *mut Result<f64>);
pub fn cv_RNG_fill_const__InputOutputArrayR_int_const__InputArrayR_const__InputArrayR_bool(instance: *mut c_void, mat: *const c_void, dist_type: i32, a: *const c_void, b: *const c_void, saturate_range: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_RNG_fill_const__InputOutputArrayR_int_const__InputArrayR_const__InputArrayR(instance: *mut c_void, mat: *const c_void, dist_type: i32, a: *const c_void, b: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_RNG_gaussian_double(instance: *mut c_void, sigma: f64, ocvrs_return: *mut Result<f64>);
pub fn cv_RNG_operatorEQ_const_const_RNGR(instance: *const c_void, other: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_RNG_propState_const(instance: *const c_void) -> u64;
pub fn cv_RNG_propState_const_uint64_t(instance: *mut c_void, val: u64);
pub fn cv_RNG_delete(instance: *mut c_void);
pub fn cv_RNG_MT19937_RNG_MT19937(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_RNG_MT19937_RNG_MT19937_unsigned_int(s: u32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_RNG_MT19937_seed_unsigned_int(instance: *mut c_void, s: u32, ocvrs_return: *mut ResultVoid);
pub fn cv_RNG_MT19937_next(instance: *mut c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_RNG_MT19937_operator_int(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_RNG_MT19937_operator_unsigned_int(instance: *mut c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_RNG_MT19937_operator_float(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_RNG_MT19937_operator_double(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_RNG_MT19937_operator___unsigned_int(instance: *mut c_void, n: u32, ocvrs_return: *mut Result<u32>);
pub fn cv_RNG_MT19937_operator__(instance: *mut c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_RNG_MT19937_uniform_int_int(instance: *mut c_void, a: i32, b: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_RNG_MT19937_uniform_float_float(instance: *mut c_void, a: f32, b: f32, ocvrs_return: *mut Result<f32>);
pub fn cv_RNG_MT19937_uniform_double_double(instance: *mut c_void, a: f64, b: f64, ocvrs_return: *mut Result<f64>);
pub fn cv_RNG_MT19937_delete(instance: *mut c_void);
pub fn cv_Range_Range(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Range_Range_int_int(_start: i32, _end: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Range_size_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_Range_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_Range_all(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Range_propStart_const(instance: *const c_void) -> i32;
pub fn cv_Range_propStart_const_int(instance: *mut c_void, val: i32);
pub fn cv_Range_propEnd_const(instance: *const c_void) -> i32;
pub fn cv_Range_propEnd_const_int(instance: *mut c_void, val: i32);
pub fn cv_Range_delete(instance: *mut c_void);
pub fn cv_RotatedRect_RotatedRect(ocvrs_return: *mut Result<core::RotatedRect>);
pub fn cv_RotatedRect_RotatedRect_const_Point2fR_const_Size2fR_float(center: *const core::Point2f, size: *const core::Size2f, angle: f32, ocvrs_return: *mut Result<core::RotatedRect>);
pub fn cv_RotatedRect_RotatedRect_const_Point2fR_const_Point2fR_const_Point2fR(point1: *const core::Point2f, point2: *const core::Point2f, point3: *const core::Point2f, ocvrs_return: *mut Result<core::RotatedRect>);
pub fn cv_RotatedRect_points_const_Point2fX(instance: *const core::RotatedRect, pts: *mut core::Point2f, ocvrs_return: *mut ResultVoid);
pub fn cv_RotatedRect_boundingRect_const(instance: *const core::RotatedRect, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_RotatedRect_boundingRect2f_const(instance: *const core::RotatedRect, ocvrs_return: *mut Result<core::Rect_<f32>>);
pub fn cv_SVD_SVD(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SVD_SVD_const__InputArrayR_int(src: *const c_void, flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SVD_SVD_const__InputArrayR(src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SVD_operator___const__InputArrayR_int(instance: *mut c_void, src: *const c_void, flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SVD_operator___const__InputArrayR(instance: *mut c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SVD_compute_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int(src: *const c_void, w: *const c_void, u: *const c_void, vt: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SVD_compute_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, w: *const c_void, u: *const c_void, vt: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SVD_compute_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, w: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SVD_compute_const__InputArrayR_const__OutputArrayR(src: *const c_void, w: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SVD_backSubst_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(w: *const c_void, u: *const c_void, vt: *const c_void, rhs: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SVD_solveZ_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SVD_backSubst_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, rhs: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SVD_propU_const(instance: *const c_void) -> *mut c_void;
pub fn cv_SVD_propU_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_SVD_propW_const(instance: *const c_void) -> *mut c_void;
pub fn cv_SVD_propW_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_SVD_propVt_const(instance: *const c_void) -> *mut c_void;
pub fn cv_SVD_propVt_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_SVD_delete(instance: *mut c_void);
pub fn cv_SparseMat_SparseMat(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_SparseMat_int_const_intX_int(dims: i32, _sizes: *const i32, _type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_SparseMat_const_SparseMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_SparseMat_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_operatorST_const_SparseMatR(instance: *mut c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_operatorST_const_MatR(instance: *mut c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_copyTo_const_SparseMatR(instance: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_copyTo_const_MatR(instance: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_convertTo_const_SparseMatR_int_double(instance: *const c_void, m: *mut c_void, rtype: i32, alpha: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_convertTo_const_SparseMatR_int(instance: *const c_void, m: *mut c_void, rtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_convertTo_const_MatR_int_double_double(instance: *const c_void, m: *mut c_void, rtype: i32, alpha: f64, beta: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_convertTo_const_MatR_int(instance: *const c_void, m: *mut c_void, rtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_assignTo_const_SparseMatR_int(instance: *const c_void, m: *mut c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_assignTo_const_SparseMatR(instance: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_create_int_const_intX_int(instance: *mut c_void, dims: i32, _sizes: *const i32, _type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_addref(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_elemSize_const(instance: *const c_void) -> size_t;
pub fn cv_SparseMat_elemSize1_const(instance: *const c_void) -> size_t;
pub fn cv_SparseMat_type_const(instance: *const c_void) -> i32;
pub fn cv_SparseMat_depth_const(instance: *const c_void) -> i32;
pub fn cv_SparseMat_channels_const(instance: *const c_void) -> i32;
pub fn cv_SparseMat_size_const(instance: *const c_void, ocvrs_return: *mut Result<*const i32>);
pub fn cv_SparseMat_size_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_SparseMat_dims_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_SparseMat_nzcount_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_SparseMat_hash_const_int(instance: *const c_void, i0: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_SparseMat_hash_const_int_int(instance: *const c_void, i0: i32, i1: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_SparseMat_hash_const_int_int_int(instance: *const c_void, i0: i32, i1: i32, i2: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_SparseMat_hash_const_const_intX(instance: *const c_void, idx: *const i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_SparseMat_ptr_int_bool_size_tX(instance: *mut c_void, i0: i32, create_missing: bool, hashval: *mut size_t, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_ptr_int_bool(instance: *mut c_void, i0: i32, create_missing: bool, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_ptr_int_int_bool_size_tX(instance: *mut c_void, i0: i32, i1: i32, create_missing: bool, hashval: *mut size_t, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_ptr_int_int_bool(instance: *mut c_void, i0: i32, i1: i32, create_missing: bool, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_ptr_int_int_int_bool_size_tX(instance: *mut c_void, i0: i32, i1: i32, i2: i32, create_missing: bool, hashval: *mut size_t, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_ptr_int_int_int_bool(instance: *mut c_void, i0: i32, i1: i32, i2: i32, create_missing: bool, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_ptr_const_intX_bool_size_tX(instance: *mut c_void, idx: *const i32, create_missing: bool, hashval: *mut size_t, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_ptr_const_intX_bool(instance: *mut c_void, idx: *const i32, create_missing: bool, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_erase_int_int_size_tX(instance: *mut c_void, i0: i32, i1: i32, hashval: *mut size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_erase_int_int(instance: *mut c_void, i0: i32, i1: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_erase_int_int_int_size_tX(instance: *mut c_void, i0: i32, i1: i32, i2: i32, hashval: *mut size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_erase_int_int_int(instance: *mut c_void, i0: i32, i1: i32, i2: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_erase_const_intX_size_tX(instance: *mut c_void, idx: *const i32, hashval: *mut size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_erase_const_intX(instance: *mut c_void, idx: *const i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_begin(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_begin_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_end(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_end_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_node_size_t(instance: *mut c_void, nidx: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_node_const_size_t(instance: *const c_void, nidx: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_newNode_const_intX_size_t(instance: *mut c_void, idx: *const i32, hashval: size_t, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_SparseMat_removeNode_size_t_size_t_size_t(instance: *mut c_void, hidx: size_t, nidx: size_t, previdx: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_resizeHashTab_size_t(instance: *mut c_void, newsize: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_propFlags_const(instance: *const c_void) -> i32;
pub fn cv_SparseMat_propFlags_const_int(instance: *mut c_void, val: i32);
pub fn cv_SparseMat_propHdr(instance: *mut c_void) -> *mut c_void;
pub fn cv_SparseMat_propHdr_HdrX(instance: *mut c_void, val: *const c_void);
pub fn cv_SparseMat_delete(instance: *mut c_void);
pub fn cv_SparseMat_Hdr_Hdr_int_const_intX_int(_dims: i32, _sizes: *const i32, _type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMat_Hdr_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMat_Hdr_propRefcount_const(instance: *const c_void) -> i32;
pub fn cv_SparseMat_Hdr_propRefcount_const_int(instance: *mut c_void, val: i32);
pub fn cv_SparseMat_Hdr_propDims_const(instance: *const c_void) -> i32;
pub fn cv_SparseMat_Hdr_propDims_const_int(instance: *mut c_void, val: i32);
pub fn cv_SparseMat_Hdr_propValueOffset_const(instance: *const c_void) -> i32;
pub fn cv_SparseMat_Hdr_propValueOffset_const_int(instance: *mut c_void, val: i32);
pub fn cv_SparseMat_Hdr_propNodeSize_const(instance: *const c_void) -> size_t;
pub fn cv_SparseMat_Hdr_propNodeSize_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_SparseMat_Hdr_propNodeCount_const(instance: *const c_void) -> size_t;
pub fn cv_SparseMat_Hdr_propNodeCount_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_SparseMat_Hdr_propFreeList_const(instance: *const c_void) -> size_t;
pub fn cv_SparseMat_Hdr_propFreeList_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_SparseMat_Hdr_propPool_const(instance: *const c_void) -> *mut c_void;
pub fn cv_SparseMat_Hdr_propPool_const_vectorLunsigned_charG(instance: *mut c_void, val: *const c_void);
pub fn cv_SparseMat_Hdr_propHashtab_const(instance: *const c_void) -> *mut c_void;
pub fn cv_SparseMat_Hdr_propHashtab_const_vectorLsize_tG(instance: *mut c_void, val: *const c_void);
pub fn cv_SparseMat_Hdr_propSize_const(instance: *const c_void) -> *const [i32; 32];
pub fn cv_SparseMat_Hdr_propSize(instance: *mut c_void) -> *mut [i32; 32];
pub fn cv_SparseMat_Hdr_delete(instance: *mut c_void);
pub fn cv_SparseMat_Node_propHashval_const(instance: *const c_void) -> size_t;
pub fn cv_SparseMat_Node_propHashval_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_SparseMat_Node_propNext_const(instance: *const c_void) -> size_t;
pub fn cv_SparseMat_Node_propNext_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_SparseMat_Node_propIdx_const(instance: *const c_void) -> *const [i32; 32];
pub fn cv_SparseMat_Node_propIdx(instance: *mut c_void) -> *mut [i32; 32];
pub fn cv_SparseMat_Node_delete(instance: *mut c_void);
pub fn cv_SparseMatConstIterator_SparseMatConstIterator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatConstIterator_SparseMatConstIterator_const_SparseMatX(_m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatConstIterator_SparseMatConstIterator_const_SparseMatConstIteratorR(it: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatConstIterator_operatorST_const_SparseMatConstIteratorR(instance: *mut c_void, it: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMatConstIterator_node_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatConstIterator_operatorAA(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatConstIterator_seekEnd(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMatConstIterator_propM_const(instance: *const c_void) -> *mut c_void;
pub fn cv_SparseMatConstIterator_propHashidx_const(instance: *const c_void) -> size_t;
pub fn cv_SparseMatConstIterator_propHashidx_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_SparseMatConstIterator_propPtr_const(instance: *const c_void) -> *const u8;
pub fn cv_SparseMatConstIterator_propPtr(instance: *mut c_void) -> *mut u8;
pub fn cv_SparseMatConstIterator_propPtr_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_SparseMatConstIterator_delete(instance: *mut c_void);
pub fn cv_SparseMatIterator_SparseMatIterator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatIterator_SparseMatIterator_SparseMatX(_m: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatIterator_SparseMatIterator_const_SparseMatIteratorR(it: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatIterator_operatorST_const_SparseMatIteratorR(instance: *mut c_void, it: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseMatIterator_node_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatIterator_operatorAA(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparseMatIterator_to_SparseMatConstIterator(instance: *mut c_void) -> *mut c_void;
pub fn cv_SparseMatIterator_delete(instance: *mut c_void);
pub fn cv_TermCriteria_TermCriteria(ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_TermCriteria_TermCriteria_int_int_double(typ: i32, max_count: i32, epsilon: f64, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_TermCriteria_isValid_const(instance: *const core::TermCriteria, ocvrs_return: *mut Result<bool>);
pub fn cv_TickMeter_TickMeter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TickMeter_start(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_TickMeter_stop(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_TickMeter_getTimeTicks_const(instance: *const c_void, ocvrs_return: *mut Result<i64>);
pub fn cv_TickMeter_getTimeMicro_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_TickMeter_getTimeMilli_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_TickMeter_getTimeSec_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_TickMeter_getCounter_const(instance: *const c_void, ocvrs_return: *mut Result<i64>);
pub fn cv_TickMeter_getFPS_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_TickMeter_getAvgTimeSec_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_TickMeter_getAvgTimeMilli_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_TickMeter_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_TickMeter_delete(instance: *mut c_void);
pub fn cv_UMat_UMat_UMatUsageFlags(usage_flags: core::UMatUsageFlags) -> *mut c_void;
pub fn cv_UMat_UMat() -> *mut c_void;
pub fn cv_UMat_UMat_int_int_int_UMatUsageFlags(rows: i32, cols: i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_Size_int_UMatUsageFlags(size: *const core::Size, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_int_int_int_const_ScalarR_UMatUsageFlags(rows: i32, cols: i32, typ: i32, s: *const core::Scalar, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_int_int_int_const_ScalarR(rows: i32, cols: i32, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_Size_int_const_ScalarR_UMatUsageFlags(size: *const core::Size, typ: i32, s: *const core::Scalar, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_Size_int_const_ScalarR(size: *const core::Size, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_int_const_intX_int_UMatUsageFlags(ndims: i32, sizes: *const i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_int_const_intX_int(ndims: i32, sizes: *const i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_int_const_intX_int_const_ScalarR_UMatUsageFlags(ndims: i32, sizes: *const i32, typ: i32, s: *const core::Scalar, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_int_const_intX_int_const_ScalarR(ndims: i32, sizes: *const i32, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_const_UMatR_const_RangeR_const_RangeR(m: *const c_void, row_range: *const c_void, col_range: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_const_UMatR_const_RangeR(m: *const c_void, row_range: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_const_UMatR_const_RectR(m: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_UMat_const_UMatR_const_vectorLRangeGR(m: *const c_void, ranges: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_operatorST_const_UMatR(instance: *mut c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_getMat_const_AccessFlag(instance: *const c_void, flags: core::AccessFlag, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_row_const_int(instance: *const c_void, y: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_col_const_int(instance: *const c_void, x: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_rowRange_const_int_int(instance: *const c_void, startrow: i32, endrow: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_rowRange_const_const_RangeR(instance: *const c_void, r: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_colRange_const_int_int(instance: *const c_void, startcol: i32, endcol: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_colRange_const_const_RangeR(instance: *const c_void, r: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_diag_const_int(instance: *const c_void, d: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_diag_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_diag_const_UMatR_UMatUsageFlags(d: *const c_void, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_diag_const_UMatR(d: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_copyTo_const_const__OutputArrayR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_copyTo_const_const__OutputArrayR_const__InputArrayR(instance: *const c_void, m: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_convertTo_const_const__OutputArrayR_int_double_double(instance: *const c_void, m: *const c_void, rtype: i32, alpha: f64, beta: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_convertTo_const_const__OutputArrayR_int(instance: *const c_void, m: *const c_void, rtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_assignTo_const_UMatR_int(instance: *const c_void, m: *mut c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_assignTo_const_UMatR(instance: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_operatorST_const_ScalarR(instance: *mut c_void, s: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_setTo_const__InputArrayR_const__InputArrayR(instance: *mut c_void, value: *const c_void, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_setTo_const__InputArrayR(instance: *mut c_void, value: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_reshape_const_int_int(instance: *const c_void, cn: i32, rows: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_reshape_const_int(instance: *const c_void, cn: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_reshape_const_int_int_const_intX(instance: *const c_void, cn: i32, newndims: i32, newsz: *const i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_t_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_inv_const_int(instance: *const c_void, method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_inv_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_mul_const_const__InputArrayR_double(instance: *const c_void, m: *const c_void, scale: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_mul_const_const__InputArrayR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_dot_const_const__InputArrayR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_UMat_zeros_int_int_int_UMatUsageFlags(rows: i32, cols: i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_zeros_Size_int_UMatUsageFlags(size: *const core::Size, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_zeros_int_const_intX_int_UMatUsageFlags(ndims: i32, sz: *const i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_zeros_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_zeros_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_zeros_int_const_intX_int(ndims: i32, sz: *const i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_ones_int_int_int_UMatUsageFlags(rows: i32, cols: i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_ones_Size_int_UMatUsageFlags(size: *const core::Size, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_ones_int_const_intX_int_UMatUsageFlags(ndims: i32, sz: *const i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_ones_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_ones_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_ones_int_const_intX_int(ndims: i32, sz: *const i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_eye_int_int_int_UMatUsageFlags(rows: i32, cols: i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_eye_Size_int_UMatUsageFlags(size: *const core::Size, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_eye_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_eye_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_create_int_int_int_UMatUsageFlags(instance: *mut c_void, rows: i32, cols: i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_create_int_int_int(instance: *mut c_void, rows: i32, cols: i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_create_Size_int_UMatUsageFlags(instance: *mut c_void, size: *const core::Size, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_create_Size_int(instance: *mut c_void, size: *const core::Size, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_create_int_const_intX_int_UMatUsageFlags(instance: *mut c_void, ndims: i32, sizes: *const i32, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_create_int_const_intX_int(instance: *mut c_void, ndims: i32, sizes: *const i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_create_const_vectorLintGR_int_UMatUsageFlags(instance: *mut c_void, sizes: *const c_void, typ: i32, usage_flags: core::UMatUsageFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_create_const_vectorLintGR_int(instance: *mut c_void, sizes: *const c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_addref(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_deallocate(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_locateROI_const_SizeR_PointR(instance: *const c_void, whole_size: *mut core::Size, ofs: *mut core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_adjustROI_int_int_int_int(instance: *mut c_void, dtop: i32, dbottom: i32, dleft: i32, dright: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_operator___const_Range_Range(instance: *const c_void, row_range: *mut c_void, col_range: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_operator___const_const_RectR(instance: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_operator___const_const_RangeX(instance: *const c_void, ranges: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_operator___const_const_vectorLRangeGR(instance: *const c_void, ranges: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_isContinuous_const(instance: *const c_void) -> bool;
pub fn cv_UMat_isSubmatrix_const(instance: *const c_void) -> bool;
pub fn cv_UMat_elemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_UMat_elemSize1_const(instance: *const c_void) -> size_t;
pub fn cv_UMat_type_const(instance: *const c_void) -> i32;
pub fn cv_UMat_depth_const(instance: *const c_void) -> i32;
pub fn cv_UMat_channels_const(instance: *const c_void) -> i32;
pub fn cv_UMat_step1_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv_UMat_step1_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_UMat_empty_const(instance: *const c_void) -> bool;
pub fn cv_UMat_total_const(instance: *const c_void) -> size_t;
pub fn cv_UMat_checkVector_const_int_int_bool(instance: *const c_void, elem_channels: i32, depth: i32, require_continuous: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_UMat_checkVector_const_int(instance: *const c_void, elem_channels: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_UMat_UMat_UMatRR(m: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_operatorST_UMatRR(instance: *mut c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_handle_const_AccessFlag(instance: *const c_void, access_flags: core::AccessFlag, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_UMat_ndoffset_const_size_tX(instance: *const c_void, ofs: *mut size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_updateContinuityFlag(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMat_size_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_UMat_propFlags_const(instance: *const c_void) -> i32;
pub fn cv_UMat_propFlags_const_int(instance: *mut c_void, val: i32);
pub fn cv_UMat_propDims_const(instance: *const c_void) -> i32;
pub fn cv_UMat_propDims_const_int(instance: *mut c_void, val: i32);
pub fn cv_UMat_propRows_const(instance: *const c_void) -> i32;
pub fn cv_UMat_propRows_const_int(instance: *mut c_void, val: i32);
pub fn cv_UMat_propCols_const(instance: *const c_void) -> i32;
pub fn cv_UMat_propCols_const_int(instance: *mut c_void, val: i32);
pub fn cv_UMat_propUsageFlags_const(instance: *const c_void, ocvrs_return: *mut core::UMatUsageFlags);
pub fn cv_UMat_propUsageFlags_const_UMatUsageFlags(instance: *mut c_void, val: core::UMatUsageFlags);
pub fn cv_UMat_propU(instance: *mut c_void) -> *mut c_void;
pub fn cv_UMat_propU_UMatDataX(instance: *mut c_void, val: *const c_void);
pub fn cv_UMat_propOffset_const(instance: *const c_void) -> size_t;
pub fn cv_UMat_propOffset_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_UMat_propSize_const(instance: *const c_void) -> *mut c_void;
pub fn cv_UMat_propSize_const_MatSize(instance: *mut c_void, val: *const c_void);
pub fn cv_UMat_propStep_const(instance: *const c_void) -> *mut c_void;
pub fn cv_UMat_delete(instance: *mut c_void);
pub fn cv_UMatData_lock(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMatData_unlock(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_UMatData_hostCopyObsolete_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_UMatData_deviceCopyObsolete_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_UMatData_deviceMemMapped_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_UMatData_copyOnMap_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_UMatData_tempUMat_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_UMatData_tempCopiedUMat_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_UMatData_markHostCopyObsolete_bool(instance: *mut c_void, flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_UMatData_markDeviceCopyObsolete_bool(instance: *mut c_void, flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_UMatData_markDeviceMemMapped_bool(instance: *mut c_void, flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_UMatData_propUrefcount_const(instance: *const c_void) -> i32;
pub fn cv_UMatData_propUrefcount_const_int(instance: *mut c_void, val: i32);
pub fn cv_UMatData_propRefcount_const(instance: *const c_void) -> i32;
pub fn cv_UMatData_propRefcount_const_int(instance: *mut c_void, val: i32);
pub fn cv_UMatData_propData_const(instance: *const c_void) -> *const u8;
pub fn cv_UMatData_propData(instance: *mut c_void) -> *mut u8;
pub fn cv_UMatData_propData_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_UMatData_propOrigdata_const(instance: *const c_void) -> *const u8;
pub fn cv_UMatData_propOrigdata(instance: *mut c_void) -> *mut u8;
pub fn cv_UMatData_propOrigdata_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_UMatData_propSize_const(instance: *const c_void) -> size_t;
pub fn cv_UMatData_propSize_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_UMatData_propFlags_const(instance: *const c_void, ocvrs_return: *mut core::UMatData_MemoryFlag);
pub fn cv_UMatData_propFlags_const_MemoryFlag(instance: *mut c_void, val: core::UMatData_MemoryFlag);
pub fn cv_UMatData_propHandle(instance: *mut c_void) -> *mut c_void;
pub fn cv_UMatData_propHandle_voidX(instance: *mut c_void, val: *const c_void);
pub fn cv_UMatData_propUserdata(instance: *mut c_void) -> *mut c_void;
pub fn cv_UMatData_propUserdata_voidX(instance: *mut c_void, val: *const c_void);
pub fn cv_UMatData_propAllocatorFlags__const(instance: *const c_void) -> i32;
pub fn cv_UMatData_propAllocatorFlags__const_int(instance: *mut c_void, val: i32);
pub fn cv_UMatData_propMapcount_const(instance: *const c_void) -> i32;
pub fn cv_UMatData_propMapcount_const_int(instance: *mut c_void, val: i32);
pub fn cv_UMatData_propOriginalUMatData(instance: *mut c_void) -> *mut c_void;
pub fn cv_UMatData_propOriginalUMatData_UMatDataX(instance: *mut c_void, val: *const c_void);
pub fn cv_UMatData_delete(instance: *mut c_void);
pub fn cv__InputArray__InputArray(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_int_voidX(_flags: i32, _obj: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_MatExprR(expr: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_vectorLMatGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_vectorLboolGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_doubleR(val: *const f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_GpuMatR(d_mat: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_vectorLGpuMatGR(d_mat_array: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_BufferR(buf: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_HostMemR(cuda_mem: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_UMatR(um: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray__InputArray_const_vectorLUMatGR(umv: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getMat_const_int(instance: *const c_void, idx: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getMat_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getMat__const_int(instance: *const c_void, idx: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getMat__const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getUMat_const_int(instance: *const c_void, idx: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getUMat_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getMatVector_const_vectorLMatGR(instance: *const c_void, mv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__InputArray_getUMatVector_const_vectorLUMatGR(instance: *const c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__InputArray_getGpuMatVector_const_vectorLGpuMatGR(instance: *const c_void, gpumv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__InputArray_getGpuMat_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getOGlBuffer_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getFlags_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_getObj_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputArray_getSz_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv__InputArray_kind_const(instance: *const c_void, ocvrs_return: *mut Result<core::_InputArray_KindFlag>);
pub fn cv__InputArray_dims_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_dims_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_cols_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_cols_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_rows_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_rows_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_size_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<core::Size>);
pub fn cv__InputArray_size_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv__InputArray_sizend_const_intX_int(instance: *const c_void, sz: *mut i32, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_sizend_const_intX(instance: *const c_void, sz: *mut i32, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_sameSize_const_const__InputArrayR(instance: *const c_void, arr: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_total_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv__InputArray_total_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv__InputArray_type_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_depth_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_depth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_channels_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_channels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv__InputArray_isContinuous_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isContinuous_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isSubmatrix_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isSubmatrix_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_copyTo_const_const__OutputArrayR(instance: *const c_void, arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__InputArray_copyTo_const_const__OutputArrayR_const__InputArrayR(instance: *const c_void, arr: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__InputArray_offset_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv__InputArray_offset_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv__InputArray_step_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<size_t>);
pub fn cv__InputArray_step_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv__InputArray_isMat_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isUMat_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isMatVector_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isUMatVector_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isMatx_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isVector_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isGpuMat_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_isGpuMatVector_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__InputArray_delete(instance: *mut c_void);
pub fn cv__InputOutputArray__InputOutputArray(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_int_voidX(_flags: i32, _obj: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_MatR(m: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_vectorLMatGR(vec: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_GpuMatR(d_mat: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_BufferR(buf: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_HostMemR(cuda_mem: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_UMatR(m: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_vectorLUMatGR(vec: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_const_vectorLMatGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_const_GpuMatR(d_mat: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_const_vectorLGpuMatGR(d_mat: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_const_BufferR(buf: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_const_HostMemR(cuda_mem: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray__InputOutputArray_const_vectorLUMatGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__InputOutputArray_to__InputArray(instance: *mut c_void) -> *mut c_void;
pub fn cv__InputOutputArray_to__OutputArray(instance: *mut c_void) -> *mut c_void;
pub fn cv__InputOutputArray_delete(instance: *mut c_void);
pub fn cv__OutputArray__OutputArray(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_int_voidX(_flags: i32, _obj: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_MatR(m: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_vectorLMatGR(vec: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_GpuMatR(d_mat: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_vectorLGpuMatGR(d_mat: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_BufferR(buf: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_HostMemR(cuda_mem: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_UMatR(m: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_vectorLUMatGR(vec: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_const_vectorLMatGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_const_GpuMatR(d_mat: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_const_BufferR(buf: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_const_HostMemR(cuda_mem: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray__OutputArray_const_vectorLUMatGR(vec: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_fixedSize_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__OutputArray_fixedType_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__OutputArray_needed_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv__OutputArray_getMatRef_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_getMatRef_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_getUMatRef_const_int(instance: *const c_void, i: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_getUMatRef_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_getGpuMatRef_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_getGpuMatVecRef_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_getOGlBufferRef_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_getHostMemRef_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv__OutputArray_create_const_Size_int_int_bool_DepthMask(instance: *const c_void, sz: *const core::Size, typ: i32, i: i32, allow_transposed: bool, fixed_depth_mask: core::_OutputArray_DepthMask, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_create_const_Size_int(instance: *const c_void, sz: *const core::Size, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_create_const_int_int_int_int_bool_DepthMask(instance: *const c_void, rows: i32, cols: i32, typ: i32, i: i32, allow_transposed: bool, fixed_depth_mask: core::_OutputArray_DepthMask, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_create_const_int_int_int(instance: *const c_void, rows: i32, cols: i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_create_const_int_const_intX_int_int_bool_DepthMask(instance: *const c_void, dims: i32, size: *const i32, typ: i32, i: i32, allow_transposed: bool, fixed_depth_mask: core::_OutputArray_DepthMask, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_create_const_int_const_intX_int(instance: *const c_void, dims: i32, size: *const i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_createSameSize_const_const__InputArrayR_int(instance: *const c_void, arr: *const c_void, mtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_release_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_clear_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_setTo_const_const__InputArrayR_const__InputArrayR(instance: *const c_void, value: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_setTo_const_const__InputArrayR(instance: *const c_void, value: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_assign_const_const_UMatR(instance: *const c_void, u: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_assign_const_const_MatR(instance: *const c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_assign_const_const_vectorLUMatGR(instance: *const c_void, v: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_assign_const_const_vectorLMatGR(instance: *const c_void, v: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_move_const_UMatR(instance: *const c_void, u: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_move_const_MatR(instance: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv__OutputArray_to__InputArray(instance: *mut c_void) -> *mut c_void;
pub fn cv__OutputArray_delete(instance: *mut c_void);
pub fn cv_cuda_BufferPool_BufferPool_StreamR(stream: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_BufferPool_getBuffer_int_int_int(instance: *mut c_void, rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_BufferPool_getBuffer_Size_int(instance: *mut c_void, size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_BufferPool_getAllocator_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_BufferPool_delete(instance: *mut c_void);
pub fn cv_cuda_DeviceInfo_DeviceInfo(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_DeviceInfo_DeviceInfo_int(device_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_DeviceInfo_deviceID_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_name_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_DeviceInfo_totalGlobalMem_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_sharedMemPerBlock_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_regsPerBlock_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_warpSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_memPitch_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_maxThreadsPerBlock_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_maxThreadsDim_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec3i>);
pub fn cv_cuda_DeviceInfo_maxGridSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec3i>);
pub fn cv_cuda_DeviceInfo_clockRate_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_totalConstMem_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_majorVersion_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_minorVersion_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_textureAlignment_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_texturePitchAlignment_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_multiProcessorCount_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_kernelExecTimeoutEnabled_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_integrated_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_canMapHostMemory_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_computeMode_const(instance: *const c_void, ocvrs_return: *mut Result<core::DeviceInfo_ComputeMode>);
pub fn cv_cuda_DeviceInfo_maxTexture1D_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_maxTexture1DMipmap_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_maxTexture1DLinear_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_maxTexture2D_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2i>);
pub fn cv_cuda_DeviceInfo_maxTexture2DMipmap_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2i>);
pub fn cv_cuda_DeviceInfo_maxTexture2DLinear_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec3i>);
pub fn cv_cuda_DeviceInfo_maxTexture2DGather_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2i>);
pub fn cv_cuda_DeviceInfo_maxTexture3D_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec3i>);
pub fn cv_cuda_DeviceInfo_maxTextureCubemap_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_maxTexture1DLayered_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2i>);
pub fn cv_cuda_DeviceInfo_maxTexture2DLayered_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec3i>);
pub fn cv_cuda_DeviceInfo_maxTextureCubemapLayered_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2i>);
pub fn cv_cuda_DeviceInfo_maxSurface1D_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_maxSurface2D_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2i>);
pub fn cv_cuda_DeviceInfo_maxSurface3D_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec3i>);
pub fn cv_cuda_DeviceInfo_maxSurface1DLayered_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2i>);
pub fn cv_cuda_DeviceInfo_maxSurface2DLayered_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec3i>);
pub fn cv_cuda_DeviceInfo_maxSurfaceCubemap_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_maxSurfaceCubemapLayered_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2i>);
pub fn cv_cuda_DeviceInfo_surfaceAlignment_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_concurrentKernels_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_ECCEnabled_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_pciBusID_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_pciDeviceID_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_pciDomainID_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_tccDriver_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_asyncEngineCount_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_unifiedAddressing_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_memoryClockRate_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_memoryBusWidth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_l2CacheSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_maxThreadsPerMultiProcessor_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_DeviceInfo_queryMemory_const_size_tR_size_tR(instance: *const c_void, total_memory: *mut size_t, free_memory: *mut size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_DeviceInfo_freeMemory_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_totalMemory_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_DeviceInfo_supports_const_FeatureSet(instance: *const c_void, feature_set: core::FeatureSet, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_isCompatible_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_DeviceInfo_delete(instance: *mut c_void);
pub fn cv_cuda_Event_Event_CreateFlags(flags: core::Event_CreateFlags, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_Event_Event(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_Event_record_StreamR(instance: *mut c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_Event_record(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_Event_queryIfComplete_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_Event_waitForCompletion(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_Event_elapsedTime_const_EventR_const_EventR(start: *const c_void, end: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_cuda_Event_delete(instance: *mut c_void);
pub fn cv_cuda_GpuData_GpuData_size_t(_size: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuData_propData_const(instance: *const c_void) -> *const u8;
pub fn cv_cuda_GpuData_propData(instance: *mut c_void) -> *mut u8;
pub fn cv_cuda_GpuData_propData_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_cuda_GpuData_propSize_const(instance: *const c_void) -> size_t;
pub fn cv_cuda_GpuData_propSize_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_cuda_GpuData_delete(instance: *mut c_void);
pub fn cv_cuda_GpuMat_defaultAllocator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_setDefaultAllocator_AllocatorX(allocator: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_GpuMat_AllocatorX(allocator: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_int_int_int_AllocatorX(rows: i32, cols: i32, typ: i32, allocator: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_Size_int_AllocatorX(size: *const core::Size, typ: i32, allocator: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_int_int_int_Scalar_AllocatorX(rows: i32, cols: i32, typ: i32, s: *const core::Scalar, allocator: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_int_int_int_Scalar(rows: i32, cols: i32, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_Size_int_Scalar_AllocatorX(size: *const core::Size, typ: i32, s: *const core::Scalar, allocator: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_Size_int_Scalar(size: *const core::Size, typ: i32, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_const_GpuMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_int_int_int_voidX_size_t(rows: i32, cols: i32, typ: i32, data: *mut c_void, step: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_int_int_int_voidX(rows: i32, cols: i32, typ: i32, data: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_Size_int_voidX_size_t(size: *const core::Size, typ: i32, data: *mut c_void, step: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_Size_int_voidX(size: *const core::Size, typ: i32, data: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_const_GpuMatR_Range_Range(m: *const c_void, row_range: *mut c_void, col_range: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_const_GpuMatR_Rect(m: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_const__InputArrayR_AllocatorX(arr: *const c_void, allocator: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_GpuMat_const__InputArrayR(arr: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_operatorST_const_GpuMatR(instance: *mut c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_create_int_int_int(instance: *mut c_void, rows: i32, cols: i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_create_Size_int(instance: *mut c_void, size: *const core::Size, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_swap_GpuMatR(instance: *mut c_void, mat: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_upload_const__InputArrayR(instance: *mut c_void, arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_upload_const__InputArrayR_StreamR(instance: *mut c_void, arr: *const c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_download_const_const__OutputArrayR(instance: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_download_const_const__OutputArrayR_StreamR(instance: *const c_void, dst: *const c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_copyTo_const_const__OutputArrayR(instance: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_copyTo_const_const__OutputArrayR_StreamR(instance: *const c_void, dst: *const c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_copyTo_const_const__OutputArrayR_const__InputArrayR(instance: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_copyTo_const_const__OutputArrayR_const__InputArrayR_StreamR(instance: *const c_void, dst: *const c_void, mask: *const c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_setTo_Scalar(instance: *mut c_void, s: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_setTo_Scalar_StreamR(instance: *mut c_void, s: *const core::Scalar, stream: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_setTo_Scalar_const__InputArrayR(instance: *mut c_void, s: *const core::Scalar, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_setTo_Scalar_const__InputArrayR_StreamR(instance: *mut c_void, s: *const core::Scalar, mask: *const c_void, stream: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_convertTo_const_const__OutputArrayR_int(instance: *const c_void, dst: *const c_void, rtype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_convertTo_const_const__OutputArrayR_int_StreamR(instance: *const c_void, dst: *const c_void, rtype: i32, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_convertTo_const_const__OutputArrayR_int_double_double(instance: *const c_void, dst: *const c_void, rtype: i32, alpha: f64, beta: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_convertTo_const_const__OutputArrayR_int_double(instance: *const c_void, dst: *const c_void, rtype: i32, alpha: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_convertTo_const_const__OutputArrayR_int_double_StreamR(instance: *const c_void, dst: *const c_void, rtype: i32, alpha: f64, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_convertTo_const_const__OutputArrayR_int_double_double_StreamR(instance: *const c_void, dst: *const c_void, rtype: i32, alpha: f64, beta: f64, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_assignTo_const_GpuMatR_int(instance: *const c_void, m: *mut c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_assignTo_const_GpuMatR(instance: *const c_void, m: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_ptr_int(instance: *mut c_void, y: i32, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_cuda_GpuMat_ptr(instance: *mut c_void, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_cuda_GpuMat_ptr_const_int(instance: *const c_void, y: i32, ocvrs_return: *mut Result<*const u8>);
pub fn cv_cuda_GpuMat_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*const u8>);
pub fn cv_cuda_GpuMat_row_const_int(instance: *const c_void, y: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_col_const_int(instance: *const c_void, x: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_rowRange_const_int_int(instance: *const c_void, startrow: i32, endrow: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_rowRange_const_Range(instance: *const c_void, r: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_colRange_const_int_int(instance: *const c_void, startcol: i32, endcol: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_colRange_const_Range(instance: *const c_void, r: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_operator___const_Range_Range(instance: *const c_void, row_range: *mut c_void, col_range: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_operator___const_Rect(instance: *const c_void, roi: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_reshape_const_int_int(instance: *const c_void, cn: i32, rows: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_reshape_const_int(instance: *const c_void, cn: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_locateROI_const_SizeR_PointR(instance: *const c_void, whole_size: *mut core::Size, ofs: *mut core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_adjustROI_int_int_int_int(instance: *mut c_void, dtop: i32, dbottom: i32, dleft: i32, dright: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_isContinuous_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_GpuMat_elemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_GpuMat_elemSize1_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_GpuMat_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_GpuMat_depth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_GpuMat_channels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_GpuMat_step1_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_GpuMat_size_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_cuda_GpuMat_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_GpuMat_cudaPtr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMat_updateContinuityFlag(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_propFlags_const(instance: *const c_void) -> i32;
pub fn cv_cuda_GpuMat_propFlags_const_int(instance: *mut c_void, val: i32);
pub fn cv_cuda_GpuMat_propRows_const(instance: *const c_void) -> i32;
pub fn cv_cuda_GpuMat_propRows_const_int(instance: *mut c_void, val: i32);
pub fn cv_cuda_GpuMat_propCols_const(instance: *const c_void) -> i32;
pub fn cv_cuda_GpuMat_propCols_const_int(instance: *mut c_void, val: i32);
pub fn cv_cuda_GpuMat_propStep_const(instance: *const c_void) -> size_t;
pub fn cv_cuda_GpuMat_propStep_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_cuda_GpuMat_propData_const(instance: *const c_void) -> *const u8;
pub fn cv_cuda_GpuMat_propData(instance: *mut c_void) -> *mut u8;
pub fn cv_cuda_GpuMat_propData_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_cuda_GpuMat_propRefcount_const(instance: *const c_void) -> *const i32;
pub fn cv_cuda_GpuMat_propRefcount(instance: *mut c_void) -> *mut i32;
pub fn cv_cuda_GpuMat_propRefcount_intX(instance: *mut c_void, val: *const i32);
pub fn cv_cuda_GpuMat_propDatastart_const(instance: *const c_void) -> *const u8;
pub fn cv_cuda_GpuMat_propDatastart(instance: *mut c_void) -> *mut u8;
pub fn cv_cuda_GpuMat_propDatastart_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_cuda_GpuMat_propDataend_const(instance: *const c_void) -> *const u8;
pub fn cv_cuda_GpuMat_propAllocator(instance: *mut c_void) -> *mut c_void;
pub fn cv_cuda_GpuMat_propAllocator_AllocatorX(instance: *mut c_void, val: *const c_void);
pub fn cv_cuda_GpuMat_delete(instance: *mut c_void);
pub fn cv_cuda_GpuMat_Allocator_allocate_GpuMatX_int_int_size_t(instance: *mut c_void, mat: *mut c_void, rows: i32, cols: i32, elem_size: size_t, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_GpuMat_Allocator_free_GpuMatX(instance: *mut c_void, mat: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMat_Allocator_delete(instance: *mut c_void);
pub fn cv_cuda_GpuMatND_GpuMatND(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_GpuMatND_SizeArray_int(size: *mut c_void, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_GpuMatND_SizeArray_int_voidX_StepArray(size: *mut c_void, typ: i32, data: *mut c_void, step: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_GpuMatND_SizeArray_int_voidX(size: *mut c_void, typ: i32, data: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_create_SizeArray_int(instance: *mut c_void, size: *mut c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMatND_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMatND_swap_GpuMatNDR(instance: *mut c_void, m: *mut c_void);
pub fn cv_cuda_GpuMatND_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_clone_const_StreamR(instance: *const c_void, stream: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_operator___const_const_vectorLRangeGR(instance: *const c_void, ranges: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_createGpuMatHeader_const_IndexArray_Range_Range(instance: *const c_void, idx: *mut c_void, row_range: *mut c_void, col_range: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_createGpuMatHeader_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_operator___const_IndexArray_Range_Range(instance: *const c_void, idx: *mut c_void, row_range: *mut c_void, col_range: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_operator_cv_cuda_GpuMat_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_GpuMatND_GpuMatND_const_GpuMatNDR(unnamed: *const c_void) -> *mut c_void;
pub fn cv_cuda_GpuMatND_operatorST_const_GpuMatNDR(instance: *mut c_void, unnamed: *const c_void);
pub fn cv_cuda_GpuMatND_GpuMatND_GpuMatNDRR(unnamed: *mut c_void) -> *mut c_void;
pub fn cv_cuda_GpuMatND_operatorST_GpuMatNDRR(instance: *mut c_void, unnamed: *mut c_void);
pub fn cv_cuda_GpuMatND_upload_const__InputArrayR(instance: *mut c_void, src: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMatND_upload_const__InputArrayR_StreamR(instance: *mut c_void, src: *const c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMatND_download_const_const__OutputArrayR(instance: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMatND_download_const_const__OutputArrayR_StreamR(instance: *const c_void, dst: *const c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_GpuMatND_isContinuous_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_GpuMatND_isSubmatrix_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_GpuMatND_elemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_GpuMatND_elemSize1_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_GpuMatND_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_GpuMatND_external_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_GpuMatND_getDevicePtr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_cuda_GpuMatND_total_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_GpuMatND_totalMemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_GpuMatND_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_GpuMatND_propFlags_const(instance: *const c_void) -> i32;
pub fn cv_cuda_GpuMatND_propFlags_const_int(instance: *mut c_void, val: i32);
pub fn cv_cuda_GpuMatND_propDims_const(instance: *const c_void) -> i32;
pub fn cv_cuda_GpuMatND_propDims_const_int(instance: *mut c_void, val: i32);
pub fn cv_cuda_GpuMatND_propSize_const(instance: *const c_void) -> *mut c_void;
pub fn cv_cuda_GpuMatND_propSize_const_SizeArray(instance: *mut c_void, val: *const c_void);
pub fn cv_cuda_GpuMatND_propStep_const(instance: *const c_void) -> *mut c_void;
pub fn cv_cuda_GpuMatND_propStep_const_StepArray(instance: *mut c_void, val: *const c_void);
pub fn cv_cuda_GpuMatND_delete(instance: *mut c_void);
pub fn cv_cuda_HostMem_HostMem_AllocType(alloc_type: core::HostMem_AllocType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_HostMem(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_HostMem_const_HostMemR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_HostMem_int_int_int_AllocType(rows: i32, cols: i32, typ: i32, alloc_type: core::HostMem_AllocType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_HostMem_int_int_int(rows: i32, cols: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_HostMem_Size_int_AllocType(size: *const core::Size, typ: i32, alloc_type: core::HostMem_AllocType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_HostMem_Size_int(size: *const core::Size, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_HostMem_const__InputArrayR_AllocType(arr: *const c_void, alloc_type: core::HostMem_AllocType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_HostMem_const__InputArrayR(arr: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_operatorST_const_HostMemR(instance: *mut c_void, m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_HostMem_swap_HostMemR(instance: *mut c_void, b: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_HostMem_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_create_int_int_int(instance: *mut c_void, rows: i32, cols: i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_HostMem_create_Size_int(instance: *mut c_void, size: *const core::Size, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_HostMem_reshape_const_int_int(instance: *const c_void, cn: i32, rows: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_reshape_const_int(instance: *const c_void, cn: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_HostMem_createMatHeader_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_createGpuMatHeader_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_HostMem_isContinuous_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_HostMem_elemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_HostMem_elemSize1_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_HostMem_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_HostMem_depth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_HostMem_channels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_cuda_HostMem_step1_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_cuda_HostMem_size_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_cuda_HostMem_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_HostMem_propFlags_const(instance: *const c_void) -> i32;
pub fn cv_cuda_HostMem_propFlags_const_int(instance: *mut c_void, val: i32);
pub fn cv_cuda_HostMem_propRows_const(instance: *const c_void) -> i32;
pub fn cv_cuda_HostMem_propRows_const_int(instance: *mut c_void, val: i32);
pub fn cv_cuda_HostMem_propCols_const(instance: *const c_void) -> i32;
pub fn cv_cuda_HostMem_propCols_const_int(instance: *mut c_void, val: i32);
pub fn cv_cuda_HostMem_propStep_const(instance: *const c_void) -> size_t;
pub fn cv_cuda_HostMem_propStep_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_cuda_HostMem_propData_const(instance: *const c_void) -> *const u8;
pub fn cv_cuda_HostMem_propData(instance: *mut c_void) -> *mut u8;
pub fn cv_cuda_HostMem_propData_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_cuda_HostMem_propRefcount_const(instance: *const c_void) -> *const i32;
pub fn cv_cuda_HostMem_propRefcount(instance: *mut c_void) -> *mut i32;
pub fn cv_cuda_HostMem_propRefcount_intX(instance: *mut c_void, val: *const i32);
pub fn cv_cuda_HostMem_propDatastart_const(instance: *const c_void) -> *const u8;
pub fn cv_cuda_HostMem_propDatastart(instance: *mut c_void) -> *mut u8;
pub fn cv_cuda_HostMem_propDatastart_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_cuda_HostMem_propDataend_const(instance: *const c_void) -> *const u8;
pub fn cv_cuda_HostMem_propAlloc_type_const(instance: *const c_void, ocvrs_return: *mut core::HostMem_AllocType);
pub fn cv_cuda_HostMem_propAlloc_type_const_AllocType(instance: *mut c_void, val: core::HostMem_AllocType);
pub fn cv_cuda_HostMem_delete(instance: *mut c_void);
pub fn cv_cuda_Stream_Stream(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_Stream_Stream_const_PtrLAllocatorGR(allocator: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_Stream_Stream_const_size_t(cuda_flags: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_Stream_queryIfComplete_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_Stream_waitForCompletion(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_Stream_waitEvent_const_EventR(instance: *mut c_void, event: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_Stream_enqueueHostCallback_StreamCallback_voidX(instance: *mut c_void, callback: Option<unsafe extern "C" fn(i32, *mut c_void) -> ()>, user_data: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_Stream_Null(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_Stream_cudaPtr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_Stream_delete(instance: *mut c_void);
pub fn cv_cuda_TargetArchs_builtWith_FeatureSet(feature_set: core::FeatureSet, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_TargetArchs_has_int_int(major: i32, minor: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_TargetArchs_hasPtx_int_int(major: i32, minor: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_TargetArchs_hasBin_int_int(major: i32, minor: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_TargetArchs_hasEqualOrLessPtx_int_int(major: i32, minor: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_TargetArchs_hasEqualOrGreater_int_int(major: i32, minor: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_TargetArchs_hasEqualOrGreaterPtx_int_int(major: i32, minor: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_TargetArchs_hasEqualOrGreaterBin_int_int(major: i32, minor: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_cuda_TargetArchs_delete(instance: *mut c_void);
pub fn cv_detail_CheckContext_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CheckContext_defaultNew_const() -> *mut c_void;
pub fn cv_detail_CheckContext_propFunc_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CheckContext_propFile_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CheckContext_propLine_const(instance: *const c_void) -> i32;
pub fn cv_detail_CheckContext_propLine_const_int(instance: *mut c_void, val: i32);
pub fn cv_detail_CheckContext_propTestOp_const(instance: *const c_void, ocvrs_return: *mut core::Detail_TestOp);
pub fn cv_detail_CheckContext_propTestOp_const_TestOp(instance: *mut c_void, val: core::Detail_TestOp);
pub fn cv_detail_CheckContext_propMessage_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CheckContext_propP1_str_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CheckContext_propP2_str_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CheckContext_delete(instance: *mut c_void);
pub fn cv_instr_NodeData_NodeData_const_charX_const_charX_int_voidX_bool_TYPE_IMPL(fun_name: *const c_char, file_name: *const c_char, line_num: i32, ret_address: *mut c_void, always_expand: bool, instr_type: core::TYPE, impl_type: core::IMPL, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_instr_NodeData_NodeData(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_instr_NodeData_NodeData_NodeDataR(ref_: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_instr_NodeData_operatorST_const_NodeDataR(instance: *mut c_void, unnamed: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_instr_NodeData_getTotalMs_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_instr_NodeData_getMeanMs_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_instr_NodeData_propM_funName_const(instance: *const c_void) -> *mut c_void;
pub fn cv_instr_NodeData_propM_funName_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_instr_NodeData_propM_instrType_const(instance: *const c_void, ocvrs_return: *mut core::TYPE);
pub fn cv_instr_NodeData_propM_instrType_const_TYPE(instance: *mut c_void, val: core::TYPE);
pub fn cv_instr_NodeData_propM_implType_const(instance: *const c_void, ocvrs_return: *mut core::IMPL);
pub fn cv_instr_NodeData_propM_implType_const_IMPL(instance: *mut c_void, val: core::IMPL);
pub fn cv_instr_NodeData_propM_fileName_const(instance: *const c_void) -> *mut c_void;
pub fn cv_instr_NodeData_propM_lineNum_const(instance: *const c_void) -> i32;
pub fn cv_instr_NodeData_propM_lineNum_const_int(instance: *mut c_void, val: i32);
pub fn cv_instr_NodeData_propM_retAddress(instance: *mut c_void) -> *mut c_void;
pub fn cv_instr_NodeData_propM_retAddress_voidX(instance: *mut c_void, val: *const c_void);
pub fn cv_instr_NodeData_propM_alwaysExpand_const(instance: *const c_void) -> bool;
pub fn cv_instr_NodeData_propM_alwaysExpand_const_bool(instance: *mut c_void, val: bool);
pub fn cv_instr_NodeData_propM_funError_const(instance: *const c_void) -> bool;
pub fn cv_instr_NodeData_propM_funError_const_bool(instance: *mut c_void, val: bool);
pub fn cv_instr_NodeData_propM_counter_const(instance: *const c_void) -> i32;
pub fn cv_instr_NodeData_propM_counter_const_int(instance: *mut c_void, val: i32);
pub fn cv_instr_NodeData_propM_ticksTotal_const(instance: *const c_void) -> u64;
pub fn cv_instr_NodeData_propM_ticksTotal_const_uint64_t(instance: *mut c_void, val: u64);
pub fn cv_instr_NodeData_propM_threads_const(instance: *const c_void) -> i32;
pub fn cv_instr_NodeData_propM_threads_const_int(instance: *mut c_void, val: i32);
pub fn cv_instr_NodeData_delete(instance: *mut c_void);
pub fn cv_internal_WriteStructContext_WriteStructContext_FileStorageR_const_StringR_int_const_StringR(_fs: *mut c_void, name: *const c_char, flags: i32, type_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_internal_WriteStructContext_WriteStructContext_FileStorageR_const_StringR_int(_fs: *mut c_void, name: *const c_char, flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_internal_WriteStructContext_delete(instance: *mut c_void);
pub fn cv_ocl_Context_Context() -> *mut c_void;
pub fn cv_ocl_Context_Context_int(dtype: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_Context_const_ContextR(c: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_operatorST_const_ContextR(instance: *mut c_void, c: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Context_Context_ContextRR(c: *mut c_void) -> *mut c_void;
pub fn cv_ocl_Context_operatorST_ContextRR(instance: *mut c_void, c: *mut c_void);
pub fn cv_ocl_Context_create(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Context_create_int(instance: *mut c_void, dtype: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Context_ndevices_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Context_device_const_size_t(instance: *const c_void, idx: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_getProg_const_ProgramSourceR_const_StringR_StringR(instance: *mut c_void, prog: *const c_void, buildopt: *const c_char, errmsg: *mut *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_unloadProg_ProgramR(instance: *mut c_void, prog: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Context_getDefault_bool(initialize: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_getDefault(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_getOpenCLContextProperty_const_int(instance: *const c_void, property_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_useSVM_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Context_setUseSVM_bool(instance: *mut c_void, enabled: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Context_fromHandle_voidX(context: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_fromDevice_const_DeviceR(device: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_create_const_stringR(configuration: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Context_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Context_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Context_delete(instance: *mut c_void);
pub fn cv_ocl_Context_UserContext_delete(instance: *mut c_void);
pub fn cv_ocl_Device_Device() -> *mut c_void;
pub fn cv_ocl_Device_Device_voidX(d: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_Device_const_DeviceR(d: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_operatorST_const_DeviceR(instance: *mut c_void, d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Device_Device_DeviceRR(d: *mut c_void) -> *mut c_void;
pub fn cv_ocl_Device_operatorST_DeviceRR(instance: *mut c_void, d: *mut c_void);
pub fn cv_ocl_Device_set_voidX(instance: *mut c_void, d: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Device_name_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_extensions_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_isExtensionSupported_const_const_StringR(instance: *const c_void, extension_name: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_version_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_vendorName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_OpenCL_C_Version_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_OpenCLVersion_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_deviceVersionMajor_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_deviceVersionMinor_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_driverVersion_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_addressBits_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_available_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_compilerAvailable_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_linkerAvailable_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_doubleFPConfig_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_singleFPConfig_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_halfFPConfig_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_endianLittle_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_errorCorrectionSupport_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_executionCapabilities_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_globalMemCacheSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_globalMemCacheType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_globalMemCacheLineSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_globalMemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_localMemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_localMemType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_hostUnifiedMemory_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_imageSupport_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_imageFromBufferSupport_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_imagePitchAlignment_const(instance: *const c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_ocl_Device_imageBaseAddressAlignment_const(instance: *const c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_ocl_Device_intelSubgroupsSupport_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_image2DMaxWidth_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_image2DMaxHeight_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_image3DMaxWidth_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_image3DMaxHeight_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_image3DMaxDepth_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_imageMaxBufferSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_imageMaxArraySize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_vendorID_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_isAMD_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_isIntel_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_isNVidia_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_maxClockFrequency_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_maxComputeUnits_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_maxConstantArgs_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_maxConstantBufferSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_maxMemAllocSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_maxParameterSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_maxReadImageArgs_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_maxWriteImageArgs_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_maxSamplers_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_maxWorkGroupSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_maxWorkItemDims_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_maxWorkItemSizes_const_size_tX(instance: *const c_void, unnamed: *mut size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Device_memBaseAddrAlign_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_nativeVectorWidthChar_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_nativeVectorWidthShort_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_nativeVectorWidthInt_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_nativeVectorWidthLong_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_nativeVectorWidthFloat_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_nativeVectorWidthDouble_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_nativeVectorWidthHalf_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_preferredVectorWidthChar_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_preferredVectorWidthShort_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_preferredVectorWidthInt_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_preferredVectorWidthLong_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_preferredVectorWidthFloat_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_preferredVectorWidthDouble_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_preferredVectorWidthHalf_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Device_printfBufferSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_profilingTimerResolution_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Device_getDefault(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_fromHandle_voidX(d: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Device_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Device_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_ocl_Device_delete(instance: *mut c_void);
pub fn cv_ocl_Image2D_Image2D() -> *mut c_void;
pub fn cv_ocl_Image2D_Image2D_const_UMatR_bool_bool(src: *const c_void, norm: bool, alias: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Image2D_Image2D_const_UMatR(src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Image2D_Image2D_const_Image2DR(i: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Image2D_operatorST_const_Image2DR(instance: *mut c_void, i: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Image2D_Image2D_Image2DRR(unnamed: *mut c_void) -> *mut c_void;
pub fn cv_ocl_Image2D_operatorST_Image2DRR(instance: *mut c_void, unnamed: *mut c_void);
pub fn cv_ocl_Image2D_canCreateAlias_const_UMatR(u: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Image2D_isFormatSupported_int_int_bool(depth: i32, cn: i32, norm: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Image2D_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Image2D_delete(instance: *mut c_void);
pub fn cv_ocl_Kernel_Kernel() -> *mut c_void;
pub fn cv_ocl_Kernel_Kernel_const_charX_const_ProgramR(kname: *const c_char, prog: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Kernel_Kernel_const_charX_const_ProgramSourceR_const_StringR_StringX(kname: *const c_char, prog: *const c_void, buildopts: *const c_char, errmsg: *mut *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Kernel_Kernel_const_charX_const_ProgramSourceR(kname: *const c_char, prog: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Kernel_Kernel_const_KernelR(k: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Kernel_operatorST_const_KernelR(instance: *mut c_void, k: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Kernel_Kernel_KernelRR(k: *mut c_void) -> *mut c_void;
pub fn cv_ocl_Kernel_operatorST_KernelRR(instance: *mut c_void, k: *mut c_void);
pub fn cv_ocl_Kernel_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_create_const_charX_const_ProgramR(instance: *mut c_void, kname: *const c_char, prog: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_create_const_charX_const_ProgramSourceR_const_StringR_StringX(instance: *mut c_void, kname: *const c_char, prog: *const c_void, buildopts: *const c_char, errmsg: *mut *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_create_const_charX_const_ProgramSourceR_const_StringR(instance: *mut c_void, kname: *const c_char, prog: *const c_void, buildopts: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_set_int_const_voidX_size_t(instance: *mut c_void, i: i32, value: *const c_void, sz: size_t, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Kernel_set_int_const_Image2DR(instance: *mut c_void, i: i32, image_2d: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Kernel_set_int_const_UMatR(instance: *mut c_void, i: i32, m: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Kernel_set_int_const_KernelArgR(instance: *mut c_void, i: i32, arg: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_Kernel_run_int_size_tX_size_tX_bool_const_QueueR(instance: *mut c_void, dims: i32, globalsize: *mut size_t, localsize: *mut size_t, sync: bool, q: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_run_int_size_tX_size_tX_bool(instance: *mut c_void, dims: i32, globalsize: *mut size_t, localsize: *mut size_t, sync: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_run__int_size_tX_size_tX_bool_const_QueueR(instance: *mut c_void, dims: i32, globalsize: *mut size_t, localsize: *mut size_t, sync: bool, q: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_run__int_size_tX_size_tX_bool(instance: *mut c_void, dims: i32, globalsize: *mut size_t, localsize: *mut size_t, sync: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_runTask_bool_const_QueueR(instance: *mut c_void, sync: bool, q: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_runTask_bool(instance: *mut c_void, sync: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_runProfiling_int_size_tX_size_tX_const_QueueR(instance: *mut c_void, dims: i32, globalsize: *mut size_t, localsize: *mut size_t, q: *const c_void, ocvrs_return: *mut Result<i64>);
pub fn cv_ocl_Kernel_runProfiling_int_size_tX_size_tX(instance: *mut c_void, dims: i32, globalsize: *mut size_t, localsize: *mut size_t, ocvrs_return: *mut Result<i64>);
pub fn cv_ocl_Kernel_workGroupSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Kernel_preferedWorkGroupSizeMultiple_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Kernel_compileWorkGroupSize_const_size_tX(instance: *const c_void, wsz: *mut size_t, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Kernel_localMemSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_ocl_Kernel_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Kernel_delete(instance: *mut c_void);
pub fn cv_ocl_KernelArg_KernelArg_int_UMatX_int_int_const_voidX_size_t(_flags: i32, _m: *mut c_void, wscale: i32, iwscale: i32, _obj: *const c_void, _sz: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_KernelArg_int_UMatX(_flags: i32, _m: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_KernelArg() -> *mut c_void;
pub fn cv_ocl_KernelArg_Local_size_t(local_mem_size: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_PtrWriteOnly_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_PtrReadOnly_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_PtrReadWrite_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_ReadWrite_const_UMatR_int_int(m: *const c_void, wscale: i32, iwscale: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_ReadWrite_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_ReadWriteNoSize_const_UMatR_int_int(m: *const c_void, wscale: i32, iwscale: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_ReadWriteNoSize_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_ReadOnly_const_UMatR_int_int(m: *const c_void, wscale: i32, iwscale: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_ReadOnly_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_WriteOnly_const_UMatR_int_int(m: *const c_void, wscale: i32, iwscale: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_WriteOnly_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_ReadOnlyNoSize_const_UMatR_int_int(m: *const c_void, wscale: i32, iwscale: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_ReadOnlyNoSize_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_WriteOnlyNoSize_const_UMatR_int_int(m: *const c_void, wscale: i32, iwscale: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_WriteOnlyNoSize_const_UMatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_Constant_const_MatR(m: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_KernelArg_propFlags_const(instance: *const c_void) -> i32;
pub fn cv_ocl_KernelArg_propFlags_const_int(instance: *mut c_void, val: i32);
pub fn cv_ocl_KernelArg_propM(instance: *mut c_void) -> *mut c_void;
pub fn cv_ocl_KernelArg_propM_UMatX(instance: *mut c_void, val: *const c_void);
pub fn cv_ocl_KernelArg_propObj_const(instance: *const c_void) -> *const c_void;
pub fn cv_ocl_KernelArg_propSz_const(instance: *const c_void) -> size_t;
pub fn cv_ocl_KernelArg_propSz_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_ocl_KernelArg_propWscale_const(instance: *const c_void) -> i32;
pub fn cv_ocl_KernelArg_propWscale_const_int(instance: *mut c_void, val: i32);
pub fn cv_ocl_KernelArg_propIwscale_const(instance: *const c_void) -> i32;
pub fn cv_ocl_KernelArg_propIwscale_const_int(instance: *mut c_void, val: i32);
pub fn cv_ocl_KernelArg_delete(instance: *mut c_void);
pub fn cv_ocl_OpenCLExecutionContext_OpenCLExecutionContext() -> *mut c_void;
pub fn cv_ocl_OpenCLExecutionContext_OpenCLExecutionContext_const_OpenCLExecutionContextR(unnamed: *const c_void) -> *mut c_void;
pub fn cv_ocl_OpenCLExecutionContext_OpenCLExecutionContext_OpenCLExecutionContextRR(unnamed: *mut c_void) -> *mut c_void;
pub fn cv_ocl_OpenCLExecutionContext_operatorST_const_OpenCLExecutionContextR(instance: *mut c_void, unnamed: *const c_void);
pub fn cv_ocl_OpenCLExecutionContext_operatorST_OpenCLExecutionContextRR(instance: *mut c_void, unnamed: *mut c_void);
pub fn cv_ocl_OpenCLExecutionContext_getContext_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_getDevice_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_getQueue_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_useOpenCL_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_OpenCLExecutionContext_setUseOpenCL_bool(instance: *mut c_void, flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_OpenCLExecutionContext_getCurrent(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_getCurrentRef(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_bind_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_OpenCLExecutionContext_cloneWithNewQueue_const_const_QueueR(instance: *const c_void, q: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_cloneWithNewQueue_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_create_const_stringR_voidX_voidX_voidX(platform_name: *const c_char, platform_id: *mut c_void, context: *mut c_void, device_id: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_create_const_ContextR_const_DeviceR_const_QueueR(context: *const c_void, device: *const c_void, queue: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_create_const_ContextR_const_DeviceR(context: *const c_void, device: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_OpenCLExecutionContext_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_OpenCLExecutionContext_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_OpenCLExecutionContext_delete(instance: *mut c_void);
pub fn cv_ocl_Platform_Platform() -> *mut c_void;
pub fn cv_ocl_Platform_Platform_const_PlatformR(p: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Platform_operatorST_const_PlatformR(instance: *mut c_void, p: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Platform_Platform_PlatformRR(p: *mut c_void) -> *mut c_void;
pub fn cv_ocl_Platform_operatorST_PlatformRR(instance: *mut c_void, p: *mut c_void);
pub fn cv_ocl_Platform_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Platform_getDefault(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Platform_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Platform_delete(instance: *mut c_void);
pub fn cv_ocl_PlatformInfo_PlatformInfo() -> *mut c_void;
pub fn cv_ocl_PlatformInfo_PlatformInfo_voidX(id: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_PlatformInfo_PlatformInfo_const_PlatformInfoR(i: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_PlatformInfo_operatorST_const_PlatformInfoR(instance: *mut c_void, i: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_PlatformInfo_PlatformInfo_PlatformInfoRR(i: *mut c_void) -> *mut c_void;
pub fn cv_ocl_PlatformInfo_operatorST_PlatformInfoRR(instance: *mut c_void, i: *mut c_void);
pub fn cv_ocl_PlatformInfo_name_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_PlatformInfo_vendor_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_PlatformInfo_version_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_PlatformInfo_versionMajor_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_PlatformInfo_versionMinor_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_PlatformInfo_deviceNumber_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ocl_PlatformInfo_getDevice_const_DeviceR_int(instance: *const c_void, device: *mut c_void, d: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_PlatformInfo_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_PlatformInfo_delete(instance: *mut c_void);
pub fn cv_ocl_Program_Program() -> *mut c_void;
pub fn cv_ocl_Program_Program_const_ProgramSourceR_const_StringR_StringR(src: *const c_void, buildflags: *const c_char, errmsg: *mut *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Program_Program_const_ProgramR(prog: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Program_operatorST_const_ProgramR(instance: *mut c_void, prog: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Program_Program_ProgramRR(prog: *mut c_void) -> *mut c_void;
pub fn cv_ocl_Program_operatorST_ProgramRR(instance: *mut c_void, prog: *mut c_void);
pub fn cv_ocl_Program_create_const_ProgramSourceR_const_StringR_StringR(instance: *mut c_void, src: *const c_void, buildflags: *const c_char, errmsg: *mut *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Program_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Program_getBinary_const_vectorLcharGR(instance: *const c_void, binary: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Program_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Program_read_const_StringR_const_StringR(instance: *mut c_void, buf: *const c_char, buildflags: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Program_write_const_StringR(instance: *const c_void, buf: *mut *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Program_source_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Program_getPrefix_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Program_getPrefix_const_StringR(buildflags: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Program_delete(instance: *mut c_void);
pub fn cv_ocl_ProgramSource_ProgramSource() -> *mut c_void;
pub fn cv_ocl_ProgramSource_ProgramSource_const_StringR_const_StringR_const_StringR_const_StringR(module: *const c_char, name: *const c_char, code_str: *const c_char, code_hash: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_ProgramSource_ProgramSource_const_StringR(prog: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_ProgramSource_ProgramSource_const_ProgramSourceR(prog: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_ProgramSource_operatorST_const_ProgramSourceR(instance: *mut c_void, prog: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_ProgramSource_ProgramSource_ProgramSourceRR(prog: *mut c_void) -> *mut c_void;
pub fn cv_ocl_ProgramSource_operatorST_ProgramSourceRR(instance: *mut c_void, prog: *mut c_void);
pub fn cv_ocl_ProgramSource_source_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_ProgramSource_hash_const(instance: *const c_void, ocvrs_return: *mut Result<core::ProgramSource_hash_t>);
pub fn cv_ocl_ProgramSource_fromBinary_const_StringR_const_StringR_const_unsigned_charX_const_size_t_const_StringR(module: *const c_char, name: *const c_char, binary: *const u8, size: size_t, build_options: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_ProgramSource_fromBinary_const_StringR_const_StringR_const_unsigned_charX_const_size_t(module: *const c_char, name: *const c_char, binary: *const u8, size: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_ProgramSource_fromSPIR_const_StringR_const_StringR_const_unsigned_charX_const_size_t_const_StringR(module: *const c_char, name: *const c_char, binary: *const u8, size: size_t, build_options: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_ProgramSource_fromSPIR_const_StringR_const_StringR_const_unsigned_charX_const_size_t(module: *const c_char, name: *const c_char, binary: *const u8, size: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_ProgramSource_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_ProgramSource_delete(instance: *mut c_void);
pub fn cv_ocl_Queue_Queue() -> *mut c_void;
pub fn cv_ocl_Queue_Queue_const_ContextR_const_DeviceR(c: *const c_void, d: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Queue_Queue_const_ContextR(c: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Queue_Queue_const_QueueR(q: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Queue_operatorST_const_QueueR(instance: *mut c_void, q: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Queue_Queue_QueueRR(q: *mut c_void) -> *mut c_void;
pub fn cv_ocl_Queue_operatorST_QueueRR(instance: *mut c_void, q: *mut c_void);
pub fn cv_ocl_Queue_create_const_ContextR_const_DeviceR(instance: *mut c_void, c: *const c_void, d: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Queue_create(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Queue_finish(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Queue_ptr_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Queue_getDefault(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Queue_getProfilingQueue_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Queue_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ocl_Queue_delete(instance: *mut c_void);
pub fn cv_ocl_Timer_Timer_const_QueueR(q: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ocl_Timer_start(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Timer_stop(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ocl_Timer_durationNS_const(instance: *const c_void, ocvrs_return: *mut Result<u64>);
pub fn cv_ocl_Timer_delete(instance: *mut c_void);
pub fn cv_ogl_Arrays_Arrays(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Arrays_setVertexArray_const__InputArrayR(instance: *mut c_void, vertex: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_resetVertexArray(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_setColorArray_const__InputArrayR(instance: *mut c_void, color: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_resetColorArray(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_setNormalArray_const__InputArrayR(instance: *mut c_void, normal: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_resetNormalArray(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_setTexCoordArray_const__InputArrayR(instance: *mut c_void, tex_coord: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_resetTexCoordArray(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_setAutoRelease_bool(instance: *mut c_void, flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_bind_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Arrays_size_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Arrays_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ogl_Arrays_delete(instance: *mut c_void);
pub fn cv_ogl_Buffer_Buffer(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_int_int_int_unsigned_int_bool(arows: i32, acols: i32, atype: i32, abuf_id: u32, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_int_int_int_unsigned_int(arows: i32, acols: i32, atype: i32, abuf_id: u32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_Size_int_unsigned_int_bool(asize: *const core::Size, atype: i32, abuf_id: u32, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_Size_int_unsigned_int(asize: *const core::Size, atype: i32, abuf_id: u32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_int_int_int_Target_bool(arows: i32, acols: i32, atype: i32, target: core::Buffer_Target, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_int_int_int(arows: i32, acols: i32, atype: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_Size_int_Target_bool(asize: *const core::Size, atype: i32, target: core::Buffer_Target, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_Size_int(asize: *const core::Size, atype: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_const__InputArrayR_Target_bool(arr: *const c_void, target: core::Buffer_Target, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_Buffer_const__InputArrayR(arr: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_create_int_int_int_Target_bool(instance: *mut c_void, arows: i32, acols: i32, atype: i32, target: core::Buffer_Target, auto_release: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_create_int_int_int(instance: *mut c_void, arows: i32, acols: i32, atype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_create_Size_int_Target_bool(instance: *mut c_void, asize: *const core::Size, atype: i32, target: core::Buffer_Target, auto_release: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_create_Size_int(instance: *mut c_void, asize: *const core::Size, atype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_setAutoRelease_bool(instance: *mut c_void, flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_copyFrom_const__InputArrayR_Target_bool(instance: *mut c_void, arr: *const c_void, target: core::Buffer_Target, auto_release: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_copyFrom_const__InputArrayR(instance: *mut c_void, arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_copyFrom_const__InputArrayR_StreamR_Target_bool(instance: *mut c_void, arr: *const c_void, stream: *mut c_void, target: core::Buffer_Target, auto_release: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_copyFrom_const__InputArrayR_StreamR(instance: *mut c_void, arr: *const c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_copyTo_const_const__OutputArrayR(instance: *const c_void, arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_copyTo_const_const__OutputArrayR_StreamR(instance: *const c_void, arr: *const c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_clone_const_Target_bool(instance: *const c_void, target: core::Buffer_Target, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_bind_const_Target(instance: *const c_void, target: core::Buffer_Target, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_unbind_Target(target: core::Buffer_Target, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_mapHost_Access(instance: *mut c_void, access: core::Buffer_Access, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_unmapHost(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_mapDevice(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_unmapDevice(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_mapDevice_StreamR(instance: *mut c_void, stream: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Buffer_unmapDevice_StreamR(instance: *mut c_void, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Buffer_rows_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Buffer_cols_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Buffer_size_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_ogl_Buffer_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ogl_Buffer_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Buffer_depth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Buffer_channels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Buffer_elemSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Buffer_elemSize1_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Buffer_bufId_const(instance: *const c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_ogl_Buffer_delete(instance: *mut c_void);
pub fn cv_ogl_Texture2D_Texture2D(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_int_int_Format_unsigned_int_bool(arows: i32, acols: i32, aformat: core::Texture2D_Format, atex_id: u32, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_int_int_Format_unsigned_int(arows: i32, acols: i32, aformat: core::Texture2D_Format, atex_id: u32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_Size_Format_unsigned_int_bool(asize: *const core::Size, aformat: core::Texture2D_Format, atex_id: u32, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_Size_Format_unsigned_int(asize: *const core::Size, aformat: core::Texture2D_Format, atex_id: u32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_int_int_Format_bool(arows: i32, acols: i32, aformat: core::Texture2D_Format, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_int_int_Format(arows: i32, acols: i32, aformat: core::Texture2D_Format, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_Size_Format_bool(asize: *const core::Size, aformat: core::Texture2D_Format, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_Size_Format(asize: *const core::Size, aformat: core::Texture2D_Format, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_const__InputArrayR_bool(arr: *const c_void, auto_release: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_Texture2D_const__InputArrayR(arr: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ogl_Texture2D_create_int_int_Format_bool(instance: *mut c_void, arows: i32, acols: i32, aformat: core::Texture2D_Format, auto_release: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_create_int_int_Format(instance: *mut c_void, arows: i32, acols: i32, aformat: core::Texture2D_Format, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_create_Size_Format_bool(instance: *mut c_void, asize: *const core::Size, aformat: core::Texture2D_Format, auto_release: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_create_Size_Format(instance: *mut c_void, asize: *const core::Size, aformat: core::Texture2D_Format, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_setAutoRelease_bool(instance: *mut c_void, flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_copyFrom_const__InputArrayR_bool(instance: *mut c_void, arr: *const c_void, auto_release: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_copyFrom_const__InputArrayR(instance: *mut c_void, arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_copyTo_const_const__OutputArrayR_int_bool(instance: *const c_void, arr: *const c_void, ddepth: i32, auto_release: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_copyTo_const_const__OutputArrayR(instance: *const c_void, arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_bind_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ogl_Texture2D_rows_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Texture2D_cols_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ogl_Texture2D_size_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_ogl_Texture2D_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ogl_Texture2D_format_const(instance: *const c_void, ocvrs_return: *mut Result<core::Texture2D_Format>);
pub fn cv_ogl_Texture2D_texId_const(instance: *const c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_ogl_Texture2D_delete(instance: *mut c_void);
pub fn cv_utils_logging_LogTag_LogTag_const_charX_LogLevel(_name: *const c_char, _level: core::LogLevel, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_utils_logging_LogTag_propName_const(instance: *const c_void) -> *mut c_void;
pub fn cv_utils_logging_LogTag_propLevel_const(instance: *const c_void, ocvrs_return: *mut core::LogLevel);
pub fn cv_utils_logging_LogTag_propLevel_const_LogLevel(instance: *mut c_void, val: core::LogLevel);
pub fn cv_utils_logging_LogTag_delete(instance: *mut c_void);
