extern "C" {
	const cv::large_kinfu::LargeKinfu* cv_PtrLcv_large_kinfu_LargeKinfuG_getInnerPtr_const(const cv::Ptr<cv::large_kinfu::LargeKinfu>* instance) {
			return instance->get();
	}
	
	cv::large_kinfu::LargeKinfu* cv_PtrLcv_large_kinfu_LargeKinfuG_getInnerPtrMut(cv::Ptr<cv::large_kinfu::LargeKinfu>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_large_kinfu_LargeKinfuG_delete(cv::Ptr<cv::large_kinfu::LargeKinfu>* instance) {
			delete instance;
	}
	
}

