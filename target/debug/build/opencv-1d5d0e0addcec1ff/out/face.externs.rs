pub fn cv_face_createFacemarkAAM(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_createFacemarkKazemi(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_createFacemarkLBF(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_drawFacemarks_const__InputOutputArrayR_const__InputArrayR(image: *const c_void, points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_drawFacemarks_const__InputOutputArrayR_const__InputArrayR_Scalar(image: *const c_void, points: *const c_void, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_face_getFacesHAAR_const__InputArrayR_const__OutputArrayR_const_StringR(image: *const c_void, faces: *const c_void, face_cascade_name: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_face_getFaces_const__InputArrayR_const__OutputArrayR_CParamsX(image: *const c_void, faces: *const c_void, params: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_loadDatasetList_String_String_vectorLStringGR_vectorLStringGR(image_list: *const c_char, annotation_list: *const c_char, images: *mut c_void, annotations: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_loadFacePoints_String_const__OutputArrayR(filename: *const c_char, points: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_loadFacePoints_String_const__OutputArrayR_float(filename: *const c_char, points: *const c_void, offset: f32, ocvrs_return: *mut Result<bool>);
pub fn cv_face_loadTrainingData_String_String_vectorLStringGR_const__OutputArrayR(image_list: *const c_char, ground_truth: *const c_char, images: *mut c_void, face_points: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_loadTrainingData_String_String_vectorLStringGR_const__OutputArrayR_float(image_list: *const c_char, ground_truth: *const c_char, images: *mut c_void, face_points: *const c_void, offset: f32, ocvrs_return: *mut Result<bool>);
pub fn cv_face_loadTrainingData_String_vectorLStringGR_const__OutputArrayR(filename: *const c_char, images: *mut c_void, face_points: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_loadTrainingData_String_vectorLStringGR_const__OutputArrayR_char_float(filename: *const c_char, images: *mut c_void, face_points: *const c_void, delim: c_char, offset: f32, ocvrs_return: *mut Result<bool>);
pub fn cv_face_loadTrainingData_vectorLStringG_vectorLvectorLPoint2fGGR_vectorLStringGR(filename: *mut c_void, trainlandmarks: *mut c_void, trainimages: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_BIF_getNumBands_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_BIF_getNumRotations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_BIF_compute_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, image: *const c_void, features: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_BIF_create_int_int(num_bands: i32, num_rotations: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_BIF_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_BIF_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_BIF_delete(instance: *mut c_void);
pub fn cv_face_BasicFaceRecognizer_getNumComponents_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_BasicFaceRecognizer_setNumComponents_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_face_BasicFaceRecognizer_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_face_BasicFaceRecognizer_setThreshold_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_face_BasicFaceRecognizer_getProjections_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_BasicFaceRecognizer_getLabels_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_BasicFaceRecognizer_getEigenValues_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_BasicFaceRecognizer_getEigenVectors_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_BasicFaceRecognizer_getMean_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_BasicFaceRecognizer_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_BasicFaceRecognizer_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_BasicFaceRecognizer_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_BasicFaceRecognizer_to_EigenFaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_BasicFaceRecognizer_to_FisherFaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_BasicFaceRecognizer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_BasicFaceRecognizer_to_FaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_BasicFaceRecognizer_delete(instance: *mut c_void);
pub fn cv_face_CParams_CParams_String_double_int_Size_Size(cascade_model: *const c_char, sf: f64, min_n: i32, min_sz: *const core::Size, max_sz: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_CParams_CParams_String(cascade_model: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_CParams_propCascade_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_CParams_propCascade_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_face_CParams_propScaleFactor_const(instance: *const c_void) -> f64;
pub fn cv_face_CParams_propScaleFactor_const_double(instance: *mut c_void, val: f64);
pub fn cv_face_CParams_propMinNeighbors_const(instance: *const c_void) -> i32;
pub fn cv_face_CParams_propMinNeighbors_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_CParams_propMinSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_face_CParams_propMinSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_face_CParams_propMaxSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_face_CParams_propMaxSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_face_CParams_propFace_cascade_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_CParams_propFace_cascade_const_CascadeClassifier(instance: *mut c_void, val: *const c_void);
pub fn cv_face_CParams_delete(instance: *mut c_void);
pub fn cv_face_EigenFaceRecognizer_create_int_double(num_components: i32, threshold: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_EigenFaceRecognizer_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_EigenFaceRecognizer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_EigenFaceRecognizer_to_BasicFaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_EigenFaceRecognizer_to_FaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_EigenFaceRecognizer_delete(instance: *mut c_void);
pub fn cv_face_FaceRecognizer_train_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, labels: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_update_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, labels: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_predict_const_const__InputArrayR(instance: *const c_void, src: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_FaceRecognizer_predict_const_const__InputArrayR_intR_doubleR(instance: *const c_void, src: *const c_void, label: *mut i32, confidence: *mut f64, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_predict_const_const__InputArrayR_PtrLPredictCollectorG(instance: *const c_void, src: *const c_void, collector: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_write_const_const_StringR(instance: *const c_void, filename: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_read_const_StringR(instance: *mut c_void, filename: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FaceRecognizer_setLabelInfo_int_const_StringR(instance: *mut c_void, label: i32, str_info: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_getLabelInfo_const_int(instance: *const c_void, label: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FaceRecognizer_getLabelsByString_const_const_StringR(instance: *const c_void, str: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FaceRecognizer_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_face_FaceRecognizer_setThreshold_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FaceRecognizer_to_BasicFaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FaceRecognizer_to_EigenFaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FaceRecognizer_to_FisherFaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FaceRecognizer_to_LBPHFaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FaceRecognizer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FaceRecognizer_delete(instance: *mut c_void);
pub fn cv_face_Facemark_loadModel_String(instance: *mut c_void, model: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_face_Facemark_fit_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, faces: *const c_void, landmarks: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_Facemark_to_FacemarkAAM(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_Facemark_to_FacemarkKazemi(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_Facemark_to_FacemarkLBF(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_Facemark_to_FacemarkTrain(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_Facemark_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_Facemark_delete(instance: *mut c_void);
pub fn cv_face_FacemarkAAM_fitConfig_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const_vectorLConfigGR(instance: *mut c_void, image: *const c_void, roi: *const c_void, _landmarks: *const c_void, runtime_params: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkAAM_create_const_ParamsR(parameters: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkAAM_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkAAM_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_to_Facemark(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_to_FacemarkTrain(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_delete(instance: *mut c_void);
pub fn cv_face_FacemarkAAM_Config_Config_Mat_Point2f_float_int(rot: *mut c_void, trans: *const core::Point2f, scaling: f32, scale_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkAAM_Config_Config(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkAAM_Config_propR_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Config_propR_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Config_propT_const(instance: *const c_void, ocvrs_return: *mut core::Point2f);
pub fn cv_face_FacemarkAAM_Config_propT_const_Point2f(instance: *mut c_void, val: *const core::Point2f);
pub fn cv_face_FacemarkAAM_Config_propScale_const(instance: *const c_void) -> f32;
pub fn cv_face_FacemarkAAM_Config_propScale_const_float(instance: *mut c_void, val: f32);
pub fn cv_face_FacemarkAAM_Config_propModel_scale_idx_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkAAM_Config_propModel_scale_idx_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkAAM_Config_delete(instance: *mut c_void);
pub fn cv_face_FacemarkAAM_Data_propS0_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Data_propS0_const_vectorLPoint2fG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Data_delete(instance: *mut c_void);
pub fn cv_face_FacemarkAAM_Model_propScales_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_propScales_const_vectorLfloatG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_propTriangles_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_propTriangles_const_vectorLVec3iG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_propTextures_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_propTextures_const_vectorLTextureG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_propS0_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_propS0_const_vectorLPoint2fG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_propS_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_propS_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_propQ_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_propQ_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_delete(instance: *mut c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_propMax_m_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkAAM_Model_Texture_propMax_m_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkAAM_Model_Texture_propResolution_const(instance: *const c_void, ocvrs_return: *mut core::Rect);
pub fn cv_face_FacemarkAAM_Model_Texture_propResolution_const_Rect(instance: *mut c_void, val: *const core::Rect);
pub fn cv_face_FacemarkAAM_Model_Texture_propA_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_Texture_propA_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_propA0_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_Texture_propA0_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_propAA_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_Texture_propAA_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_propAA0_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_Texture_propAA0_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_propTextureIdx_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_Texture_propTextureIdx_const_vectorLvectorLPointGG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_propBase_shape_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_Texture_propBase_shape_const_vectorLPoint2fG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_propInd1_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_Texture_propInd1_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_propInd2_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Model_Texture_propInd2_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Model_Texture_delete(instance: *mut c_void);
pub fn cv_face_FacemarkAAM_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkAAM_Params_read_const_FileNodeR(instance: *mut c_void, unnamed: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FacemarkAAM_Params_write_const_FileStorageR(instance: *const c_void, unnamed: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FacemarkAAM_Params_propModel_filename_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Params_propModel_filename_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_face_FacemarkAAM_Params_propM_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkAAM_Params_propM_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkAAM_Params_propN_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkAAM_Params_propN_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkAAM_Params_propN_iter_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkAAM_Params_propN_iter_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkAAM_Params_propVerbose_const(instance: *const c_void) -> bool;
pub fn cv_face_FacemarkAAM_Params_propVerbose_const_bool(instance: *mut c_void, val: bool);
pub fn cv_face_FacemarkAAM_Params_propSave_model_const(instance: *const c_void) -> bool;
pub fn cv_face_FacemarkAAM_Params_propSave_model_const_bool(instance: *mut c_void, val: bool);
pub fn cv_face_FacemarkAAM_Params_propMax_m_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkAAM_Params_propMax_m_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkAAM_Params_propMax_n_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkAAM_Params_propMax_n_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkAAM_Params_propTexture_max_m_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkAAM_Params_propTexture_max_m_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkAAM_Params_propScales_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkAAM_Params_propScales_const_vectorLfloatG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkAAM_Params_delete(instance: *mut c_void);
pub fn cv_face_FacemarkKazemi_create_const_ParamsR(parameters: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkKazemi_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkKazemi_training_vectorLMatGR_vectorLvectorLPoint2fGGR_string_Size_string(instance: *mut c_void, images: *mut c_void, landmarks: *mut c_void, configfile: *const c_char, scale: *const core::Size, model_filename: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkKazemi_training_vectorLMatGR_vectorLvectorLPoint2fGGR_string_Size(instance: *mut c_void, images: *mut c_void, landmarks: *mut c_void, configfile: *const c_char, scale: *const core::Size, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkKazemi_setFaceDetector_bool__X__const_cv__InputArrayR__const_cv__OutputArrayR__voidX__voidX(instance: *mut c_void, f: Option<unsafe extern "C" fn(*const c_void, *const c_void, *mut c_void) -> bool>, user_data: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkKazemi_getFaces_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, faces: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkKazemi_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkKazemi_to_Facemark(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkKazemi_delete(instance: *mut c_void);
pub fn cv_face_FacemarkKazemi_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkKazemi_Params_propCascade_depth_const(instance: *const c_void) -> u32;
pub fn cv_face_FacemarkKazemi_Params_propCascade_depth_const_unsigned_long(instance: *mut c_void, val: u32);
pub fn cv_face_FacemarkKazemi_Params_propTree_depth_const(instance: *const c_void) -> u32;
pub fn cv_face_FacemarkKazemi_Params_propTree_depth_const_unsigned_long(instance: *mut c_void, val: u32);
pub fn cv_face_FacemarkKazemi_Params_propNum_trees_per_cascade_level_const(instance: *const c_void) -> u32;
pub fn cv_face_FacemarkKazemi_Params_propNum_trees_per_cascade_level_const_unsigned_long(instance: *mut c_void, val: u32);
pub fn cv_face_FacemarkKazemi_Params_propLearning_rate_const(instance: *const c_void) -> f32;
pub fn cv_face_FacemarkKazemi_Params_propLearning_rate_const_float(instance: *mut c_void, val: f32);
pub fn cv_face_FacemarkKazemi_Params_propOversampling_amount_const(instance: *const c_void) -> u32;
pub fn cv_face_FacemarkKazemi_Params_propOversampling_amount_const_unsigned_long(instance: *mut c_void, val: u32);
pub fn cv_face_FacemarkKazemi_Params_propNum_test_coordinates_const(instance: *const c_void) -> u32;
pub fn cv_face_FacemarkKazemi_Params_propNum_test_coordinates_const_unsigned_long(instance: *mut c_void, val: u32);
pub fn cv_face_FacemarkKazemi_Params_propLambda_const(instance: *const c_void) -> f32;
pub fn cv_face_FacemarkKazemi_Params_propLambda_const_float(instance: *mut c_void, val: f32);
pub fn cv_face_FacemarkKazemi_Params_propNum_test_splits_const(instance: *const c_void) -> u32;
pub fn cv_face_FacemarkKazemi_Params_propNum_test_splits_const_unsigned_long(instance: *mut c_void, val: u32);
pub fn cv_face_FacemarkKazemi_Params_propConfigfile_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkKazemi_Params_propConfigfile_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_face_FacemarkKazemi_Params_delete(instance: *mut c_void);
pub fn cv_face_FacemarkLBF_create_const_ParamsR(parameters: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkLBF_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkLBF_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkLBF_to_Facemark(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkLBF_to_FacemarkTrain(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkLBF_delete(instance: *mut c_void);
pub fn cv_face_FacemarkLBF_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FacemarkLBF_Params_read_const_FileNodeR(instance: *mut c_void, unnamed: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FacemarkLBF_Params_write_const_FileStorageR(instance: *const c_void, unnamed: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FacemarkLBF_Params_propShape_offset_const(instance: *const c_void) -> f64;
pub fn cv_face_FacemarkLBF_Params_propShape_offset_const_double(instance: *mut c_void, val: f64);
pub fn cv_face_FacemarkLBF_Params_propCascade_face_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkLBF_Params_propCascade_face_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_face_FacemarkLBF_Params_propVerbose_const(instance: *const c_void) -> bool;
pub fn cv_face_FacemarkLBF_Params_propVerbose_const_bool(instance: *mut c_void, val: bool);
pub fn cv_face_FacemarkLBF_Params_propN_landmarks_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkLBF_Params_propN_landmarks_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkLBF_Params_propInitShape_n_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkLBF_Params_propInitShape_n_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkLBF_Params_propStages_n_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkLBF_Params_propStages_n_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkLBF_Params_propTree_n_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkLBF_Params_propTree_n_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkLBF_Params_propTree_depth_const(instance: *const c_void) -> i32;
pub fn cv_face_FacemarkLBF_Params_propTree_depth_const_int(instance: *mut c_void, val: i32);
pub fn cv_face_FacemarkLBF_Params_propBagging_overlap_const(instance: *const c_void) -> f64;
pub fn cv_face_FacemarkLBF_Params_propBagging_overlap_const_double(instance: *mut c_void, val: f64);
pub fn cv_face_FacemarkLBF_Params_propModel_filename_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkLBF_Params_propModel_filename_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_face_FacemarkLBF_Params_propSave_model_const(instance: *const c_void) -> bool;
pub fn cv_face_FacemarkLBF_Params_propSave_model_const_bool(instance: *mut c_void, val: bool);
pub fn cv_face_FacemarkLBF_Params_propSeed_const(instance: *const c_void) -> u32;
pub fn cv_face_FacemarkLBF_Params_propSeed_const_unsigned_int(instance: *mut c_void, val: u32);
pub fn cv_face_FacemarkLBF_Params_propFeats_m_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkLBF_Params_propFeats_m_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkLBF_Params_propRadius_m_const(instance: *const c_void) -> *mut c_void;
pub fn cv_face_FacemarkLBF_Params_propRadius_m_const_vectorLdoubleG(instance: *mut c_void, val: *const c_void);
pub fn cv_face_FacemarkLBF_Params_propDetectROI_const(instance: *const c_void, ocvrs_return: *mut core::Rect);
pub fn cv_face_FacemarkLBF_Params_propDetectROI_const_Rect(instance: *mut c_void, val: *const core::Rect);
pub fn cv_face_FacemarkLBF_Params_delete(instance: *mut c_void);
pub fn cv_face_FacemarkTrain_addTrainingSample_const__InputArrayR_const__InputArrayR(instance: *mut c_void, image: *const c_void, landmarks: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkTrain_training_voidX(instance: *mut c_void, parameters: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FacemarkTrain_training(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_FacemarkTrain_setFaceDetector_FN_FaceDetector_voidX(instance: *mut c_void, detector: Option<unsafe extern "C" fn(*const c_void, *const c_void, *mut c_void) -> bool>, user_data: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkTrain_getFaces_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, faces: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkTrain_getData_voidX(instance: *mut c_void, items: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkTrain_getData(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_FacemarkTrain_to_FacemarkAAM(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkTrain_to_FacemarkLBF(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkTrain_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkTrain_to_Facemark(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FacemarkTrain_delete(instance: *mut c_void);
pub fn cv_face_FisherFaceRecognizer_create_int_double(num_components: i32, threshold: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FisherFaceRecognizer_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_FisherFaceRecognizer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FisherFaceRecognizer_to_BasicFaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FisherFaceRecognizer_to_FaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_FisherFaceRecognizer_delete(instance: *mut c_void);
pub fn cv_face_LBPHFaceRecognizer_getGridX_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_LBPHFaceRecognizer_setGridX_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_face_LBPHFaceRecognizer_getGridY_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_LBPHFaceRecognizer_setGridY_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_face_LBPHFaceRecognizer_getRadius_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_LBPHFaceRecognizer_setRadius_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_face_LBPHFaceRecognizer_getNeighbors_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_LBPHFaceRecognizer_setNeighbors_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_face_LBPHFaceRecognizer_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_face_LBPHFaceRecognizer_setThreshold_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_face_LBPHFaceRecognizer_getHistograms_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_LBPHFaceRecognizer_getLabels_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_LBPHFaceRecognizer_create_int_int_int_int_double(radius: i32, neighbors: i32, grid_x: i32, grid_y: i32, threshold: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_LBPHFaceRecognizer_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_LBPHFaceRecognizer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_LBPHFaceRecognizer_to_FaceRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_LBPHFaceRecognizer_delete(instance: *mut c_void);
pub fn cv_face_MACE_salt_const_StringR(instance: *mut c_void, passphrase: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_face_MACE_train_const__InputArrayR(instance: *mut c_void, images: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_face_MACE_same_const_const__InputArrayR(instance: *const c_void, query: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_face_MACE_load_const_StringR_const_StringR(filename: *const c_char, objname: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_MACE_load_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_MACE_create_int(imgsize: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_MACE_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_MACE_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_MACE_delete(instance: *mut c_void);
pub fn cv_face_PredictCollector_init_size_t(instance: *mut c_void, size: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_face_PredictCollector_collect_int_double(instance: *mut c_void, label: i32, dist: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_face_PredictCollector_to_StandardCollector(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_PredictCollector_delete(instance: *mut c_void);
pub fn cv_face_StandardCollector_StandardCollector_double(threshold_: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_StandardCollector_StandardCollector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_StandardCollector_init_size_t(instance: *mut c_void, size: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_face_StandardCollector_collect_int_double(instance: *mut c_void, label: i32, dist: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_face_StandardCollector_getMinLabel_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_face_StandardCollector_getMinDist_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_face_StandardCollector_getResults_const_bool(instance: *const c_void, sorted: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_StandardCollector_getResults_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_StandardCollector_create_double(threshold: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_StandardCollector_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_face_StandardCollector_to_PredictCollector(instance: *mut c_void) -> *mut c_void;
pub fn cv_face_StandardCollector_delete(instance: *mut c_void);
pub fn cv_face_StandardCollector_PredictResult_PredictResult_int_double(label_: i32, distance_: f64, ocvrs_return: *mut Result<crate::face::StandardCollector_PredictResult>);
pub fn cv_face_StandardCollector_PredictResult_PredictResult(ocvrs_return: *mut Result<crate::face::StandardCollector_PredictResult>);
