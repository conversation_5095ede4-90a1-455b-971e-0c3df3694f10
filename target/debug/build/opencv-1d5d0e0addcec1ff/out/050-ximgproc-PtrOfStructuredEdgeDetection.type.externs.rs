pub fn cv_PtrLcv_ximgproc_StructuredEdgeDetectionG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_StructuredEdgeDetectionG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_StructuredEdgeDetectionG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_StructuredEdgeDetectionG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
