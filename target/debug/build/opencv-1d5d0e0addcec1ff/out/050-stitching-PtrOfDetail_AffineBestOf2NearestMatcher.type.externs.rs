pub fn cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_to_PtrOfDetail_BestOf2NearestMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_to_PtrOfDetail_FeaturesMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_new_const_AffineBestOf2NearestMatcher(val: *mut c_void) -> *mut c_void;
