pub fn cv_PtrLcv_text_OCRHMMDecoder_ClassifierCallbackG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRHMMDecoder_ClassifierCallbackG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRHMMDecoder_ClassifierCallbackG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_text_OCRHMMDecoder_ClassifierCallbackG_new_const_ClassifierCallback(val: *mut c_void) -> *mut c_void;
