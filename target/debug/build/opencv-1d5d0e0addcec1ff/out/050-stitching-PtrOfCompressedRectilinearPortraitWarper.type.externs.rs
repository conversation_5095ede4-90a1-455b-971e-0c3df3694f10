pub fn cv_PtrLcv_CompressedRectilinearPortraitWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_CompressedRectilinearPortraitWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_CompressedRectilinearPortraitWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_CompressedRectilinearPortraitWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_CompressedRectilinearPortraitWarperG_new_const_CompressedRectilinearPortraitWarper(val: *mut c_void) -> *mut c_void;
