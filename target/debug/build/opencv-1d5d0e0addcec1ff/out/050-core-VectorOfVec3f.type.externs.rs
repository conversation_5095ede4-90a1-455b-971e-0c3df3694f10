pub fn std_vectorLcv_Vec3fG_new_const() -> *mut c_void;
pub fn std_vectorLcv_Vec3fG_delete(instance: *mut c_void);
pub fn std_vectorLcv_Vec3fG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec3fG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_Vec3fG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec3fG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_Vec3fG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_Vec3fG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_Vec3fG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_Vec3fG_clear(instance: *mut c_void);
pub fn std_vectorLcv_Vec3fG_push_const_Vec3f(instance: *mut c_void, val: *const core::Vec3f);
pub fn std_vectorLcv_Vec3fG_insert_size_t_const_Vec3f(instance: *mut c_void, index: size_t, val: *const core::Vec3f);
pub fn std_vectorLcv_Vec3fG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Vec3f);
pub fn std_vectorLcv_Vec3fG_set_size_t_const_Vec3f(instance: *mut c_void, index: size_t, val: *const core::Vec3f);
pub fn std_vectorLcv_Vec3fG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_Vec3fG_data_const(instance: *const c_void) -> *const core::Vec3f;
pub fn std_vectorLcv_Vec3fG_dataMut(instance: *mut c_void) -> *mut core::Vec3f;
pub fn cv_fromSlice_const_const_Vec3fX_size_t(data: *const core::Vec3f, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_Vec3fG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec3fG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec3fG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
