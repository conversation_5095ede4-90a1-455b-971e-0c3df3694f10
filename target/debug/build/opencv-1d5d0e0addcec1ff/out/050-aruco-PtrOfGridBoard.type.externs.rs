pub fn cv_PtrLcv_aruco_GridBoardG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_aruco_GridBoardG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_aruco_GridBoardG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_aruco_GridBoardG_to_PtrOfBoard(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_aruco_GridBoardG_new_const_GridBoard(val: *mut c_void) -> *mut c_void;
