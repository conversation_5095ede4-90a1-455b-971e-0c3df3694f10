pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_new_const() -> *mut c_void;
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_delete(instance: *mut c_void);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_clear(instance: *mut c_void);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_push_const_Pose3DPtr(instance: *mut c_void, val: *const c_void);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_insert_size_t_const_Pose3DPtr(instance: *mut c_void, index: size_t, val: *const c_void);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut *mut c_void);
pub fn std_vectorLcv_ppf_match_3d_Pose3DPtrG_set_size_t_const_Pose3DPtr(instance: *mut c_void, index: size_t, val: *const c_void);
