pub fn cv_PtrLcv_detail_GainCompensatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_GainCompensatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_GainCompensatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_GainCompensatorG_to_PtrOfDetail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_GainCompensatorG_new_const_GainCompensator(val: *mut c_void) -> *mut c_void;
