pub fn cv_PtrLcv_detail_BestOf2NearestMatcherG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BestOf2NearestMatcherG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BestOf2NearestMatcherG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_BestOf2NearestMatcherG_to_PtrOfDetail_FeaturesMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BestOf2NearestMatcherG_new_const_BestOf2NearestMatcher(val: *mut c_void) -> *mut c_void;
