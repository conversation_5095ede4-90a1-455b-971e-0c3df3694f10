pub fn cv_PtrLcv_text_OCRBeamSearchDecoder_ClassifierCallbackG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRBeamSearchDecoder_ClassifierCallbackG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRBeamSearchDecoder_ClassifierCallbackG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_text_OCRBeamSearchDecoder_ClassifierCallbackG_new_const_ClassifierCallback(val: *mut c_void) -> *mut c_void;
