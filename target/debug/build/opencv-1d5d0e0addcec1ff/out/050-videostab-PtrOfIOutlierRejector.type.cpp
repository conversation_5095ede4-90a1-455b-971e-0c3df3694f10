extern "C" {
	const cv::videostab::IOutlierRejector* cv_PtrLcv_videostab_IOutlierRejectorG_getInnerPtr_const(const cv::Ptr<cv::videostab::IOutlierRejector>* instance) {
			return instance->get();
	}
	
	cv::videostab::IOutlierRejector* cv_PtrLcv_videostab_IOutlierRejectorG_getInnerPtrMut(cv::Ptr<cv::videostab::IOutlierRejector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_IOutlierRejectorG_delete(cv::Ptr<cv::videostab::IOutlierRejector>* instance) {
			delete instance;
	}
	
}

