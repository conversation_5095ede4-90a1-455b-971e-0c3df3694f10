pub fn std_vectorLcv_detail_CameraParamsG_new_const() -> *mut c_void;
pub fn std_vectorLcv_detail_CameraParamsG_delete(instance: *mut c_void);
pub fn std_vectorLcv_detail_CameraParamsG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_detail_CameraParamsG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_detail_CameraParamsG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_detail_CameraParamsG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_detail_CameraParamsG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_detail_CameraParamsG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_detail_CameraParamsG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_detail_CameraParamsG_clear(instance: *mut c_void);
pub fn std_vectorLcv_detail_CameraParamsG_push_const_CameraParams(instance: *mut c_void, val: *const c_void);
pub fn std_vectorLcv_detail_CameraParamsG_insert_size_t_const_CameraParams(instance: *mut c_void, index: size_t, val: *const c_void);
pub fn std_vectorLcv_detail_CameraParamsG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut *mut c_void);
pub fn std_vectorLcv_detail_CameraParamsG_set_size_t_const_CameraParams(instance: *mut c_void, index: size_t, val: *const c_void);
