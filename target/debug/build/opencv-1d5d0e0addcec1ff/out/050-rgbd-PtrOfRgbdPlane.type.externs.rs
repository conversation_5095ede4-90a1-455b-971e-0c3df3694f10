pub fn cv_PtrLcv_rgbd_RgbdPlaneG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_RgbdPlaneG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_RgbdPlaneG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_rgbd_RgbdPlaneG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_RgbdPlaneG_new_const_RgbdPlane(val: *mut c_void) -> *mut c_void;
