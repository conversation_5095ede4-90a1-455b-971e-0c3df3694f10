pub fn std_vectorLstd_vectorLcv_MatGG_new_const() -> *mut c_void;
pub fn std_vectorLstd_vectorLcv_MatGG_delete(instance: *mut c_void);
pub fn std_vectorLstd_vectorLcv_MatGG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLstd_vectorLcv_MatGG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLstd_vectorLcv_MatGG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLstd_vectorLcv_MatGG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLstd_vectorLcv_MatGG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLstd_vectorLcv_MatGG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLstd_vectorLcv_MatGG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLstd_vectorLcv_MatGG_clear(instance: *mut c_void);
pub fn std_vectorLstd_vectorLcv_MatGG_push_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn std_vectorLstd_vectorLcv_MatGG_insert_size_t_const_vectorLMatG(instance: *mut c_void, index: size_t, val: *const c_void);
pub fn std_vectorLstd_vectorLcv_MatGG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut *mut c_void);
pub fn std_vectorLstd_vectorLcv_MatGG_set_size_t_const_vectorLMatG(instance: *mut c_void, index: size_t, val: *const c_void);
