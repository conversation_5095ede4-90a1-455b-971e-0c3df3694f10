extern "C" {
	std::pair<cv::dnn::Backend, cv::dnn::Target>* std_pairLcv_dnn_Backend__cv_dnn_TargetG_new_const_Backend_Target(cv::dnn::Backend arg, cv::dnn::Target arg_1) {
			std::pair<cv::dnn::Backend, cv::dnn::Target>* ret = new std::pair<cv::dnn::Backend, cv::dnn::Target>(arg, arg_1);
			return ret;
	}
	
	void std_pairLcv_dnn_Backend__cv_dnn_TargetG_get_0_const(const std::pair<cv::dnn::Backend, cv::dnn::Target>* instance, cv::dnn::Backend* ocvrs_return) {
			cv::dnn::Backend ret = std::get<0>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_dnn_Backend__cv_dnn_TargetG_get_1_const(const std::pair<cv::dnn::Backend, cv::dnn::Target>* instance, cv::dnn::Target* ocvrs_return) {
			cv::dnn::Target ret = std::get<1>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_dnn_Backend__cv_dnn_TargetG_delete(std::pair<cv::dnn::Backend, cv::dnn::Target>* instance) {
			delete instance;
	}
	
}

