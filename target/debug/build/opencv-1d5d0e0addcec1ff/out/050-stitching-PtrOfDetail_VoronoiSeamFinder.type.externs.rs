pub fn cv_PtrLcv_detail_VoronoiSeamFinderG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_VoronoiSeamFinderG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_VoronoiSeamFinderG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_VoronoiSeamFinderG_to_PtrOfDetail_PairwiseSeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_VoronoiSeamFinderG_to_PtrOfDetail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_VoronoiSeamFinderG_new_const_VoronoiSeamFinder(val: *mut c_void) -> *mut c_void;
