pub fn cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_to_PtrOfImageMotionEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_new_const_KeypointBasedMotionEstimator(val: *mut c_void) -> *mut c_void;
