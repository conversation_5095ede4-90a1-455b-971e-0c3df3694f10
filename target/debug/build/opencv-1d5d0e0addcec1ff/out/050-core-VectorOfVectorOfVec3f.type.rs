pub type VectorOfVectorOfVec3f = core::Vector<core::Vector<core::Vec3f>>;

impl core::Vector<core::Vector<core::Vec3f>> {
	pub fn as_raw_VectorOfVectorOfVec3f(&self) -> extern_send!(Self) { self.as_raw() }
	pub fn as_raw_mut_VectorOfVectorOfVec3f(&mut self) -> extern_send!(mut Self) { self.as_raw_mut() }
}

vector_extern! { core::Vector<core::Vec3f>,
	std_vectorLstd_vectorLcv_Vec3fGG_new_const, std_vectorLstd_vectorLcv_Vec3fGG_delete,
	std_vectorLstd_vectorLcv_Vec3fGG_len_const, std_vectorLstd_vectorLcv_Vec3fGG_isEmpty_const,
	std_vectorLstd_vectorLcv_Vec3fGG_capacity_const, std_vectorLstd_vectorLcv_Vec3fGG_shrinkToFit,
	std_vectorLstd_vectorLcv_Vec3fGG_reserve_size_t, std_vectorLstd_vectorLcv_Vec3fGG_remove_size_t,
	std_vectorLstd_vectorLcv_Vec3fGG_swap_size_t_size_t, std_vectorLstd_vectorLcv_Vec3fGG_clear,
	std_vectorLstd_vectorLcv_Vec3fGG_get_const_size_t, std_vectorLstd_vectorLcv_Vec3fGG_set_size_t_const_vectorLVec3fG,
	std_vectorLstd_vectorLcv_Vec3fGG_push_const_vectorLVec3fG, std_vectorLstd_vectorLcv_Vec3fGG_insert_size_t_const_vectorLVec3fG,
}
vector_non_copy_or_bool! { clone core::Vector<core::Vec3f> }

impl core::ToInputArray for core::Vector<core::Vector<core::Vec3f>> {
	#[inline]
	fn input_array(&self) -> Result<core::_InputArray> {
		return_send!(via ocvrs_return);
		unsafe { sys::std_vectorLstd_vectorLcv_Vec3fGG_inputArray_const(self.as_raw_VectorOfVectorOfVec3f(), ocvrs_return.as_mut_ptr()) };
		return_receive!(unsafe ocvrs_return => ret);
		let ret = ret.into_result()?;
		let ret = unsafe { core::_InputArray::opencv_from_extern(ret) };
		Ok(ret)
	}
	
}

input_array_ref_forward! { core::Vector<core::Vector<core::Vec3f>> }

impl core::ToOutputArray for core::Vector<core::Vector<core::Vec3f>> {
	#[inline]
	fn output_array(&mut self) -> Result<core::_OutputArray> {
		return_send!(via ocvrs_return);
		unsafe { sys::std_vectorLstd_vectorLcv_Vec3fGG_outputArray(self.as_raw_mut_VectorOfVectorOfVec3f(), ocvrs_return.as_mut_ptr()) };
		return_receive!(unsafe ocvrs_return => ret);
		let ret = ret.into_result()?;
		let ret = unsafe { core::_OutputArray::opencv_from_extern(ret) };
		Ok(ret)
	}
	
}

impl core::ToInputOutputArray for core::Vector<core::Vector<core::Vec3f>> {
	#[inline]
	fn input_output_array(&mut self) -> Result<core::_InputOutputArray> {
		return_send!(via ocvrs_return);
		unsafe { sys::std_vectorLstd_vectorLcv_Vec3fGG_inputOutputArray(self.as_raw_mut_VectorOfVectorOfVec3f(), ocvrs_return.as_mut_ptr()) };
		return_receive!(unsafe ocvrs_return => ret);
		let ret = ret.into_result()?;
		let ret = unsafe { core::_InputOutputArray::opencv_from_extern(ret) };
		Ok(ret)
	}
	
}

output_array_ref_forward! { core::Vector<core::Vector<core::Vec3f>> }

