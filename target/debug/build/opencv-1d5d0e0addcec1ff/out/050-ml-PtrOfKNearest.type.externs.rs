pub fn cv_PtrLcv_ml_KNearestG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_KNearestG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_KNearestG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ml_KNearestG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_KNearestG_to_PtrOfStatModel(instance: *mut c_void) -> *mut c_void;
