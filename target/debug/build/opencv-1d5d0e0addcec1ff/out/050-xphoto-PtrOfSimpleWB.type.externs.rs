pub fn cv_PtrLcv_xphoto_SimpleWBG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_SimpleWBG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_SimpleWBG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_xphoto_SimpleWBG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_SimpleWBG_to_PtrOfWhiteBalancer(instance: *mut c_void) -> *mut c_void;
