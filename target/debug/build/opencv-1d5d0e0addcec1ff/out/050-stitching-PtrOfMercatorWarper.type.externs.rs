pub fn cv_PtrLcv_MercatorWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_MercatorWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_MercatorWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_MercatorWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_MercatorWarperG_new_const_MercatorWarper(val: *mut c_void) -> *mut c_void;
