extern "C" {
	const cv::aruco::GridBoard* cv_PtrLcv_aruco_GridBoardG_getInnerPtr_const(const cv::Ptr<cv::aruco::GridBoard>* instance) {
			return instance->get();
	}
	
	cv::aruco::GridBoard* cv_PtrLcv_aruco_GridBoardG_getInnerPtrMut(cv::Ptr<cv::aruco::GridBoard>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_GridBoardG_delete(cv::Ptr<cv::aruco::GridBoard>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::Board>* cv_PtrLcv_aruco_GridBoardG_to_PtrOfBoard(cv::Ptr<cv::aruco::GridBoard>* instance) {
			return new cv::Ptr<cv::aruco::Board>(instance->dynamicCast<cv::aruco::Board>());
	}
	
	cv::Ptr<cv::aruco::GridBoard>* cv_PtrLcv_aruco_GridBoardG_new_const_GridBoard(cv::aruco::GridBoard* val) {
			return new cv::Ptr<cv::aruco::GridBoard>(val);
	}
	
}

