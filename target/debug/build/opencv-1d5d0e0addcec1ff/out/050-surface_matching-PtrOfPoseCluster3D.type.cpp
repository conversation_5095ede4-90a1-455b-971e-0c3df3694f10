extern "C" {
	const cv::ppf_match_3d::PoseCluster3D* cv_PtrLcv_ppf_match_3d_PoseCluster3DG_getInnerPtr_const(const cv::Ptr<cv::ppf_match_3d::PoseCluster3D>* instance) {
			return instance->get();
	}
	
	cv::ppf_match_3d::PoseCluster3D* cv_PtrLcv_ppf_match_3d_PoseCluster3DG_getInnerPtrMut(cv::Ptr<cv::ppf_match_3d::PoseCluster3D>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ppf_match_3d_PoseCluster3DG_delete(cv::Ptr<cv::ppf_match_3d::PoseCluster3D>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::ppf_match_3d::PoseCluster3D>* cv_PtrLcv_ppf_match_3d_PoseCluster3DG_new_const_PoseCluster3D(cv::ppf_match_3d::PoseCluster3D* val) {
			return new cv::Ptr<cv::ppf_match_3d::PoseCluster3D>(val);
	}
	
}

