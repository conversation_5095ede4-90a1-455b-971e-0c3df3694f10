pub fn std_vectorLsize_tG_new_const() -> *mut c_void;
pub fn std_vectorLsize_tG_delete(instance: *mut c_void);
pub fn std_vectorLsize_tG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLsize_tG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLsize_tG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLsize_tG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLsize_tG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLsize_tG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLsize_tG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLsize_tG_clear(instance: *mut c_void);
pub fn std_vectorLsize_tG_push_const_size_t(instance: *mut c_void, val: size_t);
pub fn std_vectorLsize_tG_insert_size_t_const_size_t(instance: *mut c_void, index: size_t, val: size_t);
pub fn std_vectorLsize_tG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut size_t);
pub fn std_vectorLsize_tG_set_size_t_const_size_t(instance: *mut c_void, index: size_t, val: size_t);
pub fn std_vectorLsize_tG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLsize_tG_data_const(instance: *const c_void) -> *const size_t;
pub fn std_vectorLsize_tG_dataMut(instance: *mut c_void) -> *mut size_t;
pub fn cv_fromSlice_const_const_size_tX_size_t(data: *const size_t, len: size_t) -> *mut c_void;
