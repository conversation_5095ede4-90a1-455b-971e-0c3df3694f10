extern "C" {
	const cv::videostab::ISparseOptFlowEstimator* cv_PtrLcv_videostab_ISparseOptFlowEstimatorG_getInnerPtr_const(const cv::Ptr<cv::videostab::ISparseOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	cv::videostab::ISparseOptFlowEstimator* cv_PtrLcv_videostab_ISparseOptFlowEstimatorG_getInnerPtrMut(cv::Ptr<cv::videostab::ISparseOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ISparseOptFlowEstimatorG_delete(cv::Ptr<cv::videostab::ISparseOptFlowEstimator>* instance) {
			delete instance;
	}
	
}

