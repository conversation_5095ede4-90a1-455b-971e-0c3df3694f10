extern "C" {
	const cv::text::ERFilter::Callback* cv_PtrLcv_text_ERFilter_CallbackG_getInnerPtr_const(const cv::Ptr<cv::text::ERFilter::Callback>* instance) {
			return instance->get();
	}
	
	cv::text::ERFilter::Callback* cv_PtrLcv_text_ERFilter_CallbackG_getInnerPtrMut(cv::Ptr<cv::text::ERFilter::Callback>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_text_ERFilter_CallbackG_delete(cv::Ptr<cv::text::ERFilter::Callback>* instance) {
			delete instance;
	}
	
}

