extern "C" {
	const cv::detail::GraphCutSeamFinderBase* cv_PtrLcv_detail_GraphCutSeamFinderBaseG_getInnerPtr_const(const cv::Ptr<cv::detail::GraphCutSeamFinderBase>* instance) {
			return instance->get();
	}
	
	cv::detail::GraphCutSeamFinderBase* cv_PtrLcv_detail_GraphCutSeamFinderBaseG_getInnerPtrMut(cv::Ptr<cv::detail::GraphCutSeamFinderBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_GraphCutSeamFinderBaseG_delete(cv::Ptr<cv::detail::GraphCutSeamFinderBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::GraphCutSeamFinderBase>* cv_PtrLcv_detail_GraphCutSeamFinderBaseG_new_const_GraphCutSeamFinderBase(cv::detail::GraphCutSeamFinderBase* val) {
			return new cv::Ptr<cv::detail::GraphCutSeamFinderBase>(val);
	}
	
}

