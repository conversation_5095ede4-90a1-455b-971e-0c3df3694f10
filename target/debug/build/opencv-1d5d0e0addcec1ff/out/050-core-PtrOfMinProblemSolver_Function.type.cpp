extern "C" {
	const cv::MinProblemSolver::Function* cv_PtrLcv_MinProblemSolver_FunctionG_getInnerPtr_const(const cv::Ptr<cv::MinProblemSolver::Function>* instance) {
			return instance->get();
	}
	
	cv::MinProblemSolver::Function* cv_PtrLcv_MinProblemSolver_FunctionG_getInnerPtrMut(cv::Ptr<cv::MinProblemSolver::Function>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MinProblemSolver_FunctionG_delete(cv::Ptr<cv::MinProblemSolver::Function>* instance) {
			delete instance;
	}
	
}

