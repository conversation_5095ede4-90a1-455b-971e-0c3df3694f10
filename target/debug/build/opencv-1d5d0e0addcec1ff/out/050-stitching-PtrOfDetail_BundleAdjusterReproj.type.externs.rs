pub fn cv_PtrLcv_detail_BundleAdjusterReprojG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterReprojG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterReprojG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_BundleAdjusterReprojG_to_PtrOfDetail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterReprojG_to_PtrOfDetail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterReprojG_new_const_BundleAdjusterReproj(val: *mut c_void) -> *mut c_void;
