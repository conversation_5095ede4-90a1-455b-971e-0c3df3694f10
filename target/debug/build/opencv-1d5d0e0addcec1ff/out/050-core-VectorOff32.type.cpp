extern "C" {
	std::vector<float>* std_vectorLfloatG_new_const() {
			std::vector<float>* ret = new std::vector<float>();
			return ret;
	}
	
	void std_vectorLfloatG_delete(std::vector<float>* instance) {
			delete instance;
	}
	
	size_t std_vectorLfloatG_len_const(const std::vector<float>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLfloatG_isEmpty_const(const std::vector<float>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLfloatG_capacity_const(const std::vector<float>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLfloatG_shrinkToFit(std::vector<float>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLfloatG_reserve_size_t(std::vector<float>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLfloatG_remove_size_t(std::vector<float>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLfloatG_swap_size_t_size_t(std::vector<float>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLfloatG_clear(std::vector<float>* instance) {
			instance->clear();
	}
	
	void std_vectorLfloatG_push_const_float(std::vector<float>* instance, const float val) {
			instance->push_back(val);
	}
	
	void std_vectorLfloatG_insert_size_t_const_float(std::vector<float>* instance, size_t index, const float val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLfloatG_get_const_size_t(const std::vector<float>* instance, size_t index, float* ocvrs_return) {
			float ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLfloatG_set_size_t_const_float(std::vector<float>* instance, size_t index, const float val) {
			(*instance)[index] = val;
	}
	
	std::vector<float>* std_vectorLfloatG_clone_const(const std::vector<float>* instance) {
			std::vector<float> ret = std::vector<float>(*instance);
			return new std::vector<float>(ret);
	}
	
	const float* std_vectorLfloatG_data_const(const std::vector<float>* instance) {
			const float* ret = instance->data();
			return ret;
	}
	
	float* std_vectorLfloatG_dataMut(std::vector<float>* instance) {
			float* ret = instance->data();
			return ret;
	}
	
	std::vector<float>* cv_fromSlice_const_const_floatX_size_t(const float* data, size_t len) {
			return new std::vector<float>(data, data + len);
	}
	
	void std_vectorLfloatG_inputArray_const(const std::vector<float>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLfloatG_outputArray(std::vector<float>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLfloatG_inputOutputArray(std::vector<float>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


