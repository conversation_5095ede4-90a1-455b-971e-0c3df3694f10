extern "C" {
	const cv::detail::Estimator* cv_PtrLcv_detail_EstimatorG_getInnerPtr_const(const cv::Ptr<cv::detail::Estimator>* instance) {
			return instance->get();
	}
	
	cv::detail::Estimator* cv_PtrLcv_detail_EstimatorG_getInnerPtrMut(cv::Ptr<cv::detail::Estimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_EstimatorG_delete(cv::Ptr<cv::detail::Estimator>* instance) {
			delete instance;
	}
	
}

