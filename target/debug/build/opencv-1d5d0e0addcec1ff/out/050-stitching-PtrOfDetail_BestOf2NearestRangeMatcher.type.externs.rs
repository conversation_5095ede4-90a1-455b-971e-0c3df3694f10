pub fn cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_to_PtrOfDetail_BestOf2NearestMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_to_PtrOfDetail_FeaturesMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_new_const_BestOf2NearestRangeMatcher(val: *mut c_void) -> *mut c_void;
