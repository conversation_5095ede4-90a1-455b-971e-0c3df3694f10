pub fn cv_PtrLcv_videostab_InpaintingPipelineG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_InpaintingPipelineG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_InpaintingPipelineG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_InpaintingPipelineG_to_PtrOfInpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_InpaintingPipelineG_new_const_InpaintingPipeline(val: *mut c_void) -> *mut c_void;
