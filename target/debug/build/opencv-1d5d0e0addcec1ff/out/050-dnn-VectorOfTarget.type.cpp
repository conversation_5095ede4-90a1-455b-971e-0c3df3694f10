extern "C" {
	std::vector<cv::dnn::Target>* std_vectorLcv_dnn_TargetG_new_const() {
			std::vector<cv::dnn::Target>* ret = new std::vector<cv::dnn::Target>();
			return ret;
	}
	
	void std_vectorLcv_dnn_TargetG_delete(std::vector<cv::dnn::Target>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_dnn_TargetG_len_const(const std::vector<cv::dnn::Target>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_dnn_TargetG_isEmpty_const(const std::vector<cv::dnn::Target>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_dnn_TargetG_capacity_const(const std::vector<cv::dnn::Target>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_dnn_TargetG_shrinkToFit(std::vector<cv::dnn::Target>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_dnn_TargetG_reserve_size_t(std::vector<cv::dnn::Target>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_dnn_TargetG_remove_size_t(std::vector<cv::dnn::Target>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_dnn_TargetG_swap_size_t_size_t(std::vector<cv::dnn::Target>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_dnn_TargetG_clear(std::vector<cv::dnn::Target>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_dnn_TargetG_push_const_Target(std::vector<cv::dnn::Target>* instance, const cv::dnn::Target val) {
			instance->push_back(val);
	}
	
	void std_vectorLcv_dnn_TargetG_insert_size_t_const_Target(std::vector<cv::dnn::Target>* instance, size_t index, const cv::dnn::Target val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLcv_dnn_TargetG_get_const_size_t(const std::vector<cv::dnn::Target>* instance, size_t index, cv::dnn::Target* ocvrs_return) {
			cv::dnn::Target ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_dnn_TargetG_set_size_t_const_Target(std::vector<cv::dnn::Target>* instance, size_t index, const cv::dnn::Target val) {
			(*instance)[index] = val;
	}
	
	std::vector<cv::dnn::Target>* std_vectorLcv_dnn_TargetG_clone_const(const std::vector<cv::dnn::Target>* instance) {
			std::vector<cv::dnn::Target> ret = std::vector<cv::dnn::Target>(*instance);
			return new std::vector<cv::dnn::Target>(ret);
	}
	
	const cv::dnn::Target* std_vectorLcv_dnn_TargetG_data_const(const std::vector<cv::dnn::Target>* instance) {
			const cv::dnn::Target* ret = instance->data();
			return ret;
	}
	
	cv::dnn::Target* std_vectorLcv_dnn_TargetG_dataMut(std::vector<cv::dnn::Target>* instance) {
			cv::dnn::Target* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::dnn::Target>* cv_fromSlice_const_const_TargetX_size_t(const cv::dnn::Target* data, size_t len) {
			return new std::vector<cv::dnn::Target>(data, data + len);
	}
	
}


