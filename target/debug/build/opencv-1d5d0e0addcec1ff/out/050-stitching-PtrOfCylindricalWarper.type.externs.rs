pub fn cv_PtrLcv_CylindricalWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_CylindricalWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_CylindricalWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_CylindricalWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_CylindricalWarperG_new_const_CylindricalWarper(val: *mut c_void) -> *mut c_void;
