pub fn cv_PtrLcv_dnn_DataAugmentationLayerG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_dnn_DataAugmentationLayerG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_dnn_DataAugmentationLayerG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_dnn_DataAugmentationLayerG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_dnn_DataAugmentationLayerG_to_PtrOfLayer(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_dnn_DataAugmentationLayerG_new_const_DataAugmentationLayer(val: *mut c_void) -> *mut c_void;
