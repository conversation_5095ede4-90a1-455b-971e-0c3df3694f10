pub fn std_vectorLcv_cuda_GpuMatG_new_const() -> *mut c_void;
pub fn std_vectorLcv_cuda_GpuMatG_delete(instance: *mut c_void);
pub fn std_vectorLcv_cuda_GpuMatG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_cuda_GpuMatG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_cuda_GpuMatG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_cuda_GpuMatG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_cuda_GpuMatG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_cuda_GpuMatG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_cuda_GpuMatG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_cuda_GpuMatG_clear(instance: *mut c_void);
pub fn std_vectorLcv_cuda_GpuMatG_push_const_GpuMat(instance: *mut c_void, val: *const c_void);
pub fn std_vectorLcv_cuda_GpuMatG_insert_size_t_const_GpuMat(instance: *mut c_void, index: size_t, val: *const c_void);
pub fn std_vectorLcv_cuda_GpuMatG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut *mut c_void);
pub fn std_vectorLcv_cuda_GpuMatG_set_size_t_const_GpuMat(instance: *mut c_void, index: size_t, val: *const c_void);
