pub type VectorOfLineMod_Match = core::Vector<crate::rgbd::LineMod_Match>;

impl core::Vector<crate::rgbd::LineMod_Match> {
	pub fn as_raw_VectorOfLineMod_Match(&self) -> extern_send!(Self) { self.as_raw() }
	pub fn as_raw_mut_VectorOfLineMod_Match(&mut self) -> extern_send!(mut Self) { self.as_raw_mut() }
}

vector_extern! { crate::rgbd::LineMod_Match,
	std_vectorLcv_linemod_MatchG_new_const, std_vectorLcv_linemod_MatchG_delete,
	std_vectorLcv_linemod_MatchG_len_const, std_vectorLcv_linemod_MatchG_isEmpty_const,
	std_vectorLcv_linemod_MatchG_capacity_const, std_vectorLcv_linemod_MatchG_shrinkToFit,
	std_vectorLcv_linemod_MatchG_reserve_size_t, std_vectorLcv_linemod_MatchG_remove_size_t,
	std_vectorLcv_linemod_MatchG_swap_size_t_size_t, std_vectorLcv_linemod_MatchG_clear,
	std_vectorLcv_linemod_MatchG_get_const_size_t, std_vectorLcv_linemod_MatchG_set_size_t_const_Match,
	std_vectorLcv_linemod_MatchG_push_const_Match, std_vectorLcv_linemod_MatchG_insert_size_t_const_Match,
}
vector_non_copy_or_bool! { clone crate::rgbd::LineMod_Match }

