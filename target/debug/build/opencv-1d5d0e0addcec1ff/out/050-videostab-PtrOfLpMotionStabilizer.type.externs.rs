pub fn cv_PtrLcv_videostab_LpMotionStabilizerG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_LpMotionStabilizerG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_LpMotionStabilizerG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_LpMotionStabilizerG_to_PtrOfIMotionStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_LpMotionStabilizerG_new_const_LpMotionStabilizer(val: *mut c_void) -> *mut c_void;
