pub fn cv_PtrLcv_videostab_MotionEstimatorRansacL2G_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionEstimatorRansacL2G_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionEstimatorRansacL2G_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_MotionEstimatorRansacL2G_to_PtrOfMotionEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionEstimatorRansacL2G_new_const_MotionEstimatorRansacL2(val: *mut c_void) -> *mut c_void;
