extern "C" {
	const cv::videostab::InpainterBase* cv_PtrLcv_videostab_InpainterBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::InpainterBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::InpainterBase* cv_PtrLcv_videostab_InpainterBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::InpainterBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_InpainterBaseG_delete(cv::Ptr<cv::videostab::InpainterBase>* instance) {
			delete instance;
	}
	
}

