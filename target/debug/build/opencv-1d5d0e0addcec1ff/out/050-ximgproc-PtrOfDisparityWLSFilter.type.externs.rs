pub fn cv_PtrLcv_ximgproc_DisparityWLSFilterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_DisparityWLSFilterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_DisparityWLSFilterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_DisparityWLSFilterG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_DisparityWLSFilterG_to_PtrOfDisparityFilter(instance: *mut c_void) -> *mut c_void;
