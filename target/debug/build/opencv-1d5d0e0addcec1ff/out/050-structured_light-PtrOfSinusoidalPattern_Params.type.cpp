extern "C" {
	const cv::structured_light::SinusoidalPattern::Params* cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_getInnerPtr_const(const cv::Ptr<cv::structured_light::SinusoidalPattern::Params>* instance) {
			return instance->get();
	}
	
	cv::structured_light::SinusoidalPattern::Params* cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_getInnerPtrMut(cv::Ptr<cv::structured_light::SinusoidalPattern::Params>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_delete(cv::Ptr<cv::structured_light::SinusoidalPattern::Params>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::structured_light::SinusoidalPattern::Params>* cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_new_const_Params(cv::structured_light::SinusoidalPattern::Params* val) {
			return new cv::Ptr<cv::structured_light::SinusoidalPattern::Params>(val);
	}
	
}

