pub fn cv_PtrLcv_videostab_ConsistentMosaicInpainterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ConsistentMosaicInpainterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ConsistentMosaicInpainterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_ConsistentMosaicInpainterG_to_PtrOfInpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ConsistentMosaicInpainterG_new_const_ConsistentMosaicInpainter(val: *mut c_void) -> *mut c_void;
