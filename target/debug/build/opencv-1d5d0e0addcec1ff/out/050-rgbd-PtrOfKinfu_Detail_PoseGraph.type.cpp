extern "C" {
	const cv::kinfu::detail::PoseGraph* cv_PtrLcv_kinfu_detail_PoseGraphG_getInnerPtr_const(const cv::Ptr<cv::kinfu::detail::PoseGraph>* instance) {
			return instance->get();
	}
	
	cv::kinfu::detail::PoseGraph* cv_PtrLcv_kinfu_detail_PoseGraphG_getInnerPtrMut(cv::Ptr<cv::kinfu::detail::PoseGraph>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_kinfu_detail_PoseGraphG_delete(cv::Ptr<cv::kinfu::detail::PoseGraph>* instance) {
			delete instance;
	}
	
}

