extern "C" {
	const cv::ppf_match_3d::Pose3D* cv_PtrLcv_ppf_match_3d_Pose3DG_getInnerPtr_const(const cv::Ptr<cv::ppf_match_3d::Pose3D>* instance) {
			return instance->get();
	}
	
	cv::ppf_match_3d::Pose3D* cv_PtrLcv_ppf_match_3d_Pose3DG_getInnerPtrMut(cv::Ptr<cv::ppf_match_3d::Pose3D>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ppf_match_3d_Pose3DG_delete(cv::Ptr<cv::ppf_match_3d::Pose3D>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::ppf_match_3d::Pose3D>* cv_PtrLcv_ppf_match_3d_Pose3DG_new_const_Pose3D(cv::ppf_match_3d::Pose3D* val) {
			return new cv::Ptr<cv::ppf_match_3d::Pose3D>(val);
	}
	
}

