pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_new_const() -> *mut c_void;
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_delete(instance: *mut c_void);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_clear(instance: *mut c_void);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_push_const_pairLcv_UMat__unsigned_charG(instance: *mut c_void, val: *const c_void);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_insert_size_t_const_pairLcv_UMat__unsigned_charG(instance: *mut c_void, index: size_t, val: *const c_void);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut *mut c_void);
pub fn std_vectorLstd_pairLcv_UMat__unsigned_charGG_set_size_t_const_pairLcv_UMat__unsigned_charG(instance: *mut c_void, index: size_t, val: *const c_void);
