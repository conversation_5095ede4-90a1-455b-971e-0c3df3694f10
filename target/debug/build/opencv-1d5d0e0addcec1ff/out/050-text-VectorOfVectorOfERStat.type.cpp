extern "C" {
	std::vector<std::vector<cv::text::ERStat>>* std_vectorLstd_vectorLcv_text_ERStatGG_new_const() {
			std::vector<std::vector<cv::text::ERStat>>* ret = new std::vector<std::vector<cv::text::ERStat>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_delete(std::vector<std::vector<cv::text::ERStat>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_text_ERStatGG_len_const(const std::vector<std::vector<cv::text::ERStat>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_text_ERStatGG_isEmpty_const(const std::vector<std::vector<cv::text::ERStat>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_text_ERStatGG_capacity_const(const std::vector<std::vector<cv::text::ERStat>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_shrinkToFit(std::vector<std::vector<cv::text::ERStat>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_reserve_size_t(std::vector<std::vector<cv::text::ERStat>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_remove_size_t(std::vector<std::vector<cv::text::ERStat>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_swap_size_t_size_t(std::vector<std::vector<cv::text::ERStat>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_clear(std::vector<std::vector<cv::text::ERStat>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_push_const_vectorLERStatG(std::vector<std::vector<cv::text::ERStat>>* instance, const std::vector<cv::text::ERStat>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_insert_size_t_const_vectorLERStatG(std::vector<std::vector<cv::text::ERStat>>* instance, size_t index, const std::vector<cv::text::ERStat>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_get_const_size_t(const std::vector<std::vector<cv::text::ERStat>>* instance, size_t index, std::vector<cv::text::ERStat>** ocvrs_return) {
			std::vector<cv::text::ERStat> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::text::ERStat>(ret);
	}
	
	void std_vectorLstd_vectorLcv_text_ERStatGG_set_size_t_const_vectorLERStatG(std::vector<std::vector<cv::text::ERStat>>* instance, size_t index, const std::vector<cv::text::ERStat>* val) {
			(*instance)[index] = *val;
	}
	
}


