pub fn cv_PtrLcv_videostab_TwoPassStabilizerG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_TwoPassStabilizerG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_TwoPassStabilizerG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_TwoPassStabilizerG_to_PtrOfIFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_TwoPassStabilizerG_to_PtrOfStabilizerBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_TwoPassStabilizerG_new_const_TwoPassStabilizer(val: *mut c_void) -> *mut c_void;
