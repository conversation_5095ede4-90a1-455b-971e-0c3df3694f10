pub fn std_vectorLdoubleG_new_const() -> *mut c_void;
pub fn std_vectorLdoubleG_delete(instance: *mut c_void);
pub fn std_vectorLdoubleG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLdoubleG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLdoubleG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLdoubleG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLdoubleG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLdoubleG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLdoubleG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLdoubleG_clear(instance: *mut c_void);
pub fn std_vectorLdoubleG_push_const_double(instance: *mut c_void, val: f64);
pub fn std_vectorLdoubleG_insert_size_t_const_double(instance: *mut c_void, index: size_t, val: f64);
pub fn std_vectorLdoubleG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut f64);
pub fn std_vectorLdoubleG_set_size_t_const_double(instance: *mut c_void, index: size_t, val: f64);
pub fn std_vectorLdoubleG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLdoubleG_data_const(instance: *const c_void) -> *const f64;
pub fn std_vectorLdoubleG_dataMut(instance: *mut c_void) -> *mut f64;
pub fn cv_fromSlice_const_const_doubleX_size_t(data: *const f64, len: size_t) -> *mut c_void;
pub fn std_vectorLdoubleG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLdoubleG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLdoubleG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
