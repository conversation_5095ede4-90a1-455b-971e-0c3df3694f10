pub fn cv_PtrLcv_text_OCRHMMDecoderG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRHMMDecoderG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRHMMDecoderG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_text_OCRHMMDecoderG_to_PtrOfBaseOCR(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_text_OCRHMMDecoderG_new_const_OCRHMMDecoder(val: *mut c_void) -> *mut c_void;
