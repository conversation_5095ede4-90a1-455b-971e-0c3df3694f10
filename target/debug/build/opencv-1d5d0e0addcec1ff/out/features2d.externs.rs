pub fn cv_AGAST_const__InputArrayR_vectorLKeyPointGR_int(image: *const c_void, keypoints: *mut c_void, threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AGAST_const__InputArrayR_vectorLKeyPointGR_int_bool(image: *const c_void, keypoints: *mut c_void, threshold: i32, nonmax_suppression: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_AGAST_const__InputArrayR_vectorLKeyPointGR_int_bool_DetectorType(image: *const c_void, keypoints: *mut c_void, threshold: i32, nonmax_suppression: bool, typ: crate::features2d::AgastFeatureDetector_DetectorType, ocvrs_return: *mut ResultVoid);
pub fn cv_FAST_const__InputArrayR_vectorLKeyPointGR_int(image: *const c_void, keypoints: *mut c_void, threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FAST_const__InputArrayR_vectorLKeyPointGR_int_bool(image: *const c_void, keypoints: *mut c_void, threshold: i32, nonmax_suppression: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_FAST_const__InputArrayR_vectorLKeyPointGR_int_bool_DetectorType(image: *const c_void, keypoints: *mut c_void, threshold: i32, nonmax_suppression: bool, typ: crate::features2d::FastFeatureDetector_DetectorType, ocvrs_return: *mut ResultVoid);
pub fn cv_computeRecallPrecisionCurve_const_vectorLvectorLDMatchGGR_const_vectorLvectorLunsigned_charGGR_vectorLPoint2fGR(matches1to2: *const c_void, correct_matches1to2_mask: *const c_void, recall_precision_curve: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_drawKeypoints_const__InputArrayR_const_vectorLKeyPointGR_const__InputOutputArrayR(image: *const c_void, keypoints: *const c_void, out_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_drawKeypoints_const__InputArrayR_const_vectorLKeyPointGR_const__InputOutputArrayR_const_ScalarR_DrawMatchesFlags(image: *const c_void, keypoints: *const c_void, out_image: *const c_void, color: *const core::Scalar, flags: crate::features2d::DrawMatchesFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_drawMatches_const__InputArrayR_const_vectorLKeyPointGR_const__InputArrayR_const_vectorLKeyPointGR_const_vectorLDMatchGR_const__InputOutputArrayR(img1: *const c_void, keypoints1: *const c_void, img2: *const c_void, keypoints2: *const c_void, matches1to2: *const c_void, out_img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_drawMatches_const__InputArrayR_const_vectorLKeyPointGR_const__InputArrayR_const_vectorLKeyPointGR_const_vectorLDMatchGR_const__InputOutputArrayR_const_ScalarR_const_ScalarR_const_vectorLcharGR_DrawMatchesFlags(img1: *const c_void, keypoints1: *const c_void, img2: *const c_void, keypoints2: *const c_void, matches1to2: *const c_void, out_img: *const c_void, match_color: *const core::Scalar, single_point_color: *const core::Scalar, matches_mask: *const c_void, flags: crate::features2d::DrawMatchesFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_drawMatches_const__InputArrayR_const_vectorLKeyPointGR_const__InputArrayR_const_vectorLKeyPointGR_const_vectorLDMatchGR_const__InputOutputArrayR_const_int(img1: *const c_void, keypoints1: *const c_void, img2: *const c_void, keypoints2: *const c_void, matches1to2: *const c_void, out_img: *const c_void, matches_thickness: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_drawMatches_const__InputArrayR_const_vectorLKeyPointGR_const__InputArrayR_const_vectorLKeyPointGR_const_vectorLDMatchGR_const__InputOutputArrayR_const_int_const_ScalarR_const_ScalarR_const_vectorLcharGR_DrawMatchesFlags(img1: *const c_void, keypoints1: *const c_void, img2: *const c_void, keypoints2: *const c_void, matches1to2: *const c_void, out_img: *const c_void, matches_thickness: i32, match_color: *const core::Scalar, single_point_color: *const core::Scalar, matches_mask: *const c_void, flags: crate::features2d::DrawMatchesFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_drawMatches_const__InputArrayR_const_vectorLKeyPointGR_const__InputArrayR_const_vectorLKeyPointGR_const_vectorLvectorLDMatchGGR_const__InputOutputArrayR(img1: *const c_void, keypoints1: *const c_void, img2: *const c_void, keypoints2: *const c_void, matches1to2: *const c_void, out_img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_drawMatches_const__InputArrayR_const_vectorLKeyPointGR_const__InputArrayR_const_vectorLKeyPointGR_const_vectorLvectorLDMatchGGR_const__InputOutputArrayR_const_ScalarR_const_ScalarR_const_vectorLvectorLcharGGR_DrawMatchesFlags(img1: *const c_void, keypoints1: *const c_void, img2: *const c_void, keypoints2: *const c_void, matches1to2: *const c_void, out_img: *const c_void, match_color: *const core::Scalar, single_point_color: *const core::Scalar, matches_mask: *const c_void, flags: crate::features2d::DrawMatchesFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_evaluateFeatureDetector_const_MatR_const_MatR_const_MatR_vectorLKeyPointGX_vectorLKeyPointGX_floatR_intR(img1: *const c_void, img2: *const c_void, h1to2: *const c_void, keypoints1: *mut c_void, keypoints2: *mut c_void, repeatability: *mut f32, corresp_count: *mut i32, ocvrs_return: *mut ResultVoid);
pub fn cv_evaluateFeatureDetector_const_MatR_const_MatR_const_MatR_vectorLKeyPointGX_vectorLKeyPointGX_floatR_intR_const_PtrLFeature2DGR(img1: *const c_void, img2: *const c_void, h1to2: *const c_void, keypoints1: *mut c_void, keypoints2: *mut c_void, repeatability: *mut f32, corresp_count: *mut i32, fdetector: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_getNearestPoint_const_vectorLPoint2fGR_float(recall_precision_curve: *const c_void, l_precision: f32, ocvrs_return: *mut Result<i32>);
pub fn cv_getRecall_const_vectorLPoint2fGR_float(recall_precision_curve: *const c_void, l_precision: f32, ocvrs_return: *mut Result<f32>);
pub fn cv_AKAZE_create_DescriptorType_int_int_float_int_int_DiffusivityType(descriptor_type: crate::features2d::AKAZE_DescriptorType, descriptor_size: i32, descriptor_channels: i32, threshold: f32, n_octaves: i32, n_octave_layers: i32, diffusivity: crate::features2d::KAZE_DiffusivityType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AKAZE_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AKAZE_setDescriptorType_DescriptorType(instance: *mut c_void, dtype: crate::features2d::AKAZE_DescriptorType, ocvrs_return: *mut ResultVoid);
pub fn cv_AKAZE_getDescriptorType_const(instance: *const c_void, ocvrs_return: *mut Result<crate::features2d::AKAZE_DescriptorType>);
pub fn cv_AKAZE_setDescriptorSize_int(instance: *mut c_void, dsize: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AKAZE_getDescriptorSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_AKAZE_setDescriptorChannels_int(instance: *mut c_void, dch: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AKAZE_getDescriptorChannels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_AKAZE_setThreshold_double(instance: *mut c_void, threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_AKAZE_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_AKAZE_setNOctaves_int(instance: *mut c_void, octaves: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AKAZE_getNOctaves_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_AKAZE_setNOctaveLayers_int(instance: *mut c_void, octave_layers: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AKAZE_getNOctaveLayers_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_AKAZE_setDiffusivity_DiffusivityType(instance: *mut c_void, diff: crate::features2d::KAZE_DiffusivityType, ocvrs_return: *mut ResultVoid);
pub fn cv_AKAZE_getDiffusivity_const(instance: *const c_void, ocvrs_return: *mut Result<crate::features2d::KAZE_DiffusivityType>);
pub fn cv_AKAZE_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AKAZE_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_AKAZE_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_AKAZE_delete(instance: *mut c_void);
pub fn cv_AffineFeature_create_const_PtrLFeature2DGR_int_int_float_float(backend: *const c_void, max_tilt: i32, min_tilt: i32, tilt_step: f32, rotate_step_base: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AffineFeature_create_const_PtrLFeature2DGR(backend: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AffineFeature_setViewParams_const_vectorLfloatGR_const_vectorLfloatGR(instance: *mut c_void, tilts: *const c_void, rolls: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AffineFeature_getViewParams_const_vectorLfloatGR_vectorLfloatGR(instance: *const c_void, tilts: *mut c_void, rolls: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AffineFeature_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AffineFeature_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_AffineFeature_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_AffineFeature_delete(instance: *mut c_void);
pub fn cv_AgastFeatureDetector_create_int_bool_DetectorType(threshold: i32, nonmax_suppression: bool, typ: crate::features2d::AgastFeatureDetector_DetectorType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AgastFeatureDetector_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AgastFeatureDetector_setThreshold_int(instance: *mut c_void, threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AgastFeatureDetector_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_AgastFeatureDetector_setNonmaxSuppression_bool(instance: *mut c_void, f: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_AgastFeatureDetector_getNonmaxSuppression_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_AgastFeatureDetector_setType_DetectorType(instance: *mut c_void, typ: crate::features2d::AgastFeatureDetector_DetectorType, ocvrs_return: *mut ResultVoid);
pub fn cv_AgastFeatureDetector_getType_const(instance: *const c_void, ocvrs_return: *mut Result<crate::features2d::AgastFeatureDetector_DetectorType>);
pub fn cv_AgastFeatureDetector_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AgastFeatureDetector_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_AgastFeatureDetector_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_AgastFeatureDetector_delete(instance: *mut c_void);
pub fn cv_BFMatcher_BFMatcher_int_bool(norm_type: i32, cross_check: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BFMatcher_BFMatcher(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BFMatcher_isMaskSupported_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_BFMatcher_create_int_bool(norm_type: i32, cross_check: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BFMatcher_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BFMatcher_clone_const_bool(instance: *const c_void, empty_train_data: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BFMatcher_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BFMatcher_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_BFMatcher_to_DescriptorMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_BFMatcher_delete(instance: *mut c_void);
pub fn cv_BOWImgDescriptorExtractor_BOWImgDescriptorExtractor_const_PtrLFeature2DGR_const_PtrLDescriptorMatcherGR(dextractor: *const c_void, dmatcher: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWImgDescriptorExtractor_BOWImgDescriptorExtractor_const_PtrLDescriptorMatcherGR(dmatcher: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWImgDescriptorExtractor_setVocabulary_const_MatR(instance: *mut c_void, vocabulary: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BOWImgDescriptorExtractor_getVocabulary_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWImgDescriptorExtractor_compute_const__InputArrayR_vectorLKeyPointGR_const__OutputArrayR_vectorLvectorLintGGX_MatX(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, img_descriptor: *const c_void, point_idxs_of_clusters: *mut c_void, descriptors: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BOWImgDescriptorExtractor_compute_const__InputArrayR_vectorLKeyPointGR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, img_descriptor: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BOWImgDescriptorExtractor_compute_const__InputArrayR_const__OutputArrayR_vectorLvectorLintGGX(instance: *mut c_void, keypoint_descriptors: *const c_void, img_descriptor: *const c_void, point_idxs_of_clusters: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BOWImgDescriptorExtractor_compute_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, keypoint_descriptors: *const c_void, img_descriptor: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BOWImgDescriptorExtractor_compute2_const_MatR_vectorLKeyPointGR_MatR(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, img_descriptor: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BOWImgDescriptorExtractor_descriptorSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BOWImgDescriptorExtractor_descriptorType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BOWImgDescriptorExtractor_delete(instance: *mut c_void);
pub fn cv_BOWKMeansTrainer_BOWKMeansTrainer_int_const_TermCriteriaR_int_int(cluster_count: i32, termcrit: *const core::TermCriteria, attempts: i32, flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWKMeansTrainer_BOWKMeansTrainer_int(cluster_count: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWKMeansTrainer_cluster_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWKMeansTrainer_cluster_const_const_MatR(instance: *const c_void, descriptors: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWKMeansTrainer_to_BOWTrainer(instance: *mut c_void) -> *mut c_void;
pub fn cv_BOWKMeansTrainer_delete(instance: *mut c_void);
pub fn cv_BOWTrainer_add_const_MatR(instance: *mut c_void, descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BOWTrainer_getDescriptors_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWTrainer_descriptorsCount_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BOWTrainer_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BOWTrainer_cluster_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWTrainer_cluster_const_const_MatR(instance: *const c_void, descriptors: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BOWTrainer_to_BOWKMeansTrainer(instance: *mut c_void) -> *mut c_void;
pub fn cv_BOWTrainer_delete(instance: *mut c_void);
pub fn cv_BRISK_create_int_int_float(thresh: i32, octaves: i32, pattern_scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BRISK_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BRISK_create_const_vectorLfloatGR_const_vectorLintGR_float_float_const_vectorLintGR(radius_list: *const c_void, number_list: *const c_void, d_max: f32, d_min: f32, index_change: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BRISK_create_const_vectorLfloatGR_const_vectorLintGR(radius_list: *const c_void, number_list: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BRISK_create_int_int_const_vectorLfloatGR_const_vectorLintGR_float_float_const_vectorLintGR(thresh: i32, octaves: i32, radius_list: *const c_void, number_list: *const c_void, d_max: f32, d_min: f32, index_change: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BRISK_create_int_int_const_vectorLfloatGR_const_vectorLintGR(thresh: i32, octaves: i32, radius_list: *const c_void, number_list: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BRISK_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BRISK_setThreshold_int(instance: *mut c_void, threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BRISK_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BRISK_setOctaves_int(instance: *mut c_void, octaves: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BRISK_getOctaves_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BRISK_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_BRISK_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_BRISK_delete(instance: *mut c_void);
pub fn cv_DescriptorMatcher_add_const__InputArrayR(instance: *mut c_void, descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_getTrainDescriptors_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DescriptorMatcher_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_DescriptorMatcher_isMaskSupported_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_DescriptorMatcher_train(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_match_const_const__InputArrayR_const__InputArrayR_vectorLDMatchGR_const__InputArrayR(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_match_const_const__InputArrayR_const__InputArrayR_vectorLDMatchGR(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_knnMatch_const_const__InputArrayR_const__InputArrayR_vectorLvectorLDMatchGGR_int_const__InputArrayR_bool(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, k: i32, mask: *const c_void, compact_result: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_knnMatch_const_const__InputArrayR_const__InputArrayR_vectorLvectorLDMatchGGR_int(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, k: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_radiusMatch_const_const__InputArrayR_const__InputArrayR_vectorLvectorLDMatchGGR_float_const__InputArrayR_bool(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, max_distance: f32, mask: *const c_void, compact_result: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_radiusMatch_const_const__InputArrayR_const__InputArrayR_vectorLvectorLDMatchGGR_float(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, max_distance: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_match_const__InputArrayR_vectorLDMatchGR_const__InputArrayR(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_match_const__InputArrayR_vectorLDMatchGR(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_knnMatch_const__InputArrayR_vectorLvectorLDMatchGGR_int_const__InputArrayR_bool(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, k: i32, masks: *const c_void, compact_result: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_knnMatch_const__InputArrayR_vectorLvectorLDMatchGGR_int(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, k: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_radiusMatch_const__InputArrayR_vectorLvectorLDMatchGGR_float_const__InputArrayR_bool(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, max_distance: f32, masks: *const c_void, compact_result: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_radiusMatch_const__InputArrayR_vectorLvectorLDMatchGGR_float(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, max_distance: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_write_const_const_StringR(instance: *const c_void, file_name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_read_const_StringR(instance: *mut c_void, file_name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_read_const_FileNodeR(instance: *mut c_void, unnamed: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_write_const_FileStorageR(instance: *const c_void, unnamed: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_clone_const_bool(instance: *const c_void, empty_train_data: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DescriptorMatcher_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DescriptorMatcher_create_const_StringR(descriptor_matcher_type: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DescriptorMatcher_create_const_MatcherTypeR(matcher_type: *const crate::features2d::DescriptorMatcher_MatcherType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DescriptorMatcher_write_const_const_PtrLFileStorageGR_const_StringR(instance: *const c_void, fs: *const c_void, name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_write_const_const_PtrLFileStorageGR(instance: *const c_void, fs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DescriptorMatcher_to_BFMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_DescriptorMatcher_to_FlannBasedMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_DescriptorMatcher_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_DescriptorMatcher_delete(instance: *mut c_void);
pub fn cv_FastFeatureDetector_create_int_bool_DetectorType(threshold: i32, nonmax_suppression: bool, typ: crate::features2d::FastFeatureDetector_DetectorType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FastFeatureDetector_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FastFeatureDetector_setThreshold_int(instance: *mut c_void, threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FastFeatureDetector_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FastFeatureDetector_setNonmaxSuppression_bool(instance: *mut c_void, f: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_FastFeatureDetector_getNonmaxSuppression_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FastFeatureDetector_setType_DetectorType(instance: *mut c_void, typ: crate::features2d::FastFeatureDetector_DetectorType, ocvrs_return: *mut ResultVoid);
pub fn cv_FastFeatureDetector_getType_const(instance: *const c_void, ocvrs_return: *mut Result<crate::features2d::FastFeatureDetector_DetectorType>);
pub fn cv_FastFeatureDetector_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FastFeatureDetector_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_FastFeatureDetector_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_FastFeatureDetector_delete(instance: *mut c_void);
pub fn cv_Feature2D_detect_const__InputArrayR_vectorLKeyPointGR_const__InputArrayR(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_detect_const__InputArrayR_vectorLKeyPointGR(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_detect_const__InputArrayR_vectorLvectorLKeyPointGGR_const__InputArrayR(instance: *mut c_void, images: *const c_void, keypoints: *mut c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_detect_const__InputArrayR_vectorLvectorLKeyPointGGR(instance: *mut c_void, images: *const c_void, keypoints: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_compute_const__InputArrayR_vectorLKeyPointGR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_compute_const__InputArrayR_vectorLvectorLKeyPointGGR_const__OutputArrayR(instance: *mut c_void, images: *const c_void, keypoints: *mut c_void, descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_detectAndCompute_const__InputArrayR_const__InputArrayR_vectorLKeyPointGR_const__OutputArrayR_bool(instance: *mut c_void, image: *const c_void, mask: *const c_void, keypoints: *mut c_void, descriptors: *const c_void, use_provided_keypoints: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_detectAndCompute_const__InputArrayR_const__InputArrayR_vectorLKeyPointGR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, mask: *const c_void, keypoints: *mut c_void, descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_descriptorSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_Feature2D_descriptorType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_Feature2D_defaultNorm_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_Feature2D_write_const_const_StringR(instance: *const c_void, file_name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_read_const_StringR(instance: *mut c_void, file_name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_write_const_FileStorageR(instance: *const c_void, unnamed: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_read_const_FileNodeR(instance: *mut c_void, unnamed: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_Feature2D_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Feature2D_write_const_const_PtrLFileStorageGR_const_StringR(instance: *const c_void, fs: *const c_void, name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_write_const_const_PtrLFileStorageGR(instance: *const c_void, fs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Feature2D_to_AKAZE(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_AffineFeature(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_AgastFeatureDetector(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_BRISK(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_FastFeatureDetector(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_GFTTDetector(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_KAZE(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_MSER(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_ORB(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_SIFT(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_SimpleBlobDetector(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_Feature2D_delete(instance: *mut c_void);
pub fn cv_FlannBasedMatcher_FlannBasedMatcher_const_PtrLIndexParamsGR_const_PtrLSearchParamsGR(index_params: *const c_void, search_params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FlannBasedMatcher_FlannBasedMatcher(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FlannBasedMatcher_add_const__InputArrayR(instance: *mut c_void, descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FlannBasedMatcher_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FlannBasedMatcher_read_const_FileNodeR(instance: *mut c_void, unnamed: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FlannBasedMatcher_write_const_FileStorageR(instance: *const c_void, unnamed: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FlannBasedMatcher_train(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FlannBasedMatcher_isMaskSupported_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FlannBasedMatcher_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FlannBasedMatcher_clone_const_bool(instance: *const c_void, empty_train_data: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FlannBasedMatcher_clone_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FlannBasedMatcher_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_FlannBasedMatcher_to_DescriptorMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_FlannBasedMatcher_delete(instance: *mut c_void);
pub fn cv_GFTTDetector_create_int_double_double_int_bool_double(max_corners: i32, quality_level: f64, min_distance: f64, block_size: i32, use_harris_detector: bool, k: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_GFTTDetector_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_GFTTDetector_create_int_double_double_int_int_bool_double(max_corners: i32, quality_level: f64, min_distance: f64, block_size: i32, gradiant_size: i32, use_harris_detector: bool, k: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_GFTTDetector_create_int_double_double_int_int(max_corners: i32, quality_level: f64, min_distance: f64, block_size: i32, gradiant_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_GFTTDetector_setMaxFeatures_int(instance: *mut c_void, max_features: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GFTTDetector_getMaxFeatures_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GFTTDetector_setQualityLevel_double(instance: *mut c_void, qlevel: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GFTTDetector_getQualityLevel_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GFTTDetector_setMinDistance_double(instance: *mut c_void, min_distance: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GFTTDetector_getMinDistance_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GFTTDetector_setBlockSize_int(instance: *mut c_void, block_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GFTTDetector_getBlockSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GFTTDetector_setHarrisDetector_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_GFTTDetector_getHarrisDetector_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_GFTTDetector_setK_double(instance: *mut c_void, k: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GFTTDetector_getK_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GFTTDetector_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_GFTTDetector_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_GFTTDetector_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_GFTTDetector_delete(instance: *mut c_void);
pub fn cv_KAZE_create_bool_bool_float_int_int_DiffusivityType(extended: bool, upright: bool, threshold: f32, n_octaves: i32, n_octave_layers: i32, diffusivity: crate::features2d::KAZE_DiffusivityType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KAZE_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KAZE_setExtended_bool(instance: *mut c_void, extended: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_KAZE_getExtended_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_KAZE_setUpright_bool(instance: *mut c_void, upright: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_KAZE_getUpright_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_KAZE_setThreshold_double(instance: *mut c_void, threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_KAZE_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_KAZE_setNOctaves_int(instance: *mut c_void, octaves: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_KAZE_getNOctaves_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_KAZE_setNOctaveLayers_int(instance: *mut c_void, octave_layers: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_KAZE_getNOctaveLayers_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_KAZE_setDiffusivity_DiffusivityType(instance: *mut c_void, diff: crate::features2d::KAZE_DiffusivityType, ocvrs_return: *mut ResultVoid);
pub fn cv_KAZE_getDiffusivity_const(instance: *const c_void, ocvrs_return: *mut Result<crate::features2d::KAZE_DiffusivityType>);
pub fn cv_KAZE_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KAZE_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_KAZE_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_KAZE_delete(instance: *mut c_void);
pub fn cv_KeyPointsFilter_KeyPointsFilter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KeyPointsFilter_runByImageBorder_vectorLKeyPointGR_Size_int(keypoints: *mut c_void, image_size: *const core::Size, border_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPointsFilter_runByKeypointSize_vectorLKeyPointGR_float_float(keypoints: *mut c_void, min_size: f32, max_size: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPointsFilter_runByKeypointSize_vectorLKeyPointGR_float(keypoints: *mut c_void, min_size: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPointsFilter_runByPixelsMask_vectorLKeyPointGR_const_MatR(keypoints: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPointsFilter_removeDuplicated_vectorLKeyPointGR(keypoints: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPointsFilter_removeDuplicatedSorted_vectorLKeyPointGR(keypoints: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPointsFilter_retainBest_vectorLKeyPointGR_int(keypoints: *mut c_void, npoints: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_KeyPointsFilter_delete(instance: *mut c_void);
pub fn cv_MSER_create_int_int_int_double_double_int_double_double_int(delta: i32, min_area: i32, max_area: i32, max_variation: f64, min_diversity: f64, max_evolution: i32, area_threshold: f64, min_margin: f64, edge_blur_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MSER_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MSER_detectRegions_const__InputArrayR_vectorLvectorLPointGGR_vectorLRectGR(instance: *mut c_void, image: *const c_void, msers: *mut c_void, bboxes: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MSER_setDelta_int(instance: *mut c_void, delta: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_MSER_getDelta_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_MSER_setMinArea_int(instance: *mut c_void, min_area: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_MSER_getMinArea_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_MSER_setMaxArea_int(instance: *mut c_void, max_area: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_MSER_getMaxArea_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_MSER_setPass2Only_bool(instance: *mut c_void, f: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_MSER_getPass2Only_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_MSER_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MSER_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_MSER_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_MSER_delete(instance: *mut c_void);
pub fn cv_ORB_create_int_float_int_int_int_int_ScoreType_int_int(nfeatures: i32, scale_factor: f32, nlevels: i32, edge_threshold: i32, first_level: i32, wta_k: i32, score_type: crate::features2d::ORB_ScoreType, patch_size: i32, fast_threshold: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ORB_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ORB_setMaxFeatures_int(instance: *mut c_void, max_features: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getMaxFeatures_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ORB_setScaleFactor_double(instance: *mut c_void, scale_factor: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getScaleFactor_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ORB_setNLevels_int(instance: *mut c_void, nlevels: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getNLevels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ORB_setEdgeThreshold_int(instance: *mut c_void, edge_threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getEdgeThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ORB_setFirstLevel_int(instance: *mut c_void, first_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getFirstLevel_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ORB_setWTA_K_int(instance: *mut c_void, wta_k: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getWTA_K_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ORB_setScoreType_ScoreType(instance: *mut c_void, score_type: crate::features2d::ORB_ScoreType, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getScoreType_const(instance: *const c_void, ocvrs_return: *mut Result<crate::features2d::ORB_ScoreType>);
pub fn cv_ORB_setPatchSize_int(instance: *mut c_void, patch_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getPatchSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ORB_setFastThreshold_int(instance: *mut c_void, fast_threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ORB_getFastThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ORB_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ORB_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ORB_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_ORB_delete(instance: *mut c_void);
pub fn cv_SIFT_create_int_int_double_double_double(nfeatures: i32, n_octave_layers: i32, contrast_threshold: f64, edge_threshold: f64, sigma: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SIFT_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SIFT_create_int_int_double_double_double_int(nfeatures: i32, n_octave_layers: i32, contrast_threshold: f64, edge_threshold: f64, sigma: f64, descriptor_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SIFT_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SIFT_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_SIFT_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_SIFT_delete(instance: *mut c_void);
pub fn cv_SimpleBlobDetector_create_const_ParamsR(parameters: *const crate::features2d::SimpleBlobDetector_Params, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SimpleBlobDetector_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SimpleBlobDetector_getDefaultName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SimpleBlobDetector_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_SimpleBlobDetector_to_Feature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_SimpleBlobDetector_delete(instance: *mut c_void);
pub fn cv_SimpleBlobDetector_Params_Params(ocvrs_return: *mut Result<crate::features2d::SimpleBlobDetector_Params>);
pub fn cv_SimpleBlobDetector_Params_read_const_FileNodeR(instance: *const crate::features2d::SimpleBlobDetector_Params, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SimpleBlobDetector_Params_write_const_FileStorageR(instance: *const crate::features2d::SimpleBlobDetector_Params, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
