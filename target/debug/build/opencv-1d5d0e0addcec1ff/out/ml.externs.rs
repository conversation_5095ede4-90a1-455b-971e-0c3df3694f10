pub fn cv_ml_createConcentricSpheresTestSet_int_int_int_const__OutputArrayR_const__OutputArrayR(nsamples: i32, nfeatures: i32, nclasses: i32, samples: *const c_void, responses: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_randMVNormal_const__InputArrayR_const__InputArrayR_int_const__OutputArrayR(mean: *const c_void, cov: *const c_void, nsamples: i32, samples: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_setTrainMethod_int_double_double(instance: *mut c_void, method: i32, param1: f64, param2: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_setTrainMethod_int(instance: *mut c_void, method: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getTrainMethod_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_ANN_MLP_setActivationFunction_int_double_double(instance: *mut c_void, typ: i32, param1: f64, param2: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_setActivationFunction_int(instance: *mut c_void, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_setLayerSizes_const__InputArrayR(instance: *mut c_void, _layer_sizes: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getLayerSizes_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_ANN_MLP_getTermCriteria_const(instance: *const c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_ml_ANN_MLP_setTermCriteria_TermCriteria(instance: *mut c_void, val: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getBackpropWeightScale_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setBackpropWeightScale_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getBackpropMomentumScale_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setBackpropMomentumScale_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getRpropDW0_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setRpropDW0_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getRpropDWPlus_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setRpropDWPlus_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getRpropDWMinus_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setRpropDWMinus_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getRpropDWMin_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setRpropDWMin_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getRpropDWMax_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setRpropDWMax_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getAnnealInitialT_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setAnnealInitialT_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getAnnealFinalT_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setAnnealFinalT_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getAnnealCoolingRatio_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_ANN_MLP_setAnnealCoolingRatio_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getAnnealItePerStep_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_ANN_MLP_setAnnealItePerStep_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_setAnnealEnergyRNG_const_RNGR(instance: *mut c_void, rng: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_ANN_MLP_getWeights_const_int(instance: *const c_void, layer_idx: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_ANN_MLP_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_ANN_MLP_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_ANN_MLP_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_ANN_MLP_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_ANN_MLP_delete(instance: *mut c_void);
pub fn cv_ml_Boost_getBoostType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_Boost_setBoostType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_Boost_getWeakCount_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_Boost_setWeakCount_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_Boost_getWeightTrimRate_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_Boost_setWeightTrimRate_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_Boost_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_Boost_load_const_StringR_const_StringR(filepath: *const c_char, node_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_Boost_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_Boost_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_Boost_to_DTrees(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_Boost_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_Boost_delete(instance: *mut c_void);
pub fn cv_ml_DTrees_getMaxCategories_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_DTrees_setMaxCategories_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getMaxDepth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_DTrees_setMaxDepth_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getMinSampleCount_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_DTrees_setMinSampleCount_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getCVFolds_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_DTrees_setCVFolds_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getUseSurrogates_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_DTrees_setUseSurrogates_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getUse1SERule_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_DTrees_setUse1SERule_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getTruncatePrunedTree_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_DTrees_setTruncatePrunedTree_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getRegressionAccuracy_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_DTrees_setRegressionAccuracy_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getPriors_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_setPriors_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_DTrees_getRoots_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_getNodes_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_getSplits_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_getSubsets_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_load_const_StringR_const_StringR(filepath: *const c_char, node_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_to_Boost(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_DTrees_to_RTrees(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_DTrees_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_DTrees_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_DTrees_delete(instance: *mut c_void);
pub fn cv_ml_DTrees_Node_Node(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_Node_propValue_const(instance: *const c_void) -> f64;
pub fn cv_ml_DTrees_Node_propValue_const_double(instance: *mut c_void, val: f64);
pub fn cv_ml_DTrees_Node_propClassIdx_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Node_propClassIdx_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Node_propParent_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Node_propParent_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Node_propLeft_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Node_propLeft_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Node_propRight_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Node_propRight_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Node_propDefaultDir_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Node_propDefaultDir_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Node_propSplit_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Node_propSplit_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Node_delete(instance: *mut c_void);
pub fn cv_ml_DTrees_Split_Split(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_DTrees_Split_propVarIdx_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Split_propVarIdx_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Split_propInversed_const(instance: *const c_void) -> bool;
pub fn cv_ml_DTrees_Split_propInversed_const_bool(instance: *mut c_void, val: bool);
pub fn cv_ml_DTrees_Split_propQuality_const(instance: *const c_void) -> f32;
pub fn cv_ml_DTrees_Split_propQuality_const_float(instance: *mut c_void, val: f32);
pub fn cv_ml_DTrees_Split_propNext_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Split_propNext_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Split_propC_const(instance: *const c_void) -> f32;
pub fn cv_ml_DTrees_Split_propC_const_float(instance: *mut c_void, val: f32);
pub fn cv_ml_DTrees_Split_propSubsetOfs_const(instance: *const c_void) -> i32;
pub fn cv_ml_DTrees_Split_propSubsetOfs_const_int(instance: *mut c_void, val: i32);
pub fn cv_ml_DTrees_Split_delete(instance: *mut c_void);
pub fn cv_ml_EM_getClustersNumber_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_EM_setClustersNumber_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_EM_getCovarianceMatrixType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_EM_setCovarianceMatrixType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_EM_getTermCriteria_const(instance: *const c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_ml_EM_setTermCriteria_const_TermCriteriaR(instance: *mut c_void, val: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_EM_getWeights_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_EM_getMeans_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_EM_getCovs_const_vectorLMatGR(instance: *const c_void, covs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_EM_predict_const_const__InputArrayR_const__OutputArrayR_int(instance: *const c_void, samples: *const c_void, results: *const c_void, flags: i32, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_EM_predict_const_const__InputArrayR(instance: *const c_void, samples: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_EM_predict2_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, sample: *const c_void, probs: *const c_void, ocvrs_return: *mut Result<core::Vec2d>);
pub fn cv_ml_EM_trainEM_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, samples: *const c_void, log_likelihoods: *const c_void, labels: *const c_void, probs: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_EM_trainEM_const__InputArrayR(instance: *mut c_void, samples: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_EM_trainE_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, samples: *const c_void, means0: *const c_void, covs0: *const c_void, weights0: *const c_void, log_likelihoods: *const c_void, labels: *const c_void, probs: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_EM_trainE_const__InputArrayR_const__InputArrayR(instance: *mut c_void, samples: *const c_void, means0: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_EM_trainM_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, samples: *const c_void, probs0: *const c_void, log_likelihoods: *const c_void, labels: *const c_void, probs: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_EM_trainM_const__InputArrayR_const__InputArrayR(instance: *mut c_void, samples: *const c_void, probs0: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_EM_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_EM_load_const_StringR_const_StringR(filepath: *const c_char, node_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_EM_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_EM_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_EM_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_EM_delete(instance: *mut c_void);
pub fn cv_ml_KNearest_getDefaultK_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_KNearest_setDefaultK_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_KNearest_getIsClassifier_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_KNearest_setIsClassifier_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_KNearest_getEmax_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_KNearest_setEmax_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_KNearest_getAlgorithmType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_KNearest_setAlgorithmType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_KNearest_findNearest_const_const__InputArrayR_int_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, samples: *const c_void, k: i32, results: *const c_void, neighbor_responses: *const c_void, dist: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_KNearest_findNearest_const_const__InputArrayR_int_const__OutputArrayR(instance: *const c_void, samples: *const c_void, k: i32, results: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_KNearest_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_KNearest_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_KNearest_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_KNearest_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_KNearest_delete(instance: *mut c_void);
pub fn cv_ml_LogisticRegression_getLearningRate_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_LogisticRegression_setLearningRate_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_LogisticRegression_getIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_LogisticRegression_setIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_LogisticRegression_getRegularization_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_LogisticRegression_setRegularization_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_LogisticRegression_getTrainMethod_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_LogisticRegression_setTrainMethod_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_LogisticRegression_getMiniBatchSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_LogisticRegression_setMiniBatchSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_LogisticRegression_getTermCriteria_const(instance: *const c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_ml_LogisticRegression_setTermCriteria_TermCriteria(instance: *mut c_void, val: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_LogisticRegression_predict_const_const__InputArrayR_const__OutputArrayR_int(instance: *const c_void, samples: *const c_void, results: *const c_void, flags: i32, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_LogisticRegression_predict_const_const__InputArrayR(instance: *const c_void, samples: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_LogisticRegression_get_learnt_thetas_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_LogisticRegression_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_LogisticRegression_load_const_StringR_const_StringR(filepath: *const c_char, node_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_LogisticRegression_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_LogisticRegression_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_LogisticRegression_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_LogisticRegression_delete(instance: *mut c_void);
pub fn cv_ml_NormalBayesClassifier_predictProb_const_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int(instance: *const c_void, inputs: *const c_void, outputs: *const c_void, output_probs: *const c_void, flags: i32, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_NormalBayesClassifier_predictProb_const_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, inputs: *const c_void, outputs: *const c_void, output_probs: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_NormalBayesClassifier_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_NormalBayesClassifier_load_const_StringR_const_StringR(filepath: *const c_char, node_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_NormalBayesClassifier_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_NormalBayesClassifier_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_NormalBayesClassifier_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_NormalBayesClassifier_delete(instance: *mut c_void);
pub fn cv_ml_ParamGrid_ParamGrid(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_ParamGrid_ParamGrid_double_double_double(_min_val: f64, _max_val: f64, _log_step: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_ParamGrid_create_double_double_double(min_val: f64, max_val: f64, logstep: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_ParamGrid_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_ParamGrid_propMinVal_const(instance: *const c_void) -> f64;
pub fn cv_ml_ParamGrid_propMinVal_const_double(instance: *mut c_void, val: f64);
pub fn cv_ml_ParamGrid_propMaxVal_const(instance: *const c_void) -> f64;
pub fn cv_ml_ParamGrid_propMaxVal_const_double(instance: *mut c_void, val: f64);
pub fn cv_ml_ParamGrid_propLogStep_const(instance: *const c_void) -> f64;
pub fn cv_ml_ParamGrid_propLogStep_const_double(instance: *mut c_void, val: f64);
pub fn cv_ml_ParamGrid_delete(instance: *mut c_void);
pub fn cv_ml_RTrees_getCalculateVarImportance_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_RTrees_setCalculateVarImportance_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_RTrees_getActiveVarCount_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_RTrees_setActiveVarCount_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_RTrees_getTermCriteria_const(instance: *const c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_ml_RTrees_setTermCriteria_const_TermCriteriaR(instance: *mut c_void, val: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_RTrees_getVarImportance_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_RTrees_getVotes_const_const__InputArrayR_const__OutputArrayR_int(instance: *const c_void, samples: *const c_void, results: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_RTrees_getOOBError_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_RTrees_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_RTrees_load_const_StringR_const_StringR(filepath: *const c_char, node_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_RTrees_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_RTrees_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_RTrees_to_DTrees(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_RTrees_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_RTrees_delete(instance: *mut c_void);
pub fn cv_ml_SVM_getType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_SVM_setType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getGamma_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_SVM_setGamma_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getCoef0_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_SVM_setCoef0_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getDegree_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_SVM_setDegree_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getC_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_SVM_setC_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getNu_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_SVM_setNu_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getP_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_SVM_setP_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getClassWeights_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVM_setClassWeights_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getTermCriteria_const(instance: *const c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_ml_SVM_setTermCriteria_const_TermCriteriaR(instance: *mut c_void, val: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_getKernelType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_SVM_setKernel_int(instance: *mut c_void, kernel_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_setCustomKernel_const_PtrLKernelGR(instance: *mut c_void, _kernel: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_trainAuto_const_PtrLTrainDataGR_int_ParamGrid_ParamGrid_ParamGrid_ParamGrid_ParamGrid_ParamGrid_bool(instance: *mut c_void, data: *const c_void, k_fold: i32, cgrid: *mut c_void, gamma_grid: *mut c_void, p_grid: *mut c_void, nu_grid: *mut c_void, coeff_grid: *mut c_void, degree_grid: *mut c_void, balanced: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_SVM_trainAuto_const_PtrLTrainDataGR(instance: *mut c_void, data: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_SVM_trainAuto_const__InputArrayR_int_const__InputArrayR_int_PtrLParamGridG_PtrLParamGridG_PtrLParamGridG_PtrLParamGridG_PtrLParamGridG_PtrLParamGridG_bool(instance: *mut c_void, samples: *const c_void, layout: i32, responses: *const c_void, k_fold: i32, cgrid: *mut c_void, gamma_grid: *mut c_void, p_grid: *mut c_void, nu_grid: *mut c_void, coeff_grid: *mut c_void, degree_grid: *mut c_void, balanced: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_SVM_trainAuto_const__InputArrayR_int_const__InputArrayR(instance: *mut c_void, samples: *const c_void, layout: i32, responses: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_SVM_getSupportVectors_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVM_getUncompressedSupportVectors_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVM_getDecisionFunction_const_int_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, i: i32, alpha: *const c_void, svidx: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ml_SVM_getDefaultGrid_int(param_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVM_getDefaultGridPtr_int(param_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVM_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVM_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVM_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_SVM_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_SVM_delete(instance: *mut c_void);
pub fn cv_ml_SVM_Kernel_getType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_SVM_Kernel_calc_int_int_const_floatX_const_floatX_floatX(instance: *mut c_void, vcount: i32, n: i32, vecs: *const f32, another: *const f32, results: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVM_Kernel_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_SVM_Kernel_delete(instance: *mut c_void);
pub fn cv_ml_SVMSGD_getWeights(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVMSGD_getShift(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_SVMSGD_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVMSGD_load_const_StringR_const_StringR(filepath: *const c_char, node_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVMSGD_load_const_StringR(filepath: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_SVMSGD_setOptimalParameters_int_int(instance: *mut c_void, svmsgd_type: i32, margin_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVMSGD_setOptimalParameters(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVMSGD_getSvmsgdType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_SVMSGD_setSvmsgdType_int(instance: *mut c_void, svmsgd_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVMSGD_getMarginType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_SVMSGD_setMarginType_int(instance: *mut c_void, margin_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVMSGD_getMarginRegularization_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_SVMSGD_setMarginRegularization_float(instance: *mut c_void, margin_regularization: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVMSGD_getInitialStepSize_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_SVMSGD_setInitialStepSize_float(instance: *mut c_void, initial_step_size: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVMSGD_getStepDecreasingPower_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_SVMSGD_setStepDecreasingPower_float(instance: *mut c_void, step_decreasing_power: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVMSGD_getTermCriteria_const(instance: *const c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_ml_SVMSGD_setTermCriteria_const_TermCriteriaR(instance: *mut c_void, val: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_SVMSGD_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_SVMSGD_to_StatModel(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_SVMSGD_delete(instance: *mut c_void);
pub fn cv_ml_StatModel_getVarCount_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_StatModel_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_StatModel_isTrained_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_StatModel_isClassifier_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_StatModel_train_const_PtrLTrainDataGR_int(instance: *mut c_void, train_data: *const c_void, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_StatModel_train_const_PtrLTrainDataGR(instance: *mut c_void, train_data: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_StatModel_train_const__InputArrayR_int_const__InputArrayR(instance: *mut c_void, samples: *const c_void, layout: i32, responses: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ml_StatModel_calcError_const_const_PtrLTrainDataGR_bool_const__OutputArrayR(instance: *const c_void, data: *const c_void, test: bool, resp: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_StatModel_predict_const_const__InputArrayR_const__OutputArrayR_int(instance: *const c_void, samples: *const c_void, results: *const c_void, flags: i32, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_StatModel_predict_const_const__InputArrayR(instance: *const c_void, samples: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_ml_StatModel_to_ANN_MLP(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_Boost(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_DTrees(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_EM(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_KNearest(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_LogisticRegression(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_NormalBayesClassifier(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_RTrees(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_SVM(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_SVMSGD(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ml_StatModel_delete(instance: *mut c_void);
pub fn cv_ml_TrainData_missingValue(ocvrs_return: *mut Result<f32>);
pub fn cv_ml_TrainData_getLayout_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_TrainData_getNTrainSamples_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_TrainData_getNTestSamples_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_TrainData_getNSamples_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_TrainData_getNVars_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_TrainData_getNAllVars_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_TrainData_getSample_const_const__InputArrayR_int_floatX(instance: *const c_void, var_idx: *const c_void, sidx: i32, buf: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_getSamples_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getMissing_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTrainSamples_const_int_bool_bool(instance: *const c_void, layout: i32, compress_samples: bool, compress_vars: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTrainSamples_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTrainResponses_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTrainNormCatResponses_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTestResponses_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTestNormCatResponses_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getResponses_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getNormCatResponses_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getSampleWeights_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTrainSampleWeights_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTestSampleWeights_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getVarIdx_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getVarType_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getVarSymbolFlags_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getResponseType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_TrainData_getTrainSampleIdx_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getTestSampleIdx_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getValues_const_int_const__InputArrayR_floatX(instance: *const c_void, vi: i32, sidx: *const c_void, values: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_getNormCatValues_const_int_const__InputArrayR_intX(instance: *const c_void, vi: i32, sidx: *const c_void, values: *mut i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_getDefaultSubstValues_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getCatCount_const_int(instance: *const c_void, vi: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_ml_TrainData_getClassLabels_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getCatOfs_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getCatMap_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_setTrainTestSplit_int_bool(instance: *mut c_void, count: i32, shuffle: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_setTrainTestSplit_int(instance: *mut c_void, count: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_setTrainTestSplitRatio_double_bool(instance: *mut c_void, ratio: f64, shuffle: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_setTrainTestSplitRatio_double(instance: *mut c_void, ratio: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_shuffleTrainTest(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_getTestSamples_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getNames_const_vectorLStringGR(instance: *const c_void, names: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ml_TrainData_getSubVector_const_MatR_const_MatR(vec: *const c_void, idx: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_getSubMatrix_const_MatR_const_MatR_int(matrix: *const c_void, idx: *const c_void, layout: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_loadFromCSV_const_StringR_int_int_int_const_StringR_char_char(filename: *const c_char, header_line_count: i32, response_start_idx: i32, response_end_idx: i32, var_type_spec: *const c_char, delimiter: c_char, missch: c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_loadFromCSV_const_StringR_int(filename: *const c_char, header_line_count: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_create_const__InputArrayR_int_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(samples: *const c_void, layout: i32, responses: *const c_void, var_idx: *const c_void, sample_idx: *const c_void, sample_weights: *const c_void, var_type: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_create_const__InputArrayR_int_const__InputArrayR(samples: *const c_void, layout: i32, responses: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ml_TrainData_delete(instance: *mut c_void);
