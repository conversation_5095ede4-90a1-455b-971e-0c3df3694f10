pub fn std_vectorLstd_vectorLcv_text_ERStatGG_new_const() -> *mut c_void;
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_delete(instance: *mut c_void);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_clear(instance: *mut c_void);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_push_const_vectorLERStatG(instance: *mut c_void, val: *const c_void);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_insert_size_t_const_vectorLERStatG(instance: *mut c_void, index: size_t, val: *const c_void);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut *mut c_void);
pub fn std_vectorLstd_vectorLcv_text_ERStatGG_set_size_t_const_vectorLERStatG(instance: *mut c_void, index: size_t, val: *const c_void);
