pub fn cv_PtrLcv_SIFTG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_SIFTG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_SIFTG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_SIFTG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_SIFTG_to_PtrOfFeature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_SIFTG_new_const_SIFT(val: *mut c_void) -> *mut c_void;
