pub fn std_vectorLcv_PointG_new_const() -> *mut c_void;
pub fn std_vectorLcv_PointG_delete(instance: *mut c_void);
pub fn std_vectorLcv_PointG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_PointG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_PointG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_PointG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_PointG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_PointG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_PointG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_PointG_clear(instance: *mut c_void);
pub fn std_vectorLcv_PointG_push_const_Point(instance: *mut c_void, val: *const core::Point);
pub fn std_vectorLcv_PointG_insert_size_t_const_Point(instance: *mut c_void, index: size_t, val: *const core::Point);
pub fn std_vectorLcv_PointG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Point);
pub fn std_vectorLcv_PointG_set_size_t_const_Point(instance: *mut c_void, index: size_t, val: *const core::Point);
pub fn std_vectorLcv_PointG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_PointG_data_const(instance: *const c_void) -> *const core::Point;
pub fn std_vectorLcv_PointG_dataMut(instance: *mut c_void) -> *mut core::Point;
pub fn cv_fromSlice_const_const_PointX_size_t(data: *const core::Point, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_PointG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_PointG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_PointG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
