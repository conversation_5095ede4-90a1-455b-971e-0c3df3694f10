extern "C" {
	const cv::detail::SeamFinder* cv_PtrLcv_detail_SeamFinderG_getInnerPtr_const(const cv::Ptr<cv::detail::SeamFinder>* instance) {
			return instance->get();
	}
	
	cv::detail::SeamFinder* cv_PtrLcv_detail_SeamFinderG_getInnerPtrMut(cv::Ptr<cv::detail::SeamFinder>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_SeamFinderG_delete(cv::Ptr<cv::detail::SeamFinder>* instance) {
			delete instance;
	}
	
}

