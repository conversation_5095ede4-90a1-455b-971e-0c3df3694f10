pub fn std_vectorLcv_stereo_MatchQuasiDenseG_new_const() -> *mut c_void;
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_delete(instance: *mut c_void);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_clear(instance: *mut c_void);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_push_const_MatchQuasiDense(instance: *mut c_void, val: *const crate::stereo::MatchQuasiDense);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_insert_size_t_const_MatchQuasiDense(instance: *mut c_void, index: size_t, val: *const crate::stereo::MatchQuasiDense);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut crate::stereo::MatchQuasiDense);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_set_size_t_const_MatchQuasiDense(instance: *mut c_void, index: size_t, val: *const crate::stereo::MatchQuasiDense);
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_data_const(instance: *const c_void) -> *const crate::stereo::MatchQuasiDense;
pub fn std_vectorLcv_stereo_MatchQuasiDenseG_dataMut(instance: *mut c_void) -> *mut crate::stereo::MatchQuasiDense;
pub fn cv_fromSlice_const_const_MatchQuasiDenseX_size_t(data: *const crate::stereo::MatchQuasiDense, len: size_t) -> *mut c_void;
