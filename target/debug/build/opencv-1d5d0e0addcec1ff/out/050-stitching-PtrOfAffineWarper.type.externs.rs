pub fn cv_PtrLcv_AffineWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_AffineWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_AffineWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_AffineWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_AffineWarperG_new_const_AffineWarper(val: *mut c_void) -> *mut c_void;
