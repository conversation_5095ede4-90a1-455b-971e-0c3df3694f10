pub fn std_vectorLcv_VideoCaptureAPIsG_new_const() -> *mut c_void;
pub fn std_vectorLcv_VideoCaptureAPIsG_delete(instance: *mut c_void);
pub fn std_vectorLcv_VideoCaptureAPIsG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_VideoCaptureAPIsG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_VideoCaptureAPIsG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_VideoCaptureAPIsG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_VideoCaptureAPIsG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_VideoCaptureAPIsG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_VideoCaptureAPIsG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_VideoCaptureAPIsG_clear(instance: *mut c_void);
pub fn std_vectorLcv_VideoCaptureAPIsG_push_const_VideoCaptureAPIs(instance: *mut c_void, val: crate::videoio::VideoCaptureAPIs);
pub fn std_vectorLcv_VideoCaptureAPIsG_insert_size_t_const_VideoCaptureAPIs(instance: *mut c_void, index: size_t, val: crate::videoio::VideoCaptureAPIs);
pub fn std_vectorLcv_VideoCaptureAPIsG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut crate::videoio::VideoCaptureAPIs);
pub fn std_vectorLcv_VideoCaptureAPIsG_set_size_t_const_VideoCaptureAPIs(instance: *mut c_void, index: size_t, val: crate::videoio::VideoCaptureAPIs);
pub fn std_vectorLcv_VideoCaptureAPIsG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_VideoCaptureAPIsG_data_const(instance: *const c_void) -> *const crate::videoio::VideoCaptureAPIs;
pub fn std_vectorLcv_VideoCaptureAPIsG_dataMut(instance: *mut c_void) -> *mut crate::videoio::VideoCaptureAPIs;
pub fn cv_fromSlice_const_const_VideoCaptureAPIsX_size_t(data: *const crate::videoio::VideoCaptureAPIs, len: size_t) -> *mut c_void;
