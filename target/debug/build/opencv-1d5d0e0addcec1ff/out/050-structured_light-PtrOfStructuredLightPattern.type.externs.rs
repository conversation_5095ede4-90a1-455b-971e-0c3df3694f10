pub fn cv_PtrLcv_structured_light_StructuredLightPatternG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_structured_light_StructuredLightPatternG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_structured_light_StructuredLightPatternG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_structured_light_StructuredLightPatternG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
