pub fn cv_PtrLcv_detail_FeatherBlenderG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_FeatherBlenderG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_FeatherBlenderG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_FeatherBlenderG_to_PtrOfDetail_Blender(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_FeatherBlenderG_new_const_FeatherBlender(val: *mut c_void) -> *mut c_void;
