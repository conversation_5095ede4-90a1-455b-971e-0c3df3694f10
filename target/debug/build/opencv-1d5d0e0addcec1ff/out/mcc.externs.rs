pub fn cv_ccm_ColorCorrectionModel_ColorCorrectionModel_const_MatR_CONST_COLOR(src: *const c_void, constcolor: crate::mcc::CONST_COLOR, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_ColorCorrectionModel_const_MatR_Mat_COLOR_SPACE(src: *const c_void, colors: *mut c_void, ref_cs: crate::mcc::COLOR_SPACE, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_ColorCorrectionModel_const_MatR_Mat_COLOR_SPACE_Mat(src: *const c_void, colors: *mut c_void, ref_cs: crate::mcc::COLOR_SPACE, colored: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_setColorSpace_COLOR_SPACE(instance: *mut c_void, cs: crate::mcc::COLOR_SPACE, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setCCM_TYPE_CCM_TYPE(instance: *mut c_void, ccm_type: crate::mcc::CCM_TYPE, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setDistance_DISTANCE_TYPE(instance: *mut c_void, distance: crate::mcc::DISTANCE_TYPE, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setLinear_LINEAR_TYPE(instance: *mut c_void, linear_type: crate::mcc::LINEAR_TYPE, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setLinearGamma_const_doubleR(instance: *mut c_void, gamma: *const f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setLinearDegree_const_intR(instance: *mut c_void, deg: *const i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setSaturatedThreshold_const_doubleR_const_doubleR(instance: *mut c_void, lower: *const f64, upper: *const f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setWeightsList_const_MatR(instance: *mut c_void, weights_list: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setWeightCoeff_const_doubleR(instance: *mut c_void, weights_coeff: *const f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setInitialMethod_INITIAL_METHOD_TYPE(instance: *mut c_void, initial_method_type: crate::mcc::INITIAL_METHOD_TYPE, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setMaxCount_const_intR(instance: *mut c_void, max_count: *const i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_setEpsilon_const_doubleR(instance: *mut c_void, epsilon: *const f64, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_run(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ccm_ColorCorrectionModel_getCCM_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_getLoss_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ccm_ColorCorrectionModel_get_src_rgbl_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_get_dst_rgbl_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_getMask_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_getWeights_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_infer_const_MatR_bool(instance: *mut c_void, img: *const c_void, islinear: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_infer_const_MatR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccm_ColorCorrectionModel_delete(instance: *mut c_void);
pub fn cv_mcc_CChecker_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CChecker_setTarget_TYPECHART(instance: *mut c_void, _target: crate::mcc::MCC_TYPECHART, ocvrs_return: *mut ResultVoid);
pub fn cv_mcc_CChecker_setBox_vectorLPoint2fG(instance: *mut c_void, _box: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_mcc_CChecker_setChartsRGB_Mat(instance: *mut c_void, _charts_rgb: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_mcc_CChecker_setChartsYCbCr_Mat(instance: *mut c_void, _charts_y_cb_cr: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_mcc_CChecker_setCost_float(instance: *mut c_void, _cost: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_mcc_CChecker_setCenter_Point2f(instance: *mut c_void, _center: *const core::Point2f, ocvrs_return: *mut ResultVoid);
pub fn cv_mcc_CChecker_getTarget(instance: *mut c_void, ocvrs_return: *mut Result<crate::mcc::MCC_TYPECHART>);
pub fn cv_mcc_CChecker_getBox(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CChecker_getChartsRGB(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CChecker_getChartsYCbCr(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CChecker_getCost(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_mcc_CChecker_getCenter(instance: *mut c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_mcc_CChecker_delete(instance: *mut c_void);
pub fn cv_mcc_CCheckerDetector_setNet_Net(instance: *mut c_void, net: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_mcc_CCheckerDetector_process_const__InputArrayR_const_TYPECHART_const_vectorLRectGR_const_int_bool_const_PtrLDetectorParametersGR(instance: *mut c_void, image: *const c_void, chart_type: crate::mcc::MCC_TYPECHART, regions_of_interest: *const c_void, nc: i32, use_net: bool, params: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_mcc_CCheckerDetector_process_const__InputArrayR_const_TYPECHART_const_vectorLRectGR(instance: *mut c_void, image: *const c_void, chart_type: crate::mcc::MCC_TYPECHART, regions_of_interest: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_mcc_CCheckerDetector_process_const__InputArrayR_const_TYPECHART_const_int_bool_const_PtrLDetectorParametersGR(instance: *mut c_void, image: *const c_void, chart_type: crate::mcc::MCC_TYPECHART, nc: i32, use_net: bool, params: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_mcc_CCheckerDetector_process_const__InputArrayR_const_TYPECHART(instance: *mut c_void, image: *const c_void, chart_type: crate::mcc::MCC_TYPECHART, ocvrs_return: *mut Result<bool>);
pub fn cv_mcc_CCheckerDetector_getBestColorChecker(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CCheckerDetector_getListColorChecker(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CCheckerDetector_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CCheckerDetector_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_mcc_CCheckerDetector_delete(instance: *mut c_void);
pub fn cv_mcc_CCheckerDraw_draw_const__InputOutputArrayR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_mcc_CCheckerDraw_create_PtrLCCheckerG_Scalar_int(p_checker: *mut c_void, color: *const core::Scalar, thickness: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CCheckerDraw_create_PtrLCCheckerG(p_checker: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_CCheckerDraw_delete(instance: *mut c_void);
pub fn cv_mcc_DetectorParameters_DetectorParameters(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_DetectorParameters_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_mcc_DetectorParameters_propAdaptiveThreshWinSizeMin_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propAdaptiveThreshWinSizeMin_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propAdaptiveThreshWinSizeMax_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propAdaptiveThreshWinSizeMax_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propAdaptiveThreshWinSizeStep_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propAdaptiveThreshWinSizeStep_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propAdaptiveThreshConstant_const(instance: *const c_void) -> f64;
pub fn cv_mcc_DetectorParameters_propAdaptiveThreshConstant_const_double(instance: *mut c_void, val: f64);
pub fn cv_mcc_DetectorParameters_propMinContoursAreaRate_const(instance: *const c_void) -> f64;
pub fn cv_mcc_DetectorParameters_propMinContoursAreaRate_const_double(instance: *mut c_void, val: f64);
pub fn cv_mcc_DetectorParameters_propMinContoursArea_const(instance: *const c_void) -> f64;
pub fn cv_mcc_DetectorParameters_propMinContoursArea_const_double(instance: *mut c_void, val: f64);
pub fn cv_mcc_DetectorParameters_propConfidenceThreshold_const(instance: *const c_void) -> f64;
pub fn cv_mcc_DetectorParameters_propConfidenceThreshold_const_double(instance: *mut c_void, val: f64);
pub fn cv_mcc_DetectorParameters_propMinContourSolidity_const(instance: *const c_void) -> f64;
pub fn cv_mcc_DetectorParameters_propMinContourSolidity_const_double(instance: *mut c_void, val: f64);
pub fn cv_mcc_DetectorParameters_propFindCandidatesApproxPolyDPEpsMultiplier_const(instance: *const c_void) -> f64;
pub fn cv_mcc_DetectorParameters_propFindCandidatesApproxPolyDPEpsMultiplier_const_double(instance: *mut c_void, val: f64);
pub fn cv_mcc_DetectorParameters_propBorderWidth_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propBorderWidth_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propB0factor_const(instance: *const c_void) -> f32;
pub fn cv_mcc_DetectorParameters_propB0factor_const_float(instance: *mut c_void, val: f32);
pub fn cv_mcc_DetectorParameters_propMaxError_const(instance: *const c_void) -> f32;
pub fn cv_mcc_DetectorParameters_propMaxError_const_float(instance: *mut c_void, val: f32);
pub fn cv_mcc_DetectorParameters_propMinContourPointsAllowed_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propMinContourPointsAllowed_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propMinContourLengthAllowed_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propMinContourLengthAllowed_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propMinInterContourDistance_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propMinInterContourDistance_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propMinInterCheckerDistance_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propMinInterCheckerDistance_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propMinImageSize_const(instance: *const c_void) -> i32;
pub fn cv_mcc_DetectorParameters_propMinImageSize_const_int(instance: *mut c_void, val: i32);
pub fn cv_mcc_DetectorParameters_propMinGroupSize_const(instance: *const c_void) -> u32;
pub fn cv_mcc_DetectorParameters_propMinGroupSize_const_unsigned_int(instance: *mut c_void, val: u32);
pub fn cv_mcc_DetectorParameters_delete(instance: *mut c_void);
