pub fn cv_PtrLcv_structured_light_SinusoidalPatternG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_structured_light_SinusoidalPatternG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_structured_light_SinusoidalPatternG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_structured_light_SinusoidalPatternG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_structured_light_SinusoidalPatternG_to_PtrOfStructuredLightPattern(instance: *mut c_void) -> *mut c_void;
