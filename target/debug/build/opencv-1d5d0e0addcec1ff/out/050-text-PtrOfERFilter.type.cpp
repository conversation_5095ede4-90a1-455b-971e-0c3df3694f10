extern "C" {
	const cv::text::ERFilter* cv_PtrLcv_text_ERFilterG_getInnerPtr_const(const cv::Ptr<cv::text::ERFilter>* instance) {
			return instance->get();
	}
	
	cv::text::ERFilter* cv_PtrLcv_text_ERFilterG_getInnerPtrMut(cv::Ptr<cv::text::ERFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_text_ERFilterG_delete(cv::Ptr<cv::text::ERFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_text_ERFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::text::ERFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

