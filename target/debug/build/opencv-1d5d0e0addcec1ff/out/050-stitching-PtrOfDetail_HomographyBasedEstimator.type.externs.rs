pub fn cv_PtrLcv_detail_HomographyBasedEstimatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_HomographyBasedEstimatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_HomographyBasedEstimatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_HomographyBasedEstimatorG_to_PtrOfDetail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_HomographyBasedEstimatorG_new_const_HomographyBasedEstimator(val: *mut c_void) -> *mut c_void;
