pub fn cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_to_PtrOfIOutlierRejector(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_new_const_TranslationBasedLocalOutlierRejector(val: *mut c_void) -> *mut c_void;
