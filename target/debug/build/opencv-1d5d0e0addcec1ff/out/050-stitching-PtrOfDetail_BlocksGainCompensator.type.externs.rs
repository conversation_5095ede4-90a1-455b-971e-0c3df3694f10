pub fn cv_PtrLcv_detail_BlocksGainCompensatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BlocksGainCompensatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BlocksGainCompensatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_BlocksGainCompensatorG_to_PtrOfDetail_BlocksCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BlocksGainCompensatorG_to_PtrOfDetail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BlocksGainCompensatorG_new_const_BlocksGainCompensator(val: *mut c_void) -> *mut c_void;
