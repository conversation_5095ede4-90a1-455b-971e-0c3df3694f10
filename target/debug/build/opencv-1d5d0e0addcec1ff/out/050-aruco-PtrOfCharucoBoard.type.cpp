extern "C" {
	const cv::aruco::CharucoBoard* cv_PtrLcv_aruco_CharucoBoardG_getInnerPtr_const(const cv::Ptr<cv::aruco::CharucoBoard>* instance) {
			return instance->get();
	}
	
	cv::aruco::CharucoBoard* cv_PtrLcv_aruco_CharucoBoardG_getInnerPtrMut(cv::Ptr<cv::aruco::CharucoBoard>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_CharucoBoardG_delete(cv::Ptr<cv::aruco::CharucoBoard>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::Board>* cv_PtrLcv_aruco_CharucoBoardG_to_PtrOfBoard(cv::Ptr<cv::aruco::CharucoBoard>* instance) {
			return new cv::Ptr<cv::aruco::Board>(instance->dynamicCast<cv::aruco::Board>());
	}
	
	cv::Ptr<cv::aruco::CharucoBoard>* cv_PtrLcv_aruco_CharucoBoardG_new_const_CharucoBoard(cv::aruco::CharucoBoard* val) {
			return new cv::Ptr<cv::aruco::CharucoBoard>(val);
	}
	
}

