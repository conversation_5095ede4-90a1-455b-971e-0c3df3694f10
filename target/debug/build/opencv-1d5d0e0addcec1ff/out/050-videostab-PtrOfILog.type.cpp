extern "C" {
	const cv::videostab::ILog* cv_PtrLcv_videostab_ILogG_getInnerPtr_const(const cv::Ptr<cv::videostab::ILog>* instance) {
			return instance->get();
	}
	
	cv::videostab::ILog* cv_PtrLcv_videostab_ILogG_getInnerPtrMut(cv::Ptr<cv::videostab::ILog>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ILogG_delete(cv::Ptr<cv::videostab::ILog>* instance) {
			delete instance;
	}
	
}

