pub fn std_vectorLfloatG_new_const() -> *mut c_void;
pub fn std_vectorLfloatG_delete(instance: *mut c_void);
pub fn std_vectorLfloatG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLfloatG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLfloatG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLfloatG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLfloatG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLfloatG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLfloatG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLfloatG_clear(instance: *mut c_void);
pub fn std_vectorLfloatG_push_const_float(instance: *mut c_void, val: f32);
pub fn std_vectorLfloatG_insert_size_t_const_float(instance: *mut c_void, index: size_t, val: f32);
pub fn std_vectorLfloatG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut f32);
pub fn std_vectorLfloatG_set_size_t_const_float(instance: *mut c_void, index: size_t, val: f32);
pub fn std_vectorLfloatG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLfloatG_data_const(instance: *const c_void) -> *const f32;
pub fn std_vectorLfloatG_dataMut(instance: *mut c_void) -> *mut f32;
pub fn cv_fromSlice_const_const_floatX_size_t(data: *const f32, len: size_t) -> *mut c_void;
pub fn std_vectorLfloatG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLfloatG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLfloatG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
