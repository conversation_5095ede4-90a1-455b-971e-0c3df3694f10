pub fn cv_tracking_TrackerCSRT_create_const_ParamsR(parameters: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_tracking_TrackerCSRT_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_tracking_TrackerCSRT_setInitialMask_const__InputArrayR(instance: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_tracking_TrackerCSRT_to_Tracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_tracking_TrackerCSRT_delete(instance: *mut c_void);
pub fn cv_tracking_TrackerCSRT_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_tracking_TrackerCSRT_Params_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_tracking_TrackerCSRT_Params_propUse_hog_const(instance: *const c_void) -> bool;
pub fn cv_tracking_TrackerCSRT_Params_propUse_hog_const_bool(instance: *mut c_void, val: bool);
pub fn cv_tracking_TrackerCSRT_Params_propUse_color_names_const(instance: *const c_void) -> bool;
pub fn cv_tracking_TrackerCSRT_Params_propUse_color_names_const_bool(instance: *mut c_void, val: bool);
pub fn cv_tracking_TrackerCSRT_Params_propUse_gray_const(instance: *const c_void) -> bool;
pub fn cv_tracking_TrackerCSRT_Params_propUse_gray_const_bool(instance: *mut c_void, val: bool);
pub fn cv_tracking_TrackerCSRT_Params_propUse_rgb_const(instance: *const c_void) -> bool;
pub fn cv_tracking_TrackerCSRT_Params_propUse_rgb_const_bool(instance: *mut c_void, val: bool);
pub fn cv_tracking_TrackerCSRT_Params_propUse_channel_weights_const(instance: *const c_void) -> bool;
pub fn cv_tracking_TrackerCSRT_Params_propUse_channel_weights_const_bool(instance: *mut c_void, val: bool);
pub fn cv_tracking_TrackerCSRT_Params_propUse_segmentation_const(instance: *const c_void) -> bool;
pub fn cv_tracking_TrackerCSRT_Params_propUse_segmentation_const_bool(instance: *mut c_void, val: bool);
pub fn cv_tracking_TrackerCSRT_Params_propWindow_function_const(instance: *const c_void) -> *mut c_void;
pub fn cv_tracking_TrackerCSRT_Params_propWindow_function_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_tracking_TrackerCSRT_Params_propKaiser_alpha_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propKaiser_alpha_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propCheb_attenuation_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propCheb_attenuation_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propTemplate_size_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propTemplate_size_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propGsl_sigma_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propGsl_sigma_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propHog_orientations_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propHog_orientations_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propHog_clip_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propHog_clip_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propPadding_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propPadding_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propFilter_lr_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propFilter_lr_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propWeights_lr_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propWeights_lr_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propNum_hog_channels_used_const(instance: *const c_void) -> i32;
pub fn cv_tracking_TrackerCSRT_Params_propNum_hog_channels_used_const_int(instance: *mut c_void, val: i32);
pub fn cv_tracking_TrackerCSRT_Params_propAdmm_iterations_const(instance: *const c_void) -> i32;
pub fn cv_tracking_TrackerCSRT_Params_propAdmm_iterations_const_int(instance: *mut c_void, val: i32);
pub fn cv_tracking_TrackerCSRT_Params_propHistogram_bins_const(instance: *const c_void) -> i32;
pub fn cv_tracking_TrackerCSRT_Params_propHistogram_bins_const_int(instance: *mut c_void, val: i32);
pub fn cv_tracking_TrackerCSRT_Params_propHistogram_lr_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propHistogram_lr_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propBackground_ratio_const(instance: *const c_void) -> i32;
pub fn cv_tracking_TrackerCSRT_Params_propBackground_ratio_const_int(instance: *mut c_void, val: i32);
pub fn cv_tracking_TrackerCSRT_Params_propNumber_of_scales_const(instance: *const c_void) -> i32;
pub fn cv_tracking_TrackerCSRT_Params_propNumber_of_scales_const_int(instance: *mut c_void, val: i32);
pub fn cv_tracking_TrackerCSRT_Params_propScale_sigma_factor_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propScale_sigma_factor_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propScale_model_max_area_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propScale_model_max_area_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propScale_lr_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propScale_lr_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propScale_step_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propScale_step_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_propPsr_threshold_const(instance: *const c_void) -> f32;
pub fn cv_tracking_TrackerCSRT_Params_propPsr_threshold_const_float(instance: *mut c_void, val: f32);
pub fn cv_tracking_TrackerCSRT_Params_delete(instance: *mut c_void);
pub fn cv_tracking_TrackerKCF_create_const_ParamsR(parameters: *const crate::tracking::TrackerKCF_Params, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_tracking_TrackerKCF_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_tracking_TrackerKCF_setFeatureExtractor_FeatureExtractorCallbackFN_bool(instance: *mut c_void, callback: Option<unsafe extern "C" fn(*const c_void, core::Rect, *mut c_void) -> ()>, pca_func: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_tracking_TrackerKCF_setFeatureExtractor_FeatureExtractorCallbackFN(instance: *mut c_void, callback: Option<unsafe extern "C" fn(*const c_void, core::Rect, *mut c_void) -> ()>, ocvrs_return: *mut ResultVoid);
pub fn cv_tracking_TrackerKCF_to_Tracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_tracking_TrackerKCF_delete(instance: *mut c_void);
pub fn cv_tracking_TrackerKCF_Params_Params(ocvrs_return: *mut Result<crate::tracking::TrackerKCF_Params>);
