pub fn cv_PtrLcv_videostab_WeightingDeblurerG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_WeightingDeblurerG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_WeightingDeblurerG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_WeightingDeblurerG_to_PtrOfDeblurerBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_WeightingDeblurerG_new_const_WeightingDeblurer(val: *mut c_void) -> *mut c_void;
