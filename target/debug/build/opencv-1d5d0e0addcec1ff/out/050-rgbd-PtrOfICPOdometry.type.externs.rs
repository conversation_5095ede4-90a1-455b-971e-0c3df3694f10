pub fn cv_PtrLcv_rgbd_ICPOdometryG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_ICPOdometryG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_ICPOdometryG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_rgbd_ICPOdometryG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_ICPOdometryG_to_PtrOfOdometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_ICPOdometryG_new_const_ICPOdometry(val: *mut c_void) -> *mut c_void;
