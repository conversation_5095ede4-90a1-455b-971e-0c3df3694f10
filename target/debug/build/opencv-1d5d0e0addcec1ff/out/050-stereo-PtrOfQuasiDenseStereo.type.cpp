extern "C" {
	const cv::stereo::QuasiDenseStereo* cv_PtrLcv_stereo_QuasiDenseStereoG_getInnerPtr_const(const cv::Ptr<cv::stereo::QuasiDenseStereo>* instance) {
			return instance->get();
	}
	
	cv::stereo::QuasiDenseStereo* cv_PtrLcv_stereo_QuasiDenseStereoG_getInnerPtrMut(cv::Ptr<cv::stereo::QuasiDenseStereo>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_stereo_QuasiDenseStereoG_delete(cv::Ptr<cv::stereo::QuasiDenseStereo>* instance) {
			delete instance;
	}
	
}

