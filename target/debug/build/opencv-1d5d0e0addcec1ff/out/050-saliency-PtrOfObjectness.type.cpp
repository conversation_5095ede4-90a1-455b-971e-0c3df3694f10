extern "C" {
	const cv::saliency::Objectness* cv_PtrLcv_saliency_ObjectnessG_getInnerPtr_const(const cv::Ptr<cv::saliency::Objectness>* instance) {
			return instance->get();
	}
	
	cv::saliency::Objectness* cv_PtrLcv_saliency_ObjectnessG_getInnerPtrMut(cv::Ptr<cv::saliency::Objectness>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_ObjectnessG_delete(cv::Ptr<cv::saliency::Objectness>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_ObjectnessG_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::Objectness>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::saliency::Saliency>* cv_PtrLcv_saliency_ObjectnessG_to_PtrOfSaliency(cv::Ptr<cv::saliency::Objectness>* instance) {
			return new cv::Ptr<cv::saliency::Saliency>(instance->dynamicCast<cv::saliency::Saliency>());
	}
	
}

