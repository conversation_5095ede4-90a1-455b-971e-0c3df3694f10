pub fn cv_kinfu_makeVolume_VolumeType_float_Matx44f_float_float_int_float_Vec3i(_volume_type: crate::rgbd::Kinfu_VolumeType, _voxel_size: f32, _pose: *const core::Matx44f, _raycast_step_factor: f32, _trunc_dist: f32, _max_weight: i32, _truncate_threshold: f32, _resolution: *const core::Vec3i, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_colormap_const_MatR_MatR(quantized: *const c_void, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_drawFeatures_const__InputOutputArrayR_const_vectorLTemplateGR_const_Point2iR(img: *const c_void, templates: *const c_void, tl: *const core::Point2i, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_drawFeatures_const__InputOutputArrayR_const_vectorLTemplateGR_const_Point2iR_int(img: *const c_void, templates: *const c_void, tl: *const core::Point2i, size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_getDefaultLINE(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_getDefaultLINEMOD(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_depthTo3dSparse_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(depth: *const c_void, in_k: *const c_void, in_points: *const c_void, points3d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_depthTo3d_const__InputArrayR_const__InputArrayR_const__OutputArrayR(depth: *const c_void, k: *const c_void, points3d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_depthTo3d_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR(depth: *const c_void, k: *const c_void, points3d: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_isValidDepth_const_doubleR(depth: *const f64, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_isValidDepth_const_floatR(depth: *const f32, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_isValidDepth_const_intR(depth: *const i32, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_isValidDepth_const_shortR(depth: *const i16, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_isValidDepth_const_unsigned_intR(depth: *const u32, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_isValidDepth_const_unsigned_shortR(depth: *const u16, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_registerDepth_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR_const__OutputArrayR(unregistered_camera_matrix: *const c_void, registered_camera_matrix: *const c_void, registered_dist_coeffs: *const c_void, rt: *const c_void, unregistered_depth: *const c_void, output_image_plane_size: *const core::Size, registered_depth: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_registerDepth_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR_const__OutputArrayR_bool(unregistered_camera_matrix: *const c_void, registered_camera_matrix: *const c_void, registered_dist_coeffs: *const c_void, rt: *const c_void, unregistered_depth: *const c_void, output_image_plane_size: *const core::Size, registered_depth: *const c_void, depth_dilation: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_rescaleDepth_const__InputArrayR_int_const__OutputArrayR(in_: *const c_void, depth: i32, out: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_rescaleDepth_const__InputArrayR_int_const__OutputArrayR_double(in_: *const c_void, depth: i32, out: *const c_void, depth_factor: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_warpFrame_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_const__OutputArrayR(image: *const c_void, depth: *const c_void, mask: *const c_void, rt: *const c_void, camera_matrix: *const c_void, dist_coeff: *const c_void, warped_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_warpFrame_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(image: *const c_void, depth: *const c_void, mask: *const c_void, rt: *const c_void, camera_matrix: *const c_void, dist_coeff: *const c_void, warped_image: *const c_void, warped_depth: *const c_void, warped_mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_ColoredKinFu_create_const_PtrLParamsGR(_params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_ColoredKinFu_getParams_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_ColoredKinFu_render_const_const__OutputArrayR(instance: *const c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_ColoredKinFu_render_const_const__OutputArrayR_const_Matx44fR(instance: *const c_void, image: *const c_void, camera_pose: *const core::Matx44f, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_ColoredKinFu_getCloud_const_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_ColoredKinFu_getPoints_const_const__OutputArrayR(instance: *const c_void, points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_ColoredKinFu_getNormals_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_ColoredKinFu_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_ColoredKinFu_getPose_const(instance: *const c_void, ocvrs_return: *mut Result<core::Affine3f>);
pub fn cv_colored_kinfu_ColoredKinFu_update_const__InputArrayR_const__InputArrayR(instance: *mut c_void, depth: *const c_void, rgb: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_colored_kinfu_ColoredKinFu_delete(instance: *mut c_void);
pub fn cv_colored_kinfu_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_Params_Params_Matx33f_Vec3f(volume_initial_pose_rot: *const core::Matx33f, volume_initial_pose_transl: *const core::Vec3f, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_Params_Params_Matx44f(volume_initial_pose: *const core::Matx44f, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_Params_setInitialVolumePose_Matx33f_Vec3f(instance: *mut c_void, r: *const core::Matx33f, t: *const core::Vec3f, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_Params_setInitialVolumePose_Matx44f(instance: *mut c_void, homogen_tf: *const core::Matx44f, ocvrs_return: *mut ResultVoid);
pub fn cv_colored_kinfu_Params_defaultParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_Params_coarseParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_Params_hashTSDFParams_bool(is_coarse: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_Params_coloredTSDFParams_bool(is_coarse: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_colored_kinfu_Params_propFrameSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_colored_kinfu_Params_propFrameSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_colored_kinfu_Params_propRgb_frameSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_colored_kinfu_Params_propRgb_frameSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_colored_kinfu_Params_propVolumeType_const(instance: *const c_void, ocvrs_return: *mut crate::rgbd::Kinfu_VolumeType);
pub fn cv_colored_kinfu_Params_propVolumeType_const_VolumeType(instance: *mut c_void, val: crate::rgbd::Kinfu_VolumeType);
pub fn cv_colored_kinfu_Params_propIntr_const(instance: *const c_void, ocvrs_return: *mut core::Matx33f);
pub fn cv_colored_kinfu_Params_propIntr_const_Matx33f(instance: *mut c_void, val: *const core::Matx33f);
pub fn cv_colored_kinfu_Params_propRgb_intr_const(instance: *const c_void, ocvrs_return: *mut core::Matx33f);
pub fn cv_colored_kinfu_Params_propRgb_intr_const_Matx33f(instance: *mut c_void, val: *const core::Matx33f);
pub fn cv_colored_kinfu_Params_propDepthFactor_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propDepthFactor_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propBilateral_sigma_depth_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propBilateral_sigma_depth_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propBilateral_sigma_spatial_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propBilateral_sigma_spatial_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propBilateral_kernel_size_const(instance: *const c_void) -> i32;
pub fn cv_colored_kinfu_Params_propBilateral_kernel_size_const_int(instance: *mut c_void, val: i32);
pub fn cv_colored_kinfu_Params_propPyramidLevels_const(instance: *const c_void) -> i32;
pub fn cv_colored_kinfu_Params_propPyramidLevels_const_int(instance: *mut c_void, val: i32);
pub fn cv_colored_kinfu_Params_propVolumeDims_const(instance: *const c_void, ocvrs_return: *mut core::Vec3i);
pub fn cv_colored_kinfu_Params_propVolumeDims_const_Vec3i(instance: *mut c_void, val: *const core::Vec3i);
pub fn cv_colored_kinfu_Params_propVoxelSize_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propVoxelSize_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propTsdf_min_camera_movement_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propTsdf_min_camera_movement_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propVolumePose_const(instance: *const c_void, ocvrs_return: *mut core::Affine3f);
pub fn cv_colored_kinfu_Params_propVolumePose_const_Affine3f(instance: *mut c_void, val: *const core::Affine3f);
pub fn cv_colored_kinfu_Params_propTsdf_trunc_dist_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propTsdf_trunc_dist_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propTsdf_max_weight_const(instance: *const c_void) -> i32;
pub fn cv_colored_kinfu_Params_propTsdf_max_weight_const_int(instance: *mut c_void, val: i32);
pub fn cv_colored_kinfu_Params_propRaycast_step_factor_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propRaycast_step_factor_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propLightPose_const(instance: *const c_void, ocvrs_return: *mut core::Vec3f);
pub fn cv_colored_kinfu_Params_propLightPose_const_Vec3f(instance: *mut c_void, val: *const core::Vec3f);
pub fn cv_colored_kinfu_Params_propIcpDistThresh_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propIcpDistThresh_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propIcpAngleThresh_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propIcpAngleThresh_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_propIcpIterations_const(instance: *const c_void) -> *mut c_void;
pub fn cv_colored_kinfu_Params_propIcpIterations_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_colored_kinfu_Params_propTruncateThreshold_const(instance: *const c_void) -> f32;
pub fn cv_colored_kinfu_Params_propTruncateThreshold_const_float(instance: *mut c_void, val: f32);
pub fn cv_colored_kinfu_Params_delete(instance: *mut c_void);
pub fn cv_dynafu_DynaFu_create_const_PtrLParamsGR(_params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_dynafu_DynaFu_getParams_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_dynafu_DynaFu_render_const_const__OutputArrayR_const_Matx44fR(instance: *const c_void, image: *const c_void, camera_pose: *const core::Matx44f, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_render_const_const__OutputArrayR(instance: *const c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_getCloud_const_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_getPoints_const_const__OutputArrayR(instance: *const c_void, points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_getNormals_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_getPose_const(instance: *const c_void, ocvrs_return: *mut Result<core::Affine3f>);
pub fn cv_dynafu_DynaFu_update_const__InputArrayR(instance: *mut c_void, depth: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_dynafu_DynaFu_getNodesPos_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_dynafu_DynaFu_marchCubes_const_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, vertices: *const c_void, edges: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_renderSurface_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_bool(instance: *mut c_void, depth_image: *const c_void, vert_image: *const c_void, norm_image: *const c_void, warp: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_renderSurface_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, depth_image: *const c_void, vert_image: *const c_void, norm_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dynafu_DynaFu_delete(instance: *mut c_void);
pub fn cv_kinfu_Intr_Intr(ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr>);
pub fn cv_kinfu_Intr_Intr_float_float_float_float(_fx: f32, _fy: f32, _cx: f32, _cy: f32, ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr>);
pub fn cv_kinfu_Intr_Intr_Matx33f(m: *const core::Matx33f, ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr>);
pub fn cv_kinfu_Intr_scale_const_int(instance: *const crate::rgbd::Kinfu_Intr, pyr: i32, ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr>);
pub fn cv_kinfu_Intr_makeReprojector_const(instance: *const crate::rgbd::Kinfu_Intr, ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr_Reprojector>);
pub fn cv_kinfu_Intr_makeProjector_const(instance: *const crate::rgbd::Kinfu_Intr, ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr_Projector>);
pub fn cv_kinfu_Intr_getMat_const(instance: *const crate::rgbd::Kinfu_Intr, ocvrs_return: *mut Result<core::Matx33f>);
pub fn cv_kinfu_Intr_Projector_Projector_Intr(intr: *const crate::rgbd::Kinfu_Intr, ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr_Projector>);
pub fn cv_kinfu_Intr_Reprojector_Reprojector(ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr_Reprojector>);
pub fn cv_kinfu_Intr_Reprojector_Reprojector_Intr(intr: *const crate::rgbd::Kinfu_Intr, ocvrs_return: *mut Result<crate::rgbd::Kinfu_Intr_Reprojector>);
pub fn cv_kinfu_KinFu_create_const_PtrLParamsGR(_params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_KinFu_getParams_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_KinFu_render_const_const__OutputArrayR(instance: *const c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_KinFu_render_const_const__OutputArrayR_const_Matx44fR(instance: *const c_void, image: *const c_void, camera_pose: *const core::Matx44f, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_KinFu_getCloud_const_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_KinFu_getPoints_const_const__OutputArrayR(instance: *const c_void, points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_KinFu_getNormals_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_KinFu_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_KinFu_getPose_const(instance: *const c_void, ocvrs_return: *mut Result<core::Affine3f>);
pub fn cv_kinfu_KinFu_update_const__InputArrayR(instance: *mut c_void, depth: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_kinfu_KinFu_delete(instance: *mut c_void);
pub fn cv_kinfu_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_Params_Params_Matx33f_Vec3f(volume_initial_pose_rot: *const core::Matx33f, volume_initial_pose_transl: *const core::Vec3f, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_Params_Params_Matx44f(volume_initial_pose: *const core::Matx44f, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_Params_setInitialVolumePose_Matx33f_Vec3f(instance: *mut c_void, r: *const core::Matx33f, t: *const core::Vec3f, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Params_setInitialVolumePose_Matx44f(instance: *mut c_void, homogen_tf: *const core::Matx44f, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Params_defaultParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_Params_coarseParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_Params_hashTSDFParams_bool(is_coarse: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_Params_coloredTSDFParams_bool(is_coarse: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_Params_propFrameSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_kinfu_Params_propFrameSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_kinfu_Params_propVolumeType_const(instance: *const c_void, ocvrs_return: *mut crate::rgbd::Kinfu_VolumeType);
pub fn cv_kinfu_Params_propVolumeType_const_VolumeType(instance: *mut c_void, val: crate::rgbd::Kinfu_VolumeType);
pub fn cv_kinfu_Params_propIntr_const(instance: *const c_void, ocvrs_return: *mut core::Matx33f);
pub fn cv_kinfu_Params_propIntr_const_Matx33f(instance: *mut c_void, val: *const core::Matx33f);
pub fn cv_kinfu_Params_propRgb_intr_const(instance: *const c_void, ocvrs_return: *mut core::Matx33f);
pub fn cv_kinfu_Params_propRgb_intr_const_Matx33f(instance: *mut c_void, val: *const core::Matx33f);
pub fn cv_kinfu_Params_propDepthFactor_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propDepthFactor_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propBilateral_sigma_depth_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propBilateral_sigma_depth_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propBilateral_sigma_spatial_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propBilateral_sigma_spatial_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propBilateral_kernel_size_const(instance: *const c_void) -> i32;
pub fn cv_kinfu_Params_propBilateral_kernel_size_const_int(instance: *mut c_void, val: i32);
pub fn cv_kinfu_Params_propPyramidLevels_const(instance: *const c_void) -> i32;
pub fn cv_kinfu_Params_propPyramidLevels_const_int(instance: *mut c_void, val: i32);
pub fn cv_kinfu_Params_propVolumeDims_const(instance: *const c_void, ocvrs_return: *mut core::Vec3i);
pub fn cv_kinfu_Params_propVolumeDims_const_Vec3i(instance: *mut c_void, val: *const core::Vec3i);
pub fn cv_kinfu_Params_propVoxelSize_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propVoxelSize_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propTsdf_min_camera_movement_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propTsdf_min_camera_movement_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propVolumePose_const(instance: *const c_void, ocvrs_return: *mut core::Affine3f);
pub fn cv_kinfu_Params_propVolumePose_const_Affine3f(instance: *mut c_void, val: *const core::Affine3f);
pub fn cv_kinfu_Params_propTsdf_trunc_dist_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propTsdf_trunc_dist_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propTsdf_max_weight_const(instance: *const c_void) -> i32;
pub fn cv_kinfu_Params_propTsdf_max_weight_const_int(instance: *mut c_void, val: i32);
pub fn cv_kinfu_Params_propRaycast_step_factor_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propRaycast_step_factor_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propLightPose_const(instance: *const c_void, ocvrs_return: *mut core::Vec3f);
pub fn cv_kinfu_Params_propLightPose_const_Vec3f(instance: *mut c_void, val: *const core::Vec3f);
pub fn cv_kinfu_Params_propIcpDistThresh_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propIcpDistThresh_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propIcpAngleThresh_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propIcpAngleThresh_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_propIcpIterations_const(instance: *const c_void) -> *mut c_void;
pub fn cv_kinfu_Params_propIcpIterations_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_kinfu_Params_propTruncateThreshold_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Params_propTruncateThreshold_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_Params_delete(instance: *mut c_void);
pub fn cv_kinfu_Volume_integrate_const__InputArrayR_float_const_Matx44fR_const_IntrR_const_int(instance: *mut c_void, _depth: *const c_void, depth_factor: f32, camera_pose: *const core::Matx44f, intrinsics: *const crate::rgbd::Kinfu_Intr, frame_id: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_integrate_const__InputArrayR_float_const_Matx44fR_const_IntrR(instance: *mut c_void, _depth: *const c_void, depth_factor: f32, camera_pose: *const core::Matx44f, intrinsics: *const crate::rgbd::Kinfu_Intr, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_integrate_const__InputArrayR_const__InputArrayR_float_const_Matx44fR_const_IntrR_const_IntrR_const_int(instance: *mut c_void, _depth: *const c_void, _rgb: *const c_void, depth_factor: f32, camera_pose: *const core::Matx44f, intrinsics: *const crate::rgbd::Kinfu_Intr, rgb_intrinsics: *const crate::rgbd::Kinfu_Intr, frame_id: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_integrate_const__InputArrayR_const__InputArrayR_float_const_Matx44fR_const_IntrR_const_IntrR(instance: *mut c_void, _depth: *const c_void, _rgb: *const c_void, depth_factor: f32, camera_pose: *const core::Matx44f, intrinsics: *const crate::rgbd::Kinfu_Intr, rgb_intrinsics: *const crate::rgbd::Kinfu_Intr, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_raycast_const_const_Matx44fR_const_IntrR_const_SizeR_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, camera_pose: *const core::Matx44f, intrinsics: *const crate::rgbd::Kinfu_Intr, frame_size: *const core::Size, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_raycast_const_const_Matx44fR_const_IntrR_const_SizeR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, camera_pose: *const core::Matx44f, intrinsics: *const crate::rgbd::Kinfu_Intr, frame_size: *const core::Size, points: *const c_void, normals: *const c_void, colors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_fetchNormals_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, _normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_fetchPointsNormals_const_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_Volume_propVoxelSize_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Volume_propVoxelSizeInv_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Volume_propPose_const(instance: *const c_void, ocvrs_return: *mut core::Affine3f);
pub fn cv_kinfu_Volume_propRaycastStepFactor_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_Volume_delete(instance: *mut c_void);
pub fn cv_kinfu_VolumeParams_defaultParams_VolumeType(_volume_type: crate::rgbd::Kinfu_VolumeType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_VolumeParams_coarseParams_VolumeType(_volume_type: crate::rgbd::Kinfu_VolumeType, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_VolumeParams_propType_const(instance: *const c_void, ocvrs_return: *mut crate::rgbd::Kinfu_VolumeType);
pub fn cv_kinfu_VolumeParams_propType_const_VolumeType(instance: *mut c_void, val: crate::rgbd::Kinfu_VolumeType);
pub fn cv_kinfu_VolumeParams_propResolution_const(instance: *const c_void, ocvrs_return: *mut core::Vec3i);
pub fn cv_kinfu_VolumeParams_propResolution_const_Vec3i(instance: *mut c_void, val: *const core::Vec3i);
pub fn cv_kinfu_VolumeParams_propUnitResolution_const(instance: *const c_void) -> i32;
pub fn cv_kinfu_VolumeParams_propUnitResolution_const_int(instance: *mut c_void, val: i32);
pub fn cv_kinfu_VolumeParams_propPose_const(instance: *const c_void, ocvrs_return: *mut core::Affine3f);
pub fn cv_kinfu_VolumeParams_propPose_const_Affine3f(instance: *mut c_void, val: *const core::Affine3f);
pub fn cv_kinfu_VolumeParams_propVoxelSize_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_VolumeParams_propVoxelSize_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_VolumeParams_propTsdfTruncDist_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_VolumeParams_propTsdfTruncDist_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_VolumeParams_propMaxWeight_const(instance: *const c_void) -> i32;
pub fn cv_kinfu_VolumeParams_propMaxWeight_const_int(instance: *mut c_void, val: i32);
pub fn cv_kinfu_VolumeParams_propDepthTruncThreshold_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_VolumeParams_propDepthTruncThreshold_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_VolumeParams_propRaycastStepFactor_const(instance: *const c_void) -> f32;
pub fn cv_kinfu_VolumeParams_propRaycastStepFactor_const_float(instance: *mut c_void, val: f32);
pub fn cv_kinfu_VolumeParams_delete(instance: *mut c_void);
pub fn cv_kinfu_detail_PoseGraph_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_detail_PoseGraph_addNode_size_t_const_Affine3dR_bool(instance: *mut c_void, _node_id: size_t, _pose: *const core::Affine3d, fixed: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_detail_PoseGraph_isNodeExist_const_size_t(instance: *const c_void, node_id: size_t, ocvrs_return: *mut Result<bool>);
pub fn cv_kinfu_detail_PoseGraph_setNodeFixed_size_t_bool(instance: *mut c_void, node_id: size_t, fixed: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_kinfu_detail_PoseGraph_isNodeFixed_const_size_t(instance: *const c_void, node_id: size_t, ocvrs_return: *mut Result<bool>);
pub fn cv_kinfu_detail_PoseGraph_getNodePose_const_size_t(instance: *const c_void, node_id: size_t, ocvrs_return: *mut Result<core::Affine3d>);
pub fn cv_kinfu_detail_PoseGraph_getNodesIds_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_kinfu_detail_PoseGraph_getNumNodes_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_kinfu_detail_PoseGraph_addEdge_size_t_size_t_const_Affine3fR_const_Matx66fR(instance: *mut c_void, _source_node_id: size_t, _target_node_id: size_t, _transformation: *const core::Affine3f, _information: *const core::Matx66f, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_detail_PoseGraph_addEdge_size_t_size_t_const_Affine3fR(instance: *mut c_void, _source_node_id: size_t, _target_node_id: size_t, _transformation: *const core::Affine3f, ocvrs_return: *mut ResultVoid);
pub fn cv_kinfu_detail_PoseGraph_getEdgeStart_const_size_t(instance: *const c_void, i: size_t, ocvrs_return: *mut Result<size_t>);
pub fn cv_kinfu_detail_PoseGraph_getEdgeEnd_const_size_t(instance: *const c_void, i: size_t, ocvrs_return: *mut Result<size_t>);
pub fn cv_kinfu_detail_PoseGraph_getNumEdges_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_kinfu_detail_PoseGraph_isValid_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_kinfu_detail_PoseGraph_optimize_const_TermCriteriaR(instance: *mut c_void, tc: *const core::TermCriteria, ocvrs_return: *mut Result<i32>);
pub fn cv_kinfu_detail_PoseGraph_optimize(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_kinfu_detail_PoseGraph_calcEnergy_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_kinfu_detail_PoseGraph_delete(instance: *mut c_void);
pub fn cv_large_kinfu_LargeKinfu_create_const_PtrLParamsGR(_params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_large_kinfu_LargeKinfu_getParams_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_large_kinfu_LargeKinfu_render_const_const__OutputArrayR(instance: *const c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_large_kinfu_LargeKinfu_render_const_const__OutputArrayR_const_Matx44fR(instance: *const c_void, image: *const c_void, camera_pose: *const core::Matx44f, ocvrs_return: *mut ResultVoid);
pub fn cv_large_kinfu_LargeKinfu_getCloud_const_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_large_kinfu_LargeKinfu_getPoints_const_const__OutputArrayR(instance: *const c_void, points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_large_kinfu_LargeKinfu_getNormals_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_large_kinfu_LargeKinfu_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_large_kinfu_LargeKinfu_getPose_const(instance: *const c_void, ocvrs_return: *mut Result<core::Affine3f>);
pub fn cv_large_kinfu_LargeKinfu_update_const__InputArrayR(instance: *mut c_void, depth: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_large_kinfu_LargeKinfu_delete(instance: *mut c_void);
pub fn cv_large_kinfu_Params_defaultParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_large_kinfu_Params_coarseParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_large_kinfu_Params_hashTSDFParams_bool(is_coarse: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_large_kinfu_Params_propFrameSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_large_kinfu_Params_propFrameSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_large_kinfu_Params_propIntr_const(instance: *const c_void, ocvrs_return: *mut core::Matx33f);
pub fn cv_large_kinfu_Params_propIntr_const_Matx33f(instance: *mut c_void, val: *const core::Matx33f);
pub fn cv_large_kinfu_Params_propRgb_intr_const(instance: *const c_void, ocvrs_return: *mut core::Matx33f);
pub fn cv_large_kinfu_Params_propRgb_intr_const_Matx33f(instance: *mut c_void, val: *const core::Matx33f);
pub fn cv_large_kinfu_Params_propDepthFactor_const(instance: *const c_void) -> f32;
pub fn cv_large_kinfu_Params_propDepthFactor_const_float(instance: *mut c_void, val: f32);
pub fn cv_large_kinfu_Params_propBilateral_sigma_depth_const(instance: *const c_void) -> f32;
pub fn cv_large_kinfu_Params_propBilateral_sigma_depth_const_float(instance: *mut c_void, val: f32);
pub fn cv_large_kinfu_Params_propBilateral_sigma_spatial_const(instance: *const c_void) -> f32;
pub fn cv_large_kinfu_Params_propBilateral_sigma_spatial_const_float(instance: *mut c_void, val: f32);
pub fn cv_large_kinfu_Params_propBilateral_kernel_size_const(instance: *const c_void) -> i32;
pub fn cv_large_kinfu_Params_propBilateral_kernel_size_const_int(instance: *mut c_void, val: i32);
pub fn cv_large_kinfu_Params_propPyramidLevels_const(instance: *const c_void) -> i32;
pub fn cv_large_kinfu_Params_propPyramidLevels_const_int(instance: *mut c_void, val: i32);
pub fn cv_large_kinfu_Params_propTsdf_min_camera_movement_const(instance: *const c_void) -> f32;
pub fn cv_large_kinfu_Params_propTsdf_min_camera_movement_const_float(instance: *mut c_void, val: f32);
pub fn cv_large_kinfu_Params_propLightPose_const(instance: *const c_void, ocvrs_return: *mut core::Vec3f);
pub fn cv_large_kinfu_Params_propLightPose_const_Vec3f(instance: *mut c_void, val: *const core::Vec3f);
pub fn cv_large_kinfu_Params_propIcpDistThresh_const(instance: *const c_void) -> f32;
pub fn cv_large_kinfu_Params_propIcpDistThresh_const_float(instance: *mut c_void, val: f32);
pub fn cv_large_kinfu_Params_propIcpAngleThresh_const(instance: *const c_void) -> f32;
pub fn cv_large_kinfu_Params_propIcpAngleThresh_const_float(instance: *mut c_void, val: f32);
pub fn cv_large_kinfu_Params_propIcpIterations_const(instance: *const c_void) -> *mut c_void;
pub fn cv_large_kinfu_Params_propIcpIterations_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_large_kinfu_Params_propTruncateThreshold_const(instance: *const c_void) -> f32;
pub fn cv_large_kinfu_Params_propTruncateThreshold_const_float(instance: *mut c_void, val: f32);
pub fn cv_large_kinfu_Params_propVolumeParams_const(instance: *const c_void) -> *mut c_void;
pub fn cv_large_kinfu_Params_propVolumeParams_const_VolumeParams(instance: *mut c_void, val: *const c_void);
pub fn cv_large_kinfu_Params_delete(instance: *mut c_void);
pub fn cv_linemod_ColorGradient_ColorGradient(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_ColorGradient_ColorGradient_float_size_t_float(weak_threshold: f32, num_features: size_t, strong_threshold: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_ColorGradient_create_float_size_t_float(weak_threshold: f32, num_features: size_t, strong_threshold: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_ColorGradient_name_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_ColorGradient_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_ColorGradient_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_ColorGradient_propWeak_threshold_const(instance: *const c_void) -> f32;
pub fn cv_linemod_ColorGradient_propWeak_threshold_const_float(instance: *mut c_void, val: f32);
pub fn cv_linemod_ColorGradient_propNum_features_const(instance: *const c_void) -> size_t;
pub fn cv_linemod_ColorGradient_propNum_features_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_linemod_ColorGradient_propStrong_threshold_const(instance: *const c_void) -> f32;
pub fn cv_linemod_ColorGradient_propStrong_threshold_const_float(instance: *mut c_void, val: f32);
pub fn cv_linemod_ColorGradient_to_LineMod_Modality(instance: *mut c_void) -> *mut c_void;
pub fn cv_linemod_ColorGradient_delete(instance: *mut c_void);
pub fn cv_linemod_DepthNormal_DepthNormal(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_DepthNormal_DepthNormal_int_int_size_t_int(distance_threshold: i32, difference_threshold: i32, num_features: size_t, extract_threshold: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_DepthNormal_create_int_int_size_t_int(distance_threshold: i32, difference_threshold: i32, num_features: size_t, extract_threshold: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_DepthNormal_name_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_DepthNormal_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_DepthNormal_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_DepthNormal_propDistance_threshold_const(instance: *const c_void) -> i32;
pub fn cv_linemod_DepthNormal_propDistance_threshold_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_DepthNormal_propDifference_threshold_const(instance: *const c_void) -> i32;
pub fn cv_linemod_DepthNormal_propDifference_threshold_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_DepthNormal_propNum_features_const(instance: *const c_void) -> size_t;
pub fn cv_linemod_DepthNormal_propNum_features_const_size_t(instance: *mut c_void, val: size_t);
pub fn cv_linemod_DepthNormal_propExtract_threshold_const(instance: *const c_void) -> i32;
pub fn cv_linemod_DepthNormal_propExtract_threshold_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_DepthNormal_to_LineMod_Modality(instance: *mut c_void) -> *mut c_void;
pub fn cv_linemod_DepthNormal_delete(instance: *mut c_void);
pub fn cv_linemod_Detector_Detector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Detector_Detector_const_vectorLPtrLModalityGGR_const_vectorLintGR(modalities: *const c_void, t_pyramid: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Detector_match_const_const_vectorLMatGR_float_vectorLMatchGR_const_vectorLStringGR_const__OutputArrayR_const_vectorLMatGR(instance: *const c_void, sources: *const c_void, threshold: f32, matches: *mut c_void, class_ids: *const c_void, quantized_images: *const c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_match_const_const_vectorLMatGR_float_vectorLMatchGR(instance: *const c_void, sources: *const c_void, threshold: f32, matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_addTemplate_const_vectorLMatGR_const_StringR_const_MatR_RectX(instance: *mut c_void, sources: *const c_void, class_id: *const c_char, object_mask: *const c_void, bounding_box: *mut core::Rect, ocvrs_return: *mut Result<i32>);
pub fn cv_linemod_Detector_addTemplate_const_vectorLMatGR_const_StringR_const_MatR(instance: *mut c_void, sources: *const c_void, class_id: *const c_char, object_mask: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_linemod_Detector_addSyntheticTemplate_const_vectorLTemplateGR_const_StringR(instance: *mut c_void, templates: *const c_void, class_id: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_linemod_Detector_getModalities_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Detector_getT_const_int(instance: *const c_void, pyramid_level: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_linemod_Detector_pyramidLevels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_linemod_Detector_getTemplates_const_const_StringR_int(instance: *const c_void, class_id: *const c_char, template_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Detector_numTemplates_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_linemod_Detector_numTemplates_const_const_StringR(instance: *const c_void, class_id: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_linemod_Detector_numClasses_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_linemod_Detector_classIds_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Detector_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_readClass_const_FileNodeR_const_StringR(instance: *mut c_void, fn_: *const c_void, class_id_override: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Detector_readClass_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Detector_writeClass_const_const_StringR_FileStorageR(instance: *const c_void, class_id: *const c_char, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_readClasses_const_vectorLStringGR_const_StringR(instance: *mut c_void, class_ids: *const c_void, format: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_readClasses_const_vectorLStringGR(instance: *mut c_void, class_ids: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_writeClasses_const_const_StringR(instance: *const c_void, format: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_writeClasses_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Detector_delete(instance: *mut c_void);
pub fn cv_linemod_Feature_Feature(ocvrs_return: *mut Result<crate::rgbd::LineMod_Feature>);
pub fn cv_linemod_Feature_Feature_int_int_int(x: i32, y: i32, label: i32, ocvrs_return: *mut Result<crate::rgbd::LineMod_Feature>);
pub fn cv_linemod_Feature_read_const_FileNodeR(instance: *const crate::rgbd::LineMod_Feature, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Feature_write_const_FileStorageR(instance: *const crate::rgbd::LineMod_Feature, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Match_Match(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Match_Match_int_int_float_const_StringR_int(x: i32, y: i32, similarity: f32, class_id: *const c_char, template_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Match_operatorL_const_const_MatchR(instance: *const c_void, rhs: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_linemod_Match_operatorEQ_const_const_MatchR(instance: *const c_void, rhs: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_linemod_Match_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_linemod_Match_propX_const(instance: *const c_void) -> i32;
pub fn cv_linemod_Match_propX_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_Match_propY_const(instance: *const c_void) -> i32;
pub fn cv_linemod_Match_propY_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_Match_propSimilarity_const(instance: *const c_void) -> f32;
pub fn cv_linemod_Match_propSimilarity_const_float(instance: *mut c_void, val: f32);
pub fn cv_linemod_Match_propClass_id_const(instance: *const c_void) -> *mut c_void;
pub fn cv_linemod_Match_propClass_id_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_linemod_Match_propTemplate_id_const(instance: *const c_void) -> i32;
pub fn cv_linemod_Match_propTemplate_id_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_Match_delete(instance: *mut c_void);
pub fn cv_linemod_Modality_process_const_const_MatR_const_MatR(instance: *const c_void, src: *const c_void, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Modality_process_const_const_MatR(instance: *const c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Modality_name_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Modality_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Modality_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Modality_create_const_StringR(modality_type: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Modality_create_const_FileNodeR(fn_: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_linemod_Modality_to_LineMod_ColorGradient(instance: *mut c_void) -> *mut c_void;
pub fn cv_linemod_Modality_to_LineMod_DepthNormal(instance: *mut c_void) -> *mut c_void;
pub fn cv_linemod_Modality_delete(instance: *mut c_void);
pub fn cv_linemod_QuantizedPyramid_quantize_const_MatR(instance: *const c_void, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_QuantizedPyramid_extractTemplate_const_TemplateR(instance: *const c_void, templ: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_linemod_QuantizedPyramid_pyrDown(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_QuantizedPyramid_delete(instance: *mut c_void);
pub fn cv_linemod_Template_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Template_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_linemod_Template_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_linemod_Template_defaultNew_const() -> *mut c_void;
pub fn cv_linemod_Template_propWidth_const(instance: *const c_void) -> i32;
pub fn cv_linemod_Template_propWidth_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_Template_propHeight_const(instance: *const c_void) -> i32;
pub fn cv_linemod_Template_propHeight_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_Template_propPyramid_level_const(instance: *const c_void) -> i32;
pub fn cv_linemod_Template_propPyramid_level_const_int(instance: *mut c_void, val: i32);
pub fn cv_linemod_Template_propFeatures_const(instance: *const c_void) -> *mut c_void;
pub fn cv_linemod_Template_propFeatures_const_vectorLFeatureG(instance: *mut c_void, val: *const c_void);
pub fn cv_linemod_Template_delete(instance: *mut c_void);
pub fn cv_rgbd_DepthCleaner_DepthCleaner(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_DepthCleaner_DepthCleaner_int_int_int(depth: i32, window_size: i32, method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_DepthCleaner_DepthCleaner_int(depth: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_DepthCleaner_create_int_int_int(depth: i32, window_size: i32, method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_DepthCleaner_create_int(depth: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_DepthCleaner_operator___const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, depth: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_DepthCleaner_initialize_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_DepthCleaner_getWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_DepthCleaner_setWindowSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_DepthCleaner_getDepth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_DepthCleaner_setDepth_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_DepthCleaner_getMethod_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_DepthCleaner_setMethod_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_DepthCleaner_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_DepthCleaner_delete(instance: *mut c_void);
pub fn cv_rgbd_FastICPOdometry_FastICPOdometry(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_FastICPOdometry_FastICPOdometry_const_MatR_float_float_float_float_int_const_vectorLintGR(camera_matrix: *const c_void, max_dist_diff: f32, angle_threshold: f32, sigma_depth: f32, sigma_spatial: f32, kernel_size: i32, iter_counts: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_FastICPOdometry_FastICPOdometry_const_MatR(camera_matrix: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_FastICPOdometry_create_const_MatR_float_float_float_float_int_const_vectorLintGR(camera_matrix: *const c_void, max_dist_diff: f32, angle_threshold: f32, sigma_depth: f32, sigma_spatial: f32, kernel_size: i32, iter_counts: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_FastICPOdometry_create_const_MatR(camera_matrix: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_FastICPOdometry_prepareFrameCache_const_PtrLOdometryFrameGR_int(instance: *const c_void, frame: *mut c_void, cache_type: i32, ocvrs_return: *mut Result<core::Size>);
pub fn cv_rgbd_FastICPOdometry_getCameraMatrix_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_FastICPOdometry_setCameraMatrix_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_FastICPOdometry_getMaxDistDiff_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_FastICPOdometry_setMaxDistDiff_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_FastICPOdometry_getAngleThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_FastICPOdometry_setAngleThreshold_float(instance: *mut c_void, f: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_FastICPOdometry_getSigmaDepth_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_FastICPOdometry_setSigmaDepth_float(instance: *mut c_void, f: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_FastICPOdometry_getSigmaSpatial_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_FastICPOdometry_setSigmaSpatial_float(instance: *mut c_void, f: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_FastICPOdometry_getKernelSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_FastICPOdometry_setKernelSize_int(instance: *mut c_void, f: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_FastICPOdometry_getIterationCounts_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_FastICPOdometry_setIterationCounts_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_FastICPOdometry_getTransformType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_FastICPOdometry_setTransformType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_FastICPOdometry_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_FastICPOdometry_to_Odometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_FastICPOdometry_delete(instance: *mut c_void);
pub fn cv_rgbd_ICPOdometry_ICPOdometry(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_ICPOdometry_ICPOdometry_const_MatR_float_float_float_float_const_vectorLintGR_int(camera_matrix: *const c_void, min_depth: f32, max_depth: f32, max_depth_diff: f32, max_points_part: f32, iter_counts: *const c_void, transform_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_ICPOdometry_ICPOdometry_const_MatR(camera_matrix: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_ICPOdometry_create_const_MatR_float_float_float_float_const_vectorLintGR_int(camera_matrix: *const c_void, min_depth: f32, max_depth: f32, max_depth_diff: f32, max_points_part: f32, iter_counts: *const c_void, transform_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_ICPOdometry_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_ICPOdometry_prepareFrameCache_const_PtrLOdometryFrameGR_int(instance: *const c_void, frame: *mut c_void, cache_type: i32, ocvrs_return: *mut Result<core::Size>);
pub fn cv_rgbd_ICPOdometry_getCameraMatrix_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_ICPOdometry_setCameraMatrix_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getMinDepth_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_ICPOdometry_setMinDepth_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getMaxDepth_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_ICPOdometry_setMaxDepth_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getMaxDepthDiff_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_ICPOdometry_setMaxDepthDiff_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getIterationCounts_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_ICPOdometry_setIterationCounts_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getMaxPointsPart_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_ICPOdometry_setMaxPointsPart_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getTransformType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_ICPOdometry_setTransformType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getMaxTranslation_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_ICPOdometry_setMaxTranslation_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getMaxRotation_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_ICPOdometry_setMaxRotation_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_ICPOdometry_getNormalsComputer_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_ICPOdometry_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_ICPOdometry_to_Odometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_ICPOdometry_delete(instance: *mut c_void);
pub fn cv_rgbd_Odometry_DEFAULT_MIN_DEPTH(ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_Odometry_DEFAULT_MAX_DEPTH(ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_Odometry_DEFAULT_MAX_DEPTH_DIFF(ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_Odometry_DEFAULT_MAX_POINTS_PART(ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_Odometry_DEFAULT_MAX_TRANSLATION(ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_Odometry_DEFAULT_MAX_ROTATION(ocvrs_return: *mut Result<f32>);
pub fn cv_rgbd_Odometry_compute_const_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_const__OutputArrayR_const_MatR(instance: *const c_void, src_image: *const c_void, src_depth: *const c_void, src_mask: *const c_void, dst_image: *const c_void, dst_depth: *const c_void, dst_mask: *const c_void, rt: *const c_void, init_rt: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_Odometry_compute_const_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_const__OutputArrayR(instance: *const c_void, src_image: *const c_void, src_depth: *const c_void, src_mask: *const c_void, dst_image: *const c_void, dst_depth: *const c_void, dst_mask: *const c_void, rt: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_Odometry_compute_const_PtrLOdometryFrameGR_PtrLOdometryFrameGR_const__OutputArrayR_const_MatR(instance: *const c_void, src_frame: *mut c_void, dst_frame: *mut c_void, rt: *const c_void, init_rt: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_Odometry_compute_const_PtrLOdometryFrameGR_PtrLOdometryFrameGR_const__OutputArrayR(instance: *const c_void, src_frame: *mut c_void, dst_frame: *mut c_void, rt: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_rgbd_Odometry_prepareFrameCache_const_PtrLOdometryFrameGR_int(instance: *const c_void, frame: *mut c_void, cache_type: i32, ocvrs_return: *mut Result<core::Size>);
pub fn cv_rgbd_Odometry_create_const_StringR(odometry_type: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_Odometry_getCameraMatrix_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_Odometry_setCameraMatrix_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_Odometry_getTransformType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_Odometry_setTransformType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_Odometry_to_FastICPOdometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_Odometry_to_ICPOdometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_Odometry_to_RgbdICPOdometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_Odometry_to_RgbdOdometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_Odometry_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_Odometry_delete(instance: *mut c_void);
pub fn cv_rgbd_OdometryFrame_OdometryFrame(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_OdometryFrame_OdometryFrame_const_MatR_const_MatR_const_MatR_const_MatR_int(image: *const c_void, depth: *const c_void, mask: *const c_void, normals: *const c_void, id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_OdometryFrame_OdometryFrame_const_MatR_const_MatR(image: *const c_void, depth: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_OdometryFrame_create_const_MatR_const_MatR_const_MatR_const_MatR_int(image: *const c_void, depth: *const c_void, mask: *const c_void, normals: *const c_void, id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_OdometryFrame_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_OdometryFrame_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_OdometryFrame_releasePyramids(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_OdometryFrame_propPyramidImage_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramidImage_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_propPyramidDepth_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramidDepth_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_propPyramidMask_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramidMask_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_propPyramidCloud_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramidCloud_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_propPyramid_dI_dx_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramid_dI_dx_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_propPyramid_dI_dy_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramid_dI_dy_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_propPyramidTexturedMask_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramidTexturedMask_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_propPyramidNormals_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramidNormals_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_propPyramidNormalsMask_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_propPyramidNormalsMask_const_vectorLMatG(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_OdometryFrame_to_RgbdFrame(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_OdometryFrame_delete(instance: *mut c_void);
pub fn cv_rgbd_RgbdFrame_RgbdFrame(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdFrame_RgbdFrame_const_MatR_const_MatR_const_MatR_const_MatR_int(image: *const c_void, depth: *const c_void, mask: *const c_void, normals: *const c_void, id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdFrame_RgbdFrame_const_MatR_const_MatR(image: *const c_void, depth: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdFrame_create_const_MatR_const_MatR_const_MatR_const_MatR_int(image: *const c_void, depth: *const c_void, mask: *const c_void, normals: *const c_void, id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdFrame_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdFrame_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdFrame_propID_const(instance: *const c_void) -> i32;
pub fn cv_rgbd_RgbdFrame_propID_const_int(instance: *mut c_void, val: i32);
pub fn cv_rgbd_RgbdFrame_propImage_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdFrame_propImage_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_RgbdFrame_propDepth_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdFrame_propDepth_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_RgbdFrame_propMask_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdFrame_propMask_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_RgbdFrame_propNormals_const(instance: *const c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdFrame_propNormals_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_rgbd_RgbdFrame_to_OdometryFrame(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdFrame_delete(instance: *mut c_void);
pub fn cv_rgbd_RgbdICPOdometry_RgbdICPOdometry(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_RgbdICPOdometry_const_MatR_float_float_float_float_const_vectorLintGR_const_vectorLfloatGR_int(camera_matrix: *const c_void, min_depth: f32, max_depth: f32, max_depth_diff: f32, max_points_part: f32, iter_counts: *const c_void, min_gradient_magnitudes: *const c_void, transform_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_RgbdICPOdometry_const_MatR(camera_matrix: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_create_const_MatR_float_float_float_float_const_vectorLintGR_const_vectorLfloatGR_int(camera_matrix: *const c_void, min_depth: f32, max_depth: f32, max_depth_diff: f32, max_points_part: f32, iter_counts: *const c_void, min_gradient_magnitudes: *const c_void, transform_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_prepareFrameCache_const_PtrLOdometryFrameGR_int(instance: *const c_void, frame: *mut c_void, cache_type: i32, ocvrs_return: *mut Result<core::Size>);
pub fn cv_rgbd_RgbdICPOdometry_getCameraMatrix_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_setCameraMatrix_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getMinDepth_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdICPOdometry_setMinDepth_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getMaxDepth_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdICPOdometry_setMaxDepth_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getMaxDepthDiff_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdICPOdometry_setMaxDepthDiff_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getMaxPointsPart_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdICPOdometry_setMaxPointsPart_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getIterationCounts_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_setIterationCounts_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getMinGradientMagnitudes_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_setMinGradientMagnitudes_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getTransformType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdICPOdometry_setTransformType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getMaxTranslation_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdICPOdometry_setMaxTranslation_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getMaxRotation_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdICPOdometry_setMaxRotation_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdICPOdometry_getNormalsComputer_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdICPOdometry_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdICPOdometry_to_Odometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdICPOdometry_delete(instance: *mut c_void);
pub fn cv_rgbd_RgbdNormals_RgbdNormals(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdNormals_RgbdNormals_int_int_int_const__InputArrayR_int_int(rows: i32, cols: i32, depth: i32, k: *const c_void, window_size: i32, method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdNormals_RgbdNormals_int_int_int_const__InputArrayR(rows: i32, cols: i32, depth: i32, k: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdNormals_create_int_int_int_const__InputArrayR_int_int(rows: i32, cols: i32, depth: i32, k: *const c_void, window_size: i32, method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdNormals_create_int_int_int_const__InputArrayR(rows: i32, cols: i32, depth: i32, k: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdNormals_operator___const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, points: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdNormals_initialize_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdNormals_getRows_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdNormals_setRows_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdNormals_getCols_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdNormals_setCols_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdNormals_getWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdNormals_setWindowSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdNormals_getDepth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdNormals_setDepth_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdNormals_getK_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdNormals_setK_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdNormals_getMethod_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdNormals_setMethod_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdNormals_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdNormals_delete(instance: *mut c_void);
pub fn cv_rgbd_RgbdOdometry_RgbdOdometry(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdOdometry_RgbdOdometry_const_MatR_float_float_float_const_vectorLintGR_const_vectorLfloatGR_float_int(camera_matrix: *const c_void, min_depth: f32, max_depth: f32, max_depth_diff: f32, iter_counts: *const c_void, min_gradient_magnitudes: *const c_void, max_points_part: f32, transform_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdOdometry_RgbdOdometry_const_MatR(camera_matrix: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdOdometry_create_const_MatR_float_float_float_const_vectorLintGR_const_vectorLfloatGR_float_int(camera_matrix: *const c_void, min_depth: f32, max_depth: f32, max_depth_diff: f32, iter_counts: *const c_void, min_gradient_magnitudes: *const c_void, max_points_part: f32, transform_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdOdometry_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdOdometry_prepareFrameCache_const_PtrLOdometryFrameGR_int(instance: *const c_void, frame: *mut c_void, cache_type: i32, ocvrs_return: *mut Result<core::Size>);
pub fn cv_rgbd_RgbdOdometry_getCameraMatrix_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdOdometry_setCameraMatrix_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getMinDepth_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdOdometry_setMinDepth_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getMaxDepth_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdOdometry_setMaxDepth_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getMaxDepthDiff_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdOdometry_setMaxDepthDiff_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getIterationCounts_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdOdometry_setIterationCounts_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getMinGradientMagnitudes_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdOdometry_setMinGradientMagnitudes_const_MatR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getMaxPointsPart_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdOdometry_setMaxPointsPart_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getTransformType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdOdometry_setTransformType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getMaxTranslation_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdOdometry_setMaxTranslation_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_getMaxRotation_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdOdometry_setMaxRotation_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdOdometry_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdOdometry_to_Odometry(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdOdometry_delete(instance: *mut c_void);
pub fn cv_rgbd_RgbdPlane_RgbdPlane_int(method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdPlane_RgbdPlane(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdPlane_RgbdPlane_int_int_int_double_double_double_double(method: i32, block_size: i32, min_size: i32, threshold: f64, sensor_error_a: f64, sensor_error_b: f64, sensor_error_c: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdPlane_RgbdPlane_int_int_int_double(method: i32, block_size: i32, min_size: i32, threshold: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdPlane_create_int_int_int_double_double_double_double(method: i32, block_size: i32, min_size: i32, threshold: f64, sensor_error_a: f64, sensor_error_b: f64, sensor_error_c: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdPlane_create_int_int_int_double(method: i32, block_size: i32, min_size: i32, threshold: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rgbd_RgbdPlane_operator___const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, points3d: *const c_void, normals: *const c_void, mask: *const c_void, plane_coefficients: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_operator___const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, points3d: *const c_void, mask: *const c_void, plane_coefficients: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_getBlockSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdPlane_setBlockSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_getMinSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdPlane_setMinSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_getMethod_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rgbd_RgbdPlane_setMethod_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdPlane_setThreshold_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_getSensorErrorA_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdPlane_setSensorErrorA_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_getSensorErrorB_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdPlane_setSensorErrorB_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_getSensorErrorC_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_rgbd_RgbdPlane_setSensorErrorC_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_rgbd_RgbdPlane_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rgbd_RgbdPlane_delete(instance: *mut c_void);
