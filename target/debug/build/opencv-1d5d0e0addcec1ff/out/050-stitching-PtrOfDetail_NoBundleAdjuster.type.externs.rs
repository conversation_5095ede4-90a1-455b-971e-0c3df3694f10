pub fn cv_PtrLcv_detail_NoBundleAdjusterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoBundleAdjusterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoBundleAdjusterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_NoBundleAdjusterG_to_PtrOfDetail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoBundleAdjusterG_to_PtrOfDetail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoBundleAdjusterG_new_const_NoBundleAdjuster(val: *mut c_void) -> *mut c_void;
