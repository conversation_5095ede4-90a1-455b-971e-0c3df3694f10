extern "C" {
	const cv::dpm::DPMDetector* cv_PtrLcv_dpm_DPMDetectorG_getInnerPtr_const(const cv::Ptr<cv::dpm::DPMDetector>* instance) {
			return instance->get();
	}
	
	cv::dpm::DPMDetector* cv_PtrLcv_dpm_DPMDetectorG_getInnerPtrMut(cv::Ptr<cv::dpm::DPMDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dpm_DPMDetectorG_delete(cv::Ptr<cv::dpm::DPMDetector>* instance) {
			delete instance;
	}
	
}

