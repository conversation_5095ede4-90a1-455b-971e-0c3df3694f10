extern "C" {
	const cv::colored_kinfu::Params* cv_PtrLcv_colored_kinfu_ParamsG_getInnerPtr_const(const cv::Ptr<cv::colored_kinfu::Params>* instance) {
			return instance->get();
	}
	
	cv::colored_kinfu::Params* cv_PtrLcv_colored_kinfu_ParamsG_getInnerPtrMut(cv::Ptr<cv::colored_kinfu::Params>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_colored_kinfu_ParamsG_delete(cv::Ptr<cv::colored_kinfu::Params>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::colored_kinfu::Params>* cv_PtrLcv_colored_kinfu_ParamsG_new_const_Params(cv::colored_kinfu::Params* val) {
			return new cv::Ptr<cv::colored_kinfu::Params>(val);
	}
	
}

