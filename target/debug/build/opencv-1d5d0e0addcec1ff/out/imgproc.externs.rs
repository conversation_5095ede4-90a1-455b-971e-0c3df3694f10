pub fn cv_Canny_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double(dx: *const c_void, dy: *const c_void, edges: *const c_void, threshold1: f64, threshold2: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_Canny_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_double_bool(dx: *const c_void, dy: *const c_void, edges: *const c_void, threshold1: f64, threshold2: f64, l2gradient: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_Canny_const__InputArrayR_const__OutputArrayR_double_double(image: *const c_void, edges: *const c_void, threshold1: f64, threshold2: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_Canny_const__InputArrayR_const__OutputArrayR_double_double_int_bool(image: *const c_void, edges: *const c_void, threshold1: f64, threshold2: f64, aperture_size: i32, l2gradient: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_EMD_const__InputArrayR_const__InputArrayR_int(signature1: *const c_void, signature2: *const c_void, dist_type: i32, ocvrs_return: *mut Result<f32>);
pub fn cv_EMD_const__InputArrayR_const__InputArrayR_int_const__InputArrayR_floatX_const__OutputArrayR(signature1: *const c_void, signature2: *const c_void, dist_type: i32, cost: *const c_void, lower_bound: *mut f32, flow: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_GaussianBlur_const__InputArrayR_const__OutputArrayR_Size_double(src: *const c_void, dst: *const c_void, ksize: *const core::Size, sigma_x: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GaussianBlur_const__InputArrayR_const__OutputArrayR_Size_double_double_int(src: *const c_void, dst: *const c_void, ksize: *const core::Size, sigma_x: f64, sigma_y: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_HoughCircles_const__InputArrayR_const__OutputArrayR_int_double_double(image: *const c_void, circles: *const c_void, method: i32, dp: f64, min_dist: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_HoughCircles_const__InputArrayR_const__OutputArrayR_int_double_double_double_double_int_int(image: *const c_void, circles: *const c_void, method: i32, dp: f64, min_dist: f64, param1: f64, param2: f64, min_radius: i32, max_radius: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_HoughLinesP_const__InputArrayR_const__OutputArrayR_double_double_int(image: *const c_void, lines: *const c_void, rho: f64, theta: f64, threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_HoughLinesP_const__InputArrayR_const__OutputArrayR_double_double_int_double_double(image: *const c_void, lines: *const c_void, rho: f64, theta: f64, threshold: i32, min_line_length: f64, max_line_gap: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_HoughLinesPointSet_const__InputArrayR_const__OutputArrayR_int_int_double_double_double_double_double_double(point: *const c_void, lines: *const c_void, lines_max: i32, threshold: i32, min_rho: f64, max_rho: f64, rho_step: f64, min_theta: f64, max_theta: f64, theta_step: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_HoughLines_const__InputArrayR_const__OutputArrayR_double_double_int(image: *const c_void, lines: *const c_void, rho: f64, theta: f64, threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_HoughLines_const__InputArrayR_const__OutputArrayR_double_double_int_double_double_double_double(image: *const c_void, lines: *const c_void, rho: f64, theta: f64, threshold: i32, srn: f64, stn: f64, min_theta: f64, max_theta: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_HuMoments_const_MomentsR_const__OutputArrayR(m: *const core::Moments, hu: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HuMoments_const_MomentsR_doubleXX(moments: *const core::Moments, hu: *mut [f64; 7], ocvrs_return: *mut ResultVoid);
pub fn cv_Laplacian_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, ddepth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Laplacian_const__InputArrayR_const__OutputArrayR_int_int_double_double_int(src: *const c_void, dst: *const c_void, ddepth: i32, ksize: i32, scale: f64, delta: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Scharr_const__InputArrayR_const__OutputArrayR_int_int_int(src: *const c_void, dst: *const c_void, ddepth: i32, dx: i32, dy: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Scharr_const__InputArrayR_const__OutputArrayR_int_int_int_double_double_int(src: *const c_void, dst: *const c_void, ddepth: i32, dx: i32, dy: i32, scale: f64, delta: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Sobel_const__InputArrayR_const__OutputArrayR_int_int_int(src: *const c_void, dst: *const c_void, ddepth: i32, dx: i32, dy: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_Sobel_const__InputArrayR_const__OutputArrayR_int_int_int_int_double_double_int(src: *const c_void, dst: *const c_void, ddepth: i32, dx: i32, dy: i32, ksize: i32, scale: f64, delta: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_accumulateProduct_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_accumulateProduct_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputArrayR(src1: *const c_void, src2: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_accumulateSquare_const__InputArrayR_const__InputOutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_accumulateSquare_const__InputArrayR_const__InputOutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_accumulateWeighted_const__InputArrayR_const__InputOutputArrayR_double(src: *const c_void, dst: *const c_void, alpha: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_accumulateWeighted_const__InputArrayR_const__InputOutputArrayR_double_const__InputArrayR(src: *const c_void, dst: *const c_void, alpha: f64, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_accumulate_const__InputArrayR_const__InputOutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_accumulate_const__InputArrayR_const__InputOutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_adaptiveThreshold_const__InputArrayR_const__OutputArrayR_double_int_int_int_double(src: *const c_void, dst: *const c_void, max_value: f64, adaptive_method: i32, threshold_type: i32, block_size: i32, c: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_applyColorMap_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, user_color: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_applyColorMap_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, colormap: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_approxPolyDP_const__InputArrayR_const__OutputArrayR_double_bool(curve: *const c_void, approx_curve: *const c_void, epsilon: f64, closed: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_arcLength_const__InputArrayR_bool(curve: *const c_void, closed: bool, ocvrs_return: *mut Result<f64>);
pub fn cv_arrowedLine_const__InputOutputArrayR_Point_Point_const_ScalarR(img: *const c_void, pt1: *const core::Point, pt2: *const core::Point, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_arrowedLine_const__InputOutputArrayR_Point_Point_const_ScalarR_int_int_int_double(img: *const c_void, pt1: *const core::Point, pt2: *const core::Point, color: *const core::Scalar, thickness: i32, line_type: i32, shift: i32, tip_length: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_bilateralFilter_const__InputArrayR_const__OutputArrayR_int_double_double(src: *const c_void, dst: *const c_void, d: i32, sigma_color: f64, sigma_space: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_bilateralFilter_const__InputArrayR_const__OutputArrayR_int_double_double_int(src: *const c_void, dst: *const c_void, d: i32, sigma_color: f64, sigma_space: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_blendLinear_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src1: *const c_void, src2: *const c_void, weights1: *const c_void, weights2: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_blur_const__InputArrayR_const__OutputArrayR_Size(src: *const c_void, dst: *const c_void, ksize: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_blur_const__InputArrayR_const__OutputArrayR_Size_Point_int(src: *const c_void, dst: *const c_void, ksize: *const core::Size, anchor: *const core::Point, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_boundingRect_const__InputArrayR(array: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_boxFilter_const__InputArrayR_const__OutputArrayR_int_Size(src: *const c_void, dst: *const c_void, ddepth: i32, ksize: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_boxFilter_const__InputArrayR_const__OutputArrayR_int_Size_Point_bool_int(src: *const c_void, dst: *const c_void, ddepth: i32, ksize: *const core::Size, anchor: *const core::Point, normalize: bool, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_boxPoints_RotatedRect_const__OutputArrayR(box_: *const core::RotatedRect, points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_buildPyramid_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, maxlevel: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_buildPyramid_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, maxlevel: i32, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_calcBackProject_const__InputArrayR_const_vectorLintGR_const__InputArrayR_const__OutputArrayR_const_vectorLfloatGR_double(images: *const c_void, channels: *const c_void, hist: *const c_void, dst: *const c_void, ranges: *const c_void, scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_calcHist_const__InputArrayR_const_vectorLintGR_const__InputArrayR_const__OutputArrayR_const_vectorLintGR_const_vectorLfloatGR(images: *const c_void, channels: *const c_void, mask: *const c_void, hist: *const c_void, hist_size: *const c_void, ranges: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_calcHist_const__InputArrayR_const_vectorLintGR_const__InputArrayR_const__OutputArrayR_const_vectorLintGR_const_vectorLfloatGR_bool(images: *const c_void, channels: *const c_void, mask: *const c_void, hist: *const c_void, hist_size: *const c_void, ranges: *const c_void, accumulate: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_circle_const__InputOutputArrayR_Point_int_const_ScalarR(img: *const c_void, center: *const core::Point, radius: i32, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_circle_const__InputOutputArrayR_Point_int_const_ScalarR_int_int_int(img: *const c_void, center: *const core::Point, radius: i32, color: *const core::Scalar, thickness: i32, line_type: i32, shift: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_clipLine_Rect_PointR_PointR(img_rect: *const core::Rect, pt1: *mut core::Point, pt2: *mut core::Point, ocvrs_return: *mut Result<bool>);
pub fn cv_clipLine_Size2l_Point2lR_Point2lR(img_size: *const core::Size2l, pt1: *mut core::Point2l, pt2: *mut core::Point2l, ocvrs_return: *mut Result<bool>);
pub fn cv_clipLine_Size_PointR_PointR(img_size: *const core::Size, pt1: *mut core::Point, pt2: *mut core::Point, ocvrs_return: *mut Result<bool>);
pub fn cv_compareHist_const_SparseMatR_const_SparseMatR_int(h1: *const c_void, h2: *const c_void, method: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_compareHist_const__InputArrayR_const__InputArrayR_int(h1: *const c_void, h2: *const c_void, method: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_connectedComponentsWithStats_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(image: *const c_void, labels: *const c_void, stats: *const c_void, centroids: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_connectedComponentsWithStats_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_int(image: *const c_void, labels: *const c_void, stats: *const c_void, centroids: *const c_void, connectivity: i32, ltype: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_connectedComponentsWithStats_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_int_int(image: *const c_void, labels: *const c_void, stats: *const c_void, centroids: *const c_void, connectivity: i32, ltype: i32, ccltype: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_connectedComponents_const__InputArrayR_const__OutputArrayR(image: *const c_void, labels: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_connectedComponents_const__InputArrayR_const__OutputArrayR_int_int(image: *const c_void, labels: *const c_void, connectivity: i32, ltype: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_connectedComponents_const__InputArrayR_const__OutputArrayR_int_int_int(image: *const c_void, labels: *const c_void, connectivity: i32, ltype: i32, ccltype: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_contourArea_const__InputArrayR(contour: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_contourArea_const__InputArrayR_bool(contour: *const c_void, oriented: bool, ocvrs_return: *mut Result<f64>);
pub fn cv_convertMaps_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int(map1: *const c_void, map2: *const c_void, dstmap1: *const c_void, dstmap2: *const c_void, dstmap1type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_convertMaps_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int_bool(map1: *const c_void, map2: *const c_void, dstmap1: *const c_void, dstmap2: *const c_void, dstmap1type: i32, nninterpolation: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_convexHull_const__InputArrayR_const__OutputArrayR(points: *const c_void, hull: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_convexHull_const__InputArrayR_const__OutputArrayR_bool_bool(points: *const c_void, hull: *const c_void, clockwise: bool, return_points: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_convexityDefects_const__InputArrayR_const__InputArrayR_const__OutputArrayR(contour: *const c_void, convexhull: *const c_void, convexity_defects: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cornerEigenValsAndVecs_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, block_size: i32, ksize: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cornerEigenValsAndVecs_const__InputArrayR_const__OutputArrayR_int_int_int(src: *const c_void, dst: *const c_void, block_size: i32, ksize: i32, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cornerHarris_const__InputArrayR_const__OutputArrayR_int_int_double(src: *const c_void, dst: *const c_void, block_size: i32, ksize: i32, k: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_cornerHarris_const__InputArrayR_const__OutputArrayR_int_int_double_int(src: *const c_void, dst: *const c_void, block_size: i32, ksize: i32, k: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cornerMinEigenVal_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, block_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cornerMinEigenVal_const__InputArrayR_const__OutputArrayR_int_int_int(src: *const c_void, dst: *const c_void, block_size: i32, ksize: i32, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cornerSubPix_const__InputArrayR_const__InputOutputArrayR_Size_Size_TermCriteria(image: *const c_void, corners: *const c_void, win_size: *const core::Size, zero_zone: *const core::Size, criteria: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_createCLAHE(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createCLAHE_double_Size(clip_limit: f64, tile_grid_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createGeneralizedHoughBallard(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createGeneralizedHoughGuil(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createHanningWindow_const__OutputArrayR_Size_int(dst: *const c_void, win_size: *const core::Size, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_createLineSegmentDetector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createLineSegmentDetector_int_double_double_double_double_double_double_int(refine: i32, scale: f64, sigma_scale: f64, quant: f64, ang_th: f64, log_eps: f64, density_th: f64, n_bins: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cvtColorTwoPlane_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int(src1: *const c_void, src2: *const c_void, dst: *const c_void, code: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cvtColor_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, code: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_cvtColor_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, code: i32, dst_cn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_demosaicing_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, code: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_demosaicing_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, code: i32, dst_cn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_dilate_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, kernel: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_dilate_const__InputArrayR_const__OutputArrayR_const__InputArrayR_Point_int_int_const_ScalarR(src: *const c_void, dst: *const c_void, kernel: *const c_void, anchor: *const core::Point, iterations: i32, border_type: i32, border_value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_distanceTransform_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, labels: *const c_void, distance_type: i32, mask_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_distanceTransform_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int_int_int(src: *const c_void, dst: *const c_void, labels: *const c_void, distance_type: i32, mask_size: i32, label_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_distanceTransform_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, distance_type: i32, mask_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_distanceTransform_const__InputArrayR_const__OutputArrayR_int_int_int(src: *const c_void, dst: *const c_void, distance_type: i32, mask_size: i32, dst_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_divSpectrums_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int(a: *const c_void, b: *const c_void, c: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_divSpectrums_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_bool(a: *const c_void, b: *const c_void, c: *const c_void, flags: i32, conj_b: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_drawContours_const__InputOutputArrayR_const__InputArrayR_int_const_ScalarR(image: *const c_void, contours: *const c_void, contour_idx: i32, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_drawContours_const__InputOutputArrayR_const__InputArrayR_int_const_ScalarR_int_int_const__InputArrayR_int_Point(image: *const c_void, contours: *const c_void, contour_idx: i32, color: *const core::Scalar, thickness: i32, line_type: i32, hierarchy: *const c_void, max_level: i32, offset: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_drawMarker_const__InputOutputArrayR_Point_const_ScalarR(img: *const c_void, position: *const core::Point, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_drawMarker_const__InputOutputArrayR_Point_const_ScalarR_int_int_int_int(img: *const c_void, position: *const core::Point, color: *const core::Scalar, marker_type: i32, marker_size: i32, thickness: i32, line_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ellipse2Poly_Point2d_Size2d_int_int_int_int_vectorLPoint2dGR(center: *const core::Point2d, axes: *const core::Size2d, angle: i32, arc_start: i32, arc_end: i32, delta: i32, pts: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ellipse2Poly_Point_Size_int_int_int_int_vectorLPointGR(center: *const core::Point, axes: *const core::Size, angle: i32, arc_start: i32, arc_end: i32, delta: i32, pts: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ellipse_const__InputOutputArrayR_Point_Size_double_double_double_const_ScalarR(img: *const c_void, center: *const core::Point, axes: *const core::Size, angle: f64, start_angle: f64, end_angle: f64, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_ellipse_const__InputOutputArrayR_Point_Size_double_double_double_const_ScalarR_int_int_int(img: *const c_void, center: *const core::Point, axes: *const core::Size, angle: f64, start_angle: f64, end_angle: f64, color: *const core::Scalar, thickness: i32, line_type: i32, shift: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ellipse_const__InputOutputArrayR_const_RotatedRectR_const_ScalarR(img: *const c_void, box_: *const core::RotatedRect, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_ellipse_const__InputOutputArrayR_const_RotatedRectR_const_ScalarR_int_int(img: *const c_void, box_: *const core::RotatedRect, color: *const core::Scalar, thickness: i32, line_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_equalizeHist_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_erode_const__InputArrayR_const__OutputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, kernel: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_erode_const__InputArrayR_const__OutputArrayR_const__InputArrayR_Point_int_int_const_ScalarR(src: *const c_void, dst: *const c_void, kernel: *const c_void, anchor: *const core::Point, iterations: i32, border_type: i32, border_value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_fillConvexPoly_const__InputOutputArrayR_const__InputArrayR_const_ScalarR(img: *const c_void, points: *const c_void, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_fillConvexPoly_const__InputOutputArrayR_const__InputArrayR_const_ScalarR_int_int(img: *const c_void, points: *const c_void, color: *const core::Scalar, line_type: i32, shift: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fillPoly_const__InputOutputArrayR_const__InputArrayR_const_ScalarR(img: *const c_void, pts: *const c_void, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_fillPoly_const__InputOutputArrayR_const__InputArrayR_const_ScalarR_int_int_Point(img: *const c_void, pts: *const c_void, color: *const core::Scalar, line_type: i32, shift: i32, offset: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_filter2D_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR(src: *const c_void, dst: *const c_void, ddepth: i32, kernel: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_filter2D_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR_Point_double_int(src: *const c_void, dst: *const c_void, ddepth: i32, kernel: *const c_void, anchor: *const core::Point, delta: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_findContours_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int_int(image: *const c_void, contours: *const c_void, hierarchy: *const c_void, mode: i32, method: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_findContours_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int_int_Point(image: *const c_void, contours: *const c_void, hierarchy: *const c_void, mode: i32, method: i32, offset: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_findContours_const__InputArrayR_const__OutputArrayR_int_int(image: *const c_void, contours: *const c_void, mode: i32, method: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_findContours_const__InputArrayR_const__OutputArrayR_int_int_Point(image: *const c_void, contours: *const c_void, mode: i32, method: i32, offset: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_fitEllipseAMS_const__InputArrayR(points: *const c_void, ocvrs_return: *mut Result<core::RotatedRect>);
pub fn cv_fitEllipseDirect_const__InputArrayR(points: *const c_void, ocvrs_return: *mut Result<core::RotatedRect>);
pub fn cv_fitEllipse_const__InputArrayR(points: *const c_void, ocvrs_return: *mut Result<core::RotatedRect>);
pub fn cv_fitLine_const__InputArrayR_const__OutputArrayR_int_double_double_double(points: *const c_void, line: *const c_void, dist_type: i32, param: f64, reps: f64, aeps: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_floodFill_const__InputOutputArrayR_Point_Scalar(image: *const c_void, seed_point: *const core::Point, new_val: *const core::Scalar, ocvrs_return: *mut Result<i32>);
pub fn cv_floodFill_const__InputOutputArrayR_Point_Scalar_RectX_Scalar_Scalar_int(image: *const c_void, seed_point: *const core::Point, new_val: *const core::Scalar, rect: *mut core::Rect, lo_diff: *const core::Scalar, up_diff: *const core::Scalar, flags: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_floodFill_const__InputOutputArrayR_const__InputOutputArrayR_Point_Scalar(image: *const c_void, mask: *const c_void, seed_point: *const core::Point, new_val: *const core::Scalar, ocvrs_return: *mut Result<i32>);
pub fn cv_floodFill_const__InputOutputArrayR_const__InputOutputArrayR_Point_Scalar_RectX_Scalar_Scalar_int(image: *const c_void, mask: *const c_void, seed_point: *const core::Point, new_val: *const core::Scalar, rect: *mut core::Rect, lo_diff: *const core::Scalar, up_diff: *const core::Scalar, flags: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_getAffineTransform_const_Point2fX_const_Point2fX(src: *const core::Point2f, dst: *const core::Point2f, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getAffineTransform_const__InputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getDerivKernels_const__OutputArrayR_const__OutputArrayR_int_int_int(kx: *const c_void, ky: *const c_void, dx: i32, dy: i32, ksize: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_getDerivKernels_const__OutputArrayR_const__OutputArrayR_int_int_int_bool_int(kx: *const c_void, ky: *const c_void, dx: i32, dy: i32, ksize: i32, normalize: bool, ktype: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_getFontScaleFromHeight_const_int_const_int(font_face: i32, pixel_height: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_getFontScaleFromHeight_const_int_const_int_const_int(font_face: i32, pixel_height: i32, thickness: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_getGaborKernel_Size_double_double_double_double(ksize: *const core::Size, sigma: f64, theta: f64, lambd: f64, gamma: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getGaborKernel_Size_double_double_double_double_double_int(ksize: *const core::Size, sigma: f64, theta: f64, lambd: f64, gamma: f64, psi: f64, ktype: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getGaussianKernel_int_double(ksize: i32, sigma: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getGaussianKernel_int_double_int(ksize: i32, sigma: f64, ktype: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getPerspectiveTransform_const_Point2fX_const_Point2fX(src: *const core::Point2f, dst: *const core::Point2f, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getPerspectiveTransform_const_Point2fX_const_Point2fX_int(src: *const core::Point2f, dst: *const core::Point2f, solve_method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getPerspectiveTransform_const__InputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getPerspectiveTransform_const__InputArrayR_const__InputArrayR_int(src: *const c_void, dst: *const c_void, solve_method: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getRectSubPix_const__InputArrayR_Size_Point2f_const__OutputArrayR(image: *const c_void, patch_size: *const core::Size, center: *const core::Point2f, patch: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_getRectSubPix_const__InputArrayR_Size_Point2f_const__OutputArrayR_int(image: *const c_void, patch_size: *const core::Size, center: *const core::Point2f, patch: *const c_void, patch_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_getRotationMatrix2D_Point2f_double_double(center: *const core::Point2f, angle: f64, scale: f64, ocvrs_return: *mut Result<*mut c_void>);
#[cfg(not(target_os = "windows"))]
pub fn cv_getRotationMatrix2D__Point2f_double_double(center: *const core::Point2f, angle: f64, scale: f64, ocvrs_return: *mut Result<core::Matx23d>);
pub fn cv_getStructuringElement_int_Size(shape: i32, ksize: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getStructuringElement_int_Size_Point(shape: i32, ksize: *const core::Size, anchor: *const core::Point, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getTextSize_const_StringR_int_double_int_intX(text: *const c_char, font_face: i32, font_scale: f64, thickness: i32, base_line: *mut i32, ocvrs_return: *mut Result<core::Size>);
pub fn cv_goodFeaturesToTrack_const__InputArrayR_const__OutputArrayR_int_double_double(image: *const c_void, corners: *const c_void, max_corners: i32, quality_level: f64, min_distance: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_goodFeaturesToTrack_const__InputArrayR_const__OutputArrayR_int_double_double_const__InputArrayR_const__OutputArrayR(image: *const c_void, corners: *const c_void, max_corners: i32, quality_level: f64, min_distance: f64, mask: *const c_void, corners_quality: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_goodFeaturesToTrack_const__InputArrayR_const__OutputArrayR_int_double_double_const__InputArrayR_const__OutputArrayR_int_int_bool_double(image: *const c_void, corners: *const c_void, max_corners: i32, quality_level: f64, min_distance: f64, mask: *const c_void, corners_quality: *const c_void, block_size: i32, gradient_size: i32, use_harris_detector: bool, k: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_goodFeaturesToTrack_const__InputArrayR_const__OutputArrayR_int_double_double_const__InputArrayR_int_bool_double(image: *const c_void, corners: *const c_void, max_corners: i32, quality_level: f64, min_distance: f64, mask: *const c_void, block_size: i32, use_harris_detector: bool, k: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_goodFeaturesToTrack_const__InputArrayR_const__OutputArrayR_int_double_double_const__InputArrayR_int_int(image: *const c_void, corners: *const c_void, max_corners: i32, quality_level: f64, min_distance: f64, mask: *const c_void, block_size: i32, gradient_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_goodFeaturesToTrack_const__InputArrayR_const__OutputArrayR_int_double_double_const__InputArrayR_int_int_bool_double(image: *const c_void, corners: *const c_void, max_corners: i32, quality_level: f64, min_distance: f64, mask: *const c_void, block_size: i32, gradient_size: i32, use_harris_detector: bool, k: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_grabCut_const__InputArrayR_const__InputOutputArrayR_Rect_const__InputOutputArrayR_const__InputOutputArrayR_int(img: *const c_void, mask: *const c_void, rect: *const core::Rect, bgd_model: *const c_void, fgd_model: *const c_void, iter_count: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_grabCut_const__InputArrayR_const__InputOutputArrayR_Rect_const__InputOutputArrayR_const__InputOutputArrayR_int_int(img: *const c_void, mask: *const c_void, rect: *const core::Rect, bgd_model: *const c_void, fgd_model: *const c_void, iter_count: i32, mode: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_integral_const__InputArrayR_const__OutputArrayR(src: *const c_void, sum: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_integral_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, sum: *const c_void, sqsum: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_integral_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, sum: *const c_void, sqsum: *const c_void, tilted: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_integral_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_int(src: *const c_void, sum: *const c_void, sqsum: *const c_void, tilted: *const c_void, sdepth: i32, sqdepth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_integral_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int_int(src: *const c_void, sum: *const c_void, sqsum: *const c_void, sdepth: i32, sqdepth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_integral_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, sum: *const c_void, sdepth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_intersectConvexConvex_const__InputArrayR_const__InputArrayR_const__OutputArrayR(p1: *const c_void, p2: *const c_void, p12: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_intersectConvexConvex_const__InputArrayR_const__InputArrayR_const__OutputArrayR_bool(p1: *const c_void, p2: *const c_void, p12: *const c_void, handle_nested: bool, ocvrs_return: *mut Result<f32>);
pub fn cv_invertAffineTransform_const__InputArrayR_const__OutputArrayR(m: *const c_void, i_m: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_isContourConvex_const__InputArrayR(contour: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_line_const__InputOutputArrayR_Point_Point_const_ScalarR(img: *const c_void, pt1: *const core::Point, pt2: *const core::Point, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_line_const__InputOutputArrayR_Point_Point_const_ScalarR_int_int_int(img: *const c_void, pt1: *const core::Point, pt2: *const core::Point, color: *const core::Scalar, thickness: i32, line_type: i32, shift: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_linearPolar_const__InputArrayR_const__OutputArrayR_Point2f_double_int(src: *const c_void, dst: *const c_void, center: *const core::Point2f, max_radius: f64, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_logPolar_const__InputArrayR_const__OutputArrayR_Point2f_double_int(src: *const c_void, dst: *const c_void, center: *const core::Point2f, m: f64, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_matchShapes_const__InputArrayR_const__InputArrayR_int_double(contour1: *const c_void, contour2: *const c_void, method: i32, parameter: f64, ocvrs_return: *mut Result<f64>);
pub fn cv_matchTemplate_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int(image: *const c_void, templ: *const c_void, result: *const c_void, method: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_matchTemplate_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR(image: *const c_void, templ: *const c_void, result: *const c_void, method: i32, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_medianBlur_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, ksize: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_minAreaRect_const__InputArrayR(points: *const c_void, ocvrs_return: *mut Result<core::RotatedRect>);
pub fn cv_minEnclosingCircle_const__InputArrayR_Point2fR_floatR(points: *const c_void, center: *mut core::Point2f, radius: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_minEnclosingTriangle_const__InputArrayR_const__OutputArrayR(points: *const c_void, triangle: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_moments_const__InputArrayR(array: *const c_void, ocvrs_return: *mut Result<core::Moments>);
pub fn cv_moments_const__InputArrayR_bool(array: *const c_void, binary_image: bool, ocvrs_return: *mut Result<core::Moments>);
pub fn cv_morphologyDefaultBorderValue(ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_morphologyEx_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR(src: *const c_void, dst: *const c_void, op: i32, kernel: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_morphologyEx_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR_Point_int_int_const_ScalarR(src: *const c_void, dst: *const c_void, op: i32, kernel: *const c_void, anchor: *const core::Point, iterations: i32, border_type: i32, border_value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_phaseCorrelate_const__InputArrayR_const__InputArrayR(src1: *const c_void, src2: *const c_void, ocvrs_return: *mut Result<core::Point2d>);
pub fn cv_phaseCorrelate_const__InputArrayR_const__InputArrayR_const__InputArrayR_doubleX(src1: *const c_void, src2: *const c_void, window: *const c_void, response: *mut f64, ocvrs_return: *mut Result<core::Point2d>);
pub fn cv_pointPolygonTest_const__InputArrayR_Point2f_bool(contour: *const c_void, pt: *const core::Point2f, measure_dist: bool, ocvrs_return: *mut Result<f64>);
pub fn cv_polylines_const__InputOutputArrayR_const__InputArrayR_bool_const_ScalarR(img: *const c_void, pts: *const c_void, is_closed: bool, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_polylines_const__InputOutputArrayR_const__InputArrayR_bool_const_ScalarR_int_int_int(img: *const c_void, pts: *const c_void, is_closed: bool, color: *const core::Scalar, thickness: i32, line_type: i32, shift: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_preCornerDetect_const__InputArrayR_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, ksize: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_preCornerDetect_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, ksize: i32, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_putText_const__InputOutputArrayR_const_StringR_Point_int_double_Scalar(img: *const c_void, text: *const c_char, org: *const core::Point, font_face: i32, font_scale: f64, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_putText_const__InputOutputArrayR_const_StringR_Point_int_double_Scalar_int_int_bool(img: *const c_void, text: *const c_char, org: *const core::Point, font_face: i32, font_scale: f64, color: *const core::Scalar, thickness: i32, line_type: i32, bottom_left_origin: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_pyrDown_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_pyrDown_const__InputArrayR_const__OutputArrayR_const_SizeR_int(src: *const c_void, dst: *const c_void, dstsize: *const core::Size, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_pyrMeanShiftFiltering_const__InputArrayR_const__OutputArrayR_double_double(src: *const c_void, dst: *const c_void, sp: f64, sr: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_pyrMeanShiftFiltering_const__InputArrayR_const__OutputArrayR_double_double_int_TermCriteria(src: *const c_void, dst: *const c_void, sp: f64, sr: f64, max_level: i32, termcrit: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_pyrUp_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_pyrUp_const__InputArrayR_const__OutputArrayR_const_SizeR_int(src: *const c_void, dst: *const c_void, dstsize: *const core::Size, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rectangle_const__InputOutputArrayR_Point_Point_const_ScalarR(img: *const c_void, pt1: *const core::Point, pt2: *const core::Point, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_rectangle_const__InputOutputArrayR_Point_Point_const_ScalarR_int_int_int(img: *const c_void, pt1: *const core::Point, pt2: *const core::Point, color: *const core::Scalar, thickness: i32, line_type: i32, shift: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rectangle_const__InputOutputArrayR_Rect_const_ScalarR(img: *const c_void, rec: *const core::Rect, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_rectangle_const__InputOutputArrayR_Rect_const_ScalarR_int_int_int(img: *const c_void, rec: *const core::Rect, color: *const core::Scalar, thickness: i32, line_type: i32, shift: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_remap_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_int(src: *const c_void, dst: *const c_void, map1: *const c_void, map2: *const c_void, interpolation: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_remap_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_int_int_const_ScalarR(src: *const c_void, dst: *const c_void, map1: *const c_void, map2: *const c_void, interpolation: i32, border_mode: i32, border_value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_resize_const__InputArrayR_const__OutputArrayR_Size(src: *const c_void, dst: *const c_void, dsize: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_resize_const__InputArrayR_const__OutputArrayR_Size_double_double_int(src: *const c_void, dst: *const c_void, dsize: *const core::Size, fx: f64, fy: f64, interpolation: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_rotatedRectangleIntersection_const_RotatedRectR_const_RotatedRectR_const__OutputArrayR(rect1: *const core::RotatedRect, rect2: *const core::RotatedRect, intersecting_region: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_sepFilter2D_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, ddepth: i32, kernel_x: *const c_void, kernel_y: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_sepFilter2D_const__InputArrayR_const__OutputArrayR_int_const__InputArrayR_const__InputArrayR_Point_double_int(src: *const c_void, dst: *const c_void, ddepth: i32, kernel_x: *const c_void, kernel_y: *const c_void, anchor: *const core::Point, delta: f64, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_spatialGradient_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, dx: *const c_void, dy: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_spatialGradient_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int_int(src: *const c_void, dx: *const c_void, dy: *const c_void, ksize: i32, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_sqrBoxFilter_const__InputArrayR_const__OutputArrayR_int_Size(src: *const c_void, dst: *const c_void, ddepth: i32, ksize: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_sqrBoxFilter_const__InputArrayR_const__OutputArrayR_int_Size_Point_bool_int(src: *const c_void, dst: *const c_void, ddepth: i32, ksize: *const core::Size, anchor: *const core::Point, normalize: bool, border_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_threshold_const__InputArrayR_const__OutputArrayR_double_double_int(src: *const c_void, dst: *const c_void, thresh: f64, maxval: f64, typ: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_warpAffine_const__InputArrayR_const__OutputArrayR_const__InputArrayR_Size(src: *const c_void, dst: *const c_void, m: *const c_void, dsize: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_warpAffine_const__InputArrayR_const__OutputArrayR_const__InputArrayR_Size_int_int_const_ScalarR(src: *const c_void, dst: *const c_void, m: *const c_void, dsize: *const core::Size, flags: i32, border_mode: i32, border_value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_warpPerspective_const__InputArrayR_const__OutputArrayR_const__InputArrayR_Size(src: *const c_void, dst: *const c_void, m: *const c_void, dsize: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_warpPerspective_const__InputArrayR_const__OutputArrayR_const__InputArrayR_Size_int_int_const_ScalarR(src: *const c_void, dst: *const c_void, m: *const c_void, dsize: *const core::Size, flags: i32, border_mode: i32, border_value: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_warpPolar_const__InputArrayR_const__OutputArrayR_Size_Point2f_double_int(src: *const c_void, dst: *const c_void, dsize: *const core::Size, center: *const core::Point2f, max_radius: f64, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_watershed_const__InputArrayR_const__InputOutputArrayR(image: *const c_void, markers: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_wrapperEMD_const__InputArrayR_const__InputArrayR_int(signature1: *const c_void, signature2: *const c_void, dist_type: i32, ocvrs_return: *mut Result<f32>);
pub fn cv_wrapperEMD_const__InputArrayR_const__InputArrayR_int_const__InputArrayR_PtrLfloatG_const__OutputArrayR(signature1: *const c_void, signature2: *const c_void, dist_type: i32, cost: *const c_void, lower_bound: *mut c_void, flow: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_CLAHE_apply_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CLAHE_setClipLimit_double(instance: *mut c_void, clip_limit: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_CLAHE_getClipLimit_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_CLAHE_setTilesGridSize_Size(instance: *mut c_void, tile_grid_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_CLAHE_getTilesGridSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_CLAHE_collectGarbage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CLAHE_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_CLAHE_delete(instance: *mut c_void);
pub fn cv_GeneralizedHough_setTemplate_const__InputArrayR_Point(instance: *mut c_void, templ: *const c_void, templ_center: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_setTemplate_const__InputArrayR(instance: *mut c_void, templ: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_setTemplate_const__InputArrayR_const__InputArrayR_const__InputArrayR_Point(instance: *mut c_void, edges: *const c_void, dx: *const c_void, dy: *const c_void, templ_center: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_setTemplate_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, edges: *const c_void, dx: *const c_void, dy: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_detect_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, positions: *const c_void, votes: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_detect_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, positions: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_detect_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, edges: *const c_void, dx: *const c_void, dy: *const c_void, positions: *const c_void, votes: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_detect_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, edges: *const c_void, dx: *const c_void, dy: *const c_void, positions: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_setCannyLowThresh_int(instance: *mut c_void, canny_low_thresh: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_getCannyLowThresh_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHough_setCannyHighThresh_int(instance: *mut c_void, canny_high_thresh: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_getCannyHighThresh_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHough_setMinDist_double(instance: *mut c_void, min_dist: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_getMinDist_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHough_setDp_double(instance: *mut c_void, dp: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_getDp_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHough_setMaxBufferSize_int(instance: *mut c_void, max_buffer_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHough_getMaxBufferSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHough_to_GeneralizedHoughBallard(instance: *mut c_void) -> *mut c_void;
pub fn cv_GeneralizedHough_to_GeneralizedHoughGuil(instance: *mut c_void) -> *mut c_void;
pub fn cv_GeneralizedHough_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_GeneralizedHough_delete(instance: *mut c_void);
pub fn cv_GeneralizedHoughBallard_setLevels_int(instance: *mut c_void, levels: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughBallard_getLevels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHoughBallard_setVotesThreshold_int(instance: *mut c_void, votes_threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughBallard_getVotesThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHoughBallard_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_GeneralizedHoughBallard_to_GeneralizedHough(instance: *mut c_void) -> *mut c_void;
pub fn cv_GeneralizedHoughBallard_delete(instance: *mut c_void);
pub fn cv_GeneralizedHoughGuil_setXi_double(instance: *mut c_void, xi: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getXi_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHoughGuil_setLevels_int(instance: *mut c_void, levels: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getLevels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHoughGuil_setAngleEpsilon_double(instance: *mut c_void, angle_epsilon: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getAngleEpsilon_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHoughGuil_setMinAngle_double(instance: *mut c_void, min_angle: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getMinAngle_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHoughGuil_setMaxAngle_double(instance: *mut c_void, max_angle: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getMaxAngle_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHoughGuil_setAngleStep_double(instance: *mut c_void, angle_step: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getAngleStep_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHoughGuil_setAngleThresh_int(instance: *mut c_void, angle_thresh: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getAngleThresh_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHoughGuil_setMinScale_double(instance: *mut c_void, min_scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getMinScale_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHoughGuil_setMaxScale_double(instance: *mut c_void, max_scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getMaxScale_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHoughGuil_setScaleStep_double(instance: *mut c_void, scale_step: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getScaleStep_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_GeneralizedHoughGuil_setScaleThresh_int(instance: *mut c_void, scale_thresh: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getScaleThresh_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHoughGuil_setPosThresh_int(instance: *mut c_void, pos_thresh: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_GeneralizedHoughGuil_getPosThresh_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_GeneralizedHoughGuil_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_GeneralizedHoughGuil_to_GeneralizedHough(instance: *mut c_void) -> *mut c_void;
pub fn cv_GeneralizedHoughGuil_delete(instance: *mut c_void);
pub fn cv_LineIterator_LineIterator_const_MatR_Point_Point_int_bool(img: *const c_void, pt1: *const core::Point, pt2: *const core::Point, connectivity: i32, left_to_right: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_LineIterator_const_MatR_Point_Point(img: *const c_void, pt1: *const core::Point, pt2: *const core::Point, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_LineIterator_Point_Point_int_bool(pt1: *const core::Point, pt2: *const core::Point, connectivity: i32, left_to_right: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_LineIterator_Point_Point(pt1: *const core::Point, pt2: *const core::Point, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_LineIterator_Size_Point_Point_int_bool(bounding_area_size: *const core::Size, pt1: *const core::Point, pt2: *const core::Point, connectivity: i32, left_to_right: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_LineIterator_Size_Point_Point(bounding_area_size: *const core::Size, pt1: *const core::Point, pt2: *const core::Point, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_LineIterator_Rect_Point_Point_int_bool(bounding_area_rect: *const core::Rect, pt1: *const core::Point, pt2: *const core::Point, connectivity: i32, left_to_right: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_LineIterator_Rect_Point_Point(bounding_area_rect: *const core::Rect, pt1: *const core::Point, pt2: *const core::Point, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_init_const_MatX_Rect_Point_Point_int_bool(instance: *mut c_void, img: *const c_void, bounding_area_rect: *const core::Rect, pt1: *const core::Point, pt2: *const core::Point, connectivity: i32, left_to_right: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_LineIterator_operatorX(instance: *mut c_void, ocvrs_return: *mut Result<*mut u8>);
pub fn cv_LineIterator_operatorAA(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LineIterator_pos_const(instance: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_LineIterator_propPtr_const(instance: *const c_void) -> *const u8;
pub fn cv_LineIterator_propPtr(instance: *mut c_void) -> *mut u8;
pub fn cv_LineIterator_propPtr_unsigned_charX(instance: *mut c_void, val: *const u8);
pub fn cv_LineIterator_propPtr0_const(instance: *const c_void) -> *const u8;
pub fn cv_LineIterator_propStep_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propStep_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propElemSize_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propElemSize_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propErr_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propErr_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propCount_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propCount_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propMinusDelta_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propMinusDelta_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propPlusDelta_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propPlusDelta_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propMinusStep_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propMinusStep_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propPlusStep_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propPlusStep_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propMinusShift_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propMinusShift_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propPlusShift_const(instance: *const c_void) -> i32;
pub fn cv_LineIterator_propPlusShift_const_int(instance: *mut c_void, val: i32);
pub fn cv_LineIterator_propP_const(instance: *const c_void, ocvrs_return: *mut core::Point);
pub fn cv_LineIterator_propP_const_Point(instance: *mut c_void, val: *const core::Point);
pub fn cv_LineIterator_propPtmode_const(instance: *const c_void) -> bool;
pub fn cv_LineIterator_propPtmode_const_bool(instance: *mut c_void, val: bool);
pub fn cv_LineIterator_delete(instance: *mut c_void);
pub fn cv_LineSegmentDetector_detect_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, lines: *const c_void, width: *const c_void, prec: *const c_void, nfa: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_LineSegmentDetector_detect_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, lines: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_LineSegmentDetector_drawSegments_const__InputOutputArrayR_const__InputArrayR(instance: *mut c_void, image: *const c_void, lines: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_LineSegmentDetector_compareSegments_const_SizeR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR(instance: *mut c_void, size: *const core::Size, lines1: *const c_void, lines2: *const c_void, image: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_LineSegmentDetector_compareSegments_const_SizeR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, size: *const core::Size, lines1: *const c_void, lines2: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_LineSegmentDetector_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_LineSegmentDetector_delete(instance: *mut c_void);
pub fn cv_Subdiv2D_Subdiv2D(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Subdiv2D_Subdiv2D_Rect(rect: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Subdiv2D_initDelaunay_Rect(instance: *mut c_void, rect: *const core::Rect, ocvrs_return: *mut ResultVoid);
pub fn cv_Subdiv2D_insert_Point2f(instance: *mut c_void, pt: *const core::Point2f, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_insert_const_vectorLPoint2fGR(instance: *mut c_void, ptvec: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Subdiv2D_locate_Point2f_intR_intR(instance: *mut c_void, pt: *const core::Point2f, edge: *mut i32, vertex: *mut i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_findNearest_Point2f_Point2fX(instance: *mut c_void, pt: *const core::Point2f, nearest_pt: *mut core::Point2f, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_findNearest_Point2f(instance: *mut c_void, pt: *const core::Point2f, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_getEdgeList_const_vectorLVec4fGR(instance: *const c_void, edge_list: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Subdiv2D_getLeadingEdgeList_const_vectorLintGR(instance: *const c_void, leading_edge_list: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Subdiv2D_getTriangleList_const_vectorLVec6fGR(instance: *const c_void, triangle_list: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Subdiv2D_getVoronoiFacetList_const_vectorLintGR_vectorLvectorLPoint2fGGR_vectorLPoint2fGR(instance: *mut c_void, idx: *const c_void, facet_list: *mut c_void, facet_centers: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Subdiv2D_getVertex_const_int_intX(instance: *const c_void, vertex: i32, first_edge: *mut i32, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_Subdiv2D_getVertex_const_int(instance: *const c_void, vertex: i32, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_Subdiv2D_getEdge_const_int_int(instance: *const c_void, edge: i32, next_edge_type: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_nextEdge_const_int(instance: *const c_void, edge: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_rotateEdge_const_int_int(instance: *const c_void, edge: i32, rotate: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_symEdge_const_int(instance: *const c_void, edge: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_edgeOrg_const_int_Point2fX(instance: *const c_void, edge: i32, orgpt: *mut core::Point2f, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_edgeOrg_const_int(instance: *const c_void, edge: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_edgeDst_const_int_Point2fX(instance: *const c_void, edge: i32, dstpt: *mut core::Point2f, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_edgeDst_const_int(instance: *const c_void, edge: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_Subdiv2D_delete(instance: *mut c_void);
pub fn cv_segmentation_IntelligentScissorsMB_IntelligentScissorsMB(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_setWeights_float_float_float(instance: *mut c_void, weight_non_edge: f32, weight_gradient_direction: f32, weight_gradient_magnitude: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_setGradientMagnitudeMaxLimit_float(instance: *mut c_void, gradient_magnitude_threshold_max: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_setGradientMagnitudeMaxLimit(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_setEdgeFeatureZeroCrossingParameters_float(instance: *mut c_void, gradient_magnitude_min_value: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_setEdgeFeatureZeroCrossingParameters(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_setEdgeFeatureCannyParameters_double_double_int_bool(instance: *mut c_void, threshold1: f64, threshold2: f64, aperture_size: i32, l2gradient: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_setEdgeFeatureCannyParameters_double_double(instance: *mut c_void, threshold1: f64, threshold2: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_applyImage_const__InputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_applyImageFeatures_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, non_edge: *const c_void, gradient_direction: *const c_void, gradient_magnitude: *const c_void, image: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_applyImageFeatures_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, non_edge: *const c_void, gradient_direction: *const c_void, gradient_magnitude: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_segmentation_IntelligentScissorsMB_buildMap_const_PointR(instance: *mut c_void, source_pt: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_segmentation_IntelligentScissorsMB_getContour_const_const_PointR_const__OutputArrayR_bool(instance: *const c_void, target_pt: *const core::Point, contour: *const c_void, backward: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_segmentation_IntelligentScissorsMB_getContour_const_const_PointR_const__OutputArrayR(instance: *const c_void, target_pt: *const core::Point, contour: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_segmentation_IntelligentScissorsMB_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_segmentation_IntelligentScissorsMB_delete(instance: *mut c_void);
