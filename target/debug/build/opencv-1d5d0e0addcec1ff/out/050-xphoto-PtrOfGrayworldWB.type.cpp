extern "C" {
	const cv::xphoto::GrayworldWB* cv_PtrLcv_xphoto_GrayworldWBG_getInnerPtr_const(const cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			return instance->get();
	}
	
	cv::xphoto::GrayworldWB* cv_PtrLcv_xphoto_GrayworldWBG_getInnerPtrMut(cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_GrayworldWBG_delete(cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_GrayworldWBG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::xphoto::WhiteBalancer>* cv_PtrLcv_xphoto_GrayworldWBG_to_PtrOfWhiteBalancer(cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			return new cv::Ptr<cv::xphoto::WhiteBalancer>(instance->dynamicCast<cv::xphoto::WhiteBalancer>());
	}
	
}

