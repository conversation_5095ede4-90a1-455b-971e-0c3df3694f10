pub fn cv_PtrLcv_rgbd_RgbdNormalsG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_RgbdNormalsG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_RgbdNormalsG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_rgbd_RgbdNormalsG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_rgbd_RgbdNormalsG_new_const_RgbdNormals(val: *mut c_void) -> *mut c_void;
