pub fn std_vectorLcv_ScalarG_new_const() -> *mut c_void;
pub fn std_vectorLcv_ScalarG_delete(instance: *mut c_void);
pub fn std_vectorLcv_ScalarG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_ScalarG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_ScalarG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_ScalarG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_ScalarG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_ScalarG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_ScalarG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_ScalarG_clear(instance: *mut c_void);
pub fn std_vectorLcv_ScalarG_push_const_Scalar(instance: *mut c_void, val: *const core::Scalar);
pub fn std_vectorLcv_ScalarG_insert_size_t_const_Scalar(instance: *mut c_void, index: size_t, val: *const core::Scalar);
pub fn std_vectorLcv_ScalarG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Scalar);
pub fn std_vectorLcv_ScalarG_set_size_t_const_Scalar(instance: *mut c_void, index: size_t, val: *const core::Scalar);
pub fn std_vectorLcv_ScalarG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_ScalarG_data_const(instance: *const c_void) -> *const core::Scalar;
pub fn std_vectorLcv_ScalarG_dataMut(instance: *mut c_void) -> *mut core::Scalar;
pub fn cv_fromSlice_const_const_ScalarX_size_t(data: *const core::Scalar, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_ScalarG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_ScalarG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_ScalarG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
