pub fn std_vectorLcv_Vec3dG_new_const() -> *mut c_void;
pub fn std_vectorLcv_Vec3dG_delete(instance: *mut c_void);
pub fn std_vectorLcv_Vec3dG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec3dG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_Vec3dG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec3dG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_Vec3dG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_Vec3dG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_Vec3dG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_Vec3dG_clear(instance: *mut c_void);
pub fn std_vectorLcv_Vec3dG_push_const_Vec3d(instance: *mut c_void, val: *const core::Vec3d);
pub fn std_vectorLcv_Vec3dG_insert_size_t_const_Vec3d(instance: *mut c_void, index: size_t, val: *const core::Vec3d);
pub fn std_vectorLcv_Vec3dG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Vec3d);
pub fn std_vectorLcv_Vec3dG_set_size_t_const_Vec3d(instance: *mut c_void, index: size_t, val: *const core::Vec3d);
pub fn std_vectorLcv_Vec3dG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_Vec3dG_data_const(instance: *const c_void) -> *const core::Vec3d;
pub fn std_vectorLcv_Vec3dG_dataMut(instance: *mut c_void) -> *mut core::Vec3d;
pub fn cv_fromSlice_const_const_Vec3dX_size_t(data: *const core::Vec3d, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_Vec3dG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec3dG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec3dG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
