pub fn cv_PtrLcv_videostab_ColorInpainterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ColorInpainterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ColorInpainterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_ColorInpainterG_to_PtrOfInpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ColorInpainterG_new_const_ColorInpainter(val: *mut c_void) -> *mut c_void;
