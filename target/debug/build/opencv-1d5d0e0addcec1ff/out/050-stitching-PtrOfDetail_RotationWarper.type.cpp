extern "C" {
	const cv::detail::RotationWarper* cv_PtrLcv_detail_RotationWarperG_getInnerPtr_const(const cv::Ptr<cv::detail::RotationWarper>* instance) {
			return instance->get();
	}
	
	cv::detail::RotationWarper* cv_PtrLcv_detail_RotationWarperG_getInnerPtrMut(cv::Ptr<cv::detail::RotationWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_RotationWarperG_delete(cv::Ptr<cv::detail::RotationWarper>* instance) {
			delete instance;
	}
	
}

