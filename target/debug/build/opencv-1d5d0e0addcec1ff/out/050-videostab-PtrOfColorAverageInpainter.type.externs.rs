pub fn cv_PtrLcv_videostab_ColorAverageInpainterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ColorAverageInpainterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ColorAverageInpainterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_ColorAverageInpainterG_to_PtrOfInpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_ColorAverageInpainterG_new_const_ColorAverageInpainter(val: *mut c_void) -> *mut c_void;
