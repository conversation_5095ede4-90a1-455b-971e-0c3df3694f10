pub fn cv_PtrLcv_TransverseMercatorWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_TransverseMercatorWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_TransverseMercatorWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_TransverseMercatorWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_TransverseMercatorWarperG_new_const_TransverseMercatorWarper(val: *mut c_void) -> *mut c_void;
