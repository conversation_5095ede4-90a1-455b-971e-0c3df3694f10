pub fn cv_rapid_convertCorrespondencies_const__InputArrayR_const__InputArrayR_const__OutputArrayR(cols: *const c_void, src_locations: *const c_void, pts2d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_convertCorrespondencies_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputOutputArrayR_const__InputArrayR(cols: *const c_void, src_locations: *const c_void, pts2d: *const c_void, pts3d: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_drawCorrespondencies_const__InputOutputArrayR_const__InputArrayR(bundle: *const c_void, cols: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_drawCorrespondencies_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR(bundle: *const c_void, cols: *const c_void, colors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_drawSearchLines_const__InputOutputArrayR_const__InputArrayR_const_ScalarR(img: *const c_void, locations: *const c_void, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_drawWireframe_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_const_ScalarR(img: *const c_void, pts2d: *const c_void, tris: *const c_void, color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_drawWireframe_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_const_ScalarR_int_bool(img: *const c_void, pts2d: *const c_void, tris: *const c_void, color: *const core::Scalar, typ: i32, cull_backface: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_extractControlPoints_int_int_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(num: i32, len: i32, pts3d: *const c_void, rvec: *const c_void, tvec: *const c_void, k: *const c_void, imsize: *const core::Size, tris: *const c_void, ctl2d: *const c_void, ctl3d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_extractLineBundle_int_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(len: i32, ctl2d: *const c_void, img: *const c_void, bundle: *const c_void, src_locations: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_findCorrespondencies_const__InputArrayR_const__OutputArrayR(bundle: *const c_void, cols: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_findCorrespondencies_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(bundle: *const c_void, cols: *const c_void, response: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_rapid_const__InputArrayR_int_int_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(img: *const c_void, num: i32, len: i32, pts3d: *const c_void, tris: *const c_void, k: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_rapid_rapid_const__InputArrayR_int_int_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_doubleX(img: *const c_void, num: i32, len: i32, pts3d: *const c_void, tris: *const c_void, k: *const c_void, rvec: *const c_void, tvec: *const c_void, rmsd: *mut f64, ocvrs_return: *mut Result<f32>);
pub fn cv_rapid_GOSTracker_create_const__InputArrayR_const__InputArrayR_int_unsigned_char(pts3d: *const c_void, tris: *const c_void, hist_bins: i32, sobel_thesh: u8, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rapid_GOSTracker_create_const__InputArrayR_const__InputArrayR(pts3d: *const c_void, tris: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rapid_GOSTracker_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_GOSTracker_to_Rapid_Tracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_GOSTracker_delete(instance: *mut c_void);
pub fn cv_rapid_OLSTracker_create_const__InputArrayR_const__InputArrayR_int_unsigned_char(pts3d: *const c_void, tris: *const c_void, hist_bins: i32, sobel_thesh: u8, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rapid_OLSTracker_create_const__InputArrayR_const__InputArrayR(pts3d: *const c_void, tris: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rapid_OLSTracker_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_OLSTracker_to_Rapid_Tracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_OLSTracker_delete(instance: *mut c_void);
pub fn cv_rapid_Rapid_create_const__InputArrayR_const__InputArrayR(pts3d: *const c_void, tris: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_rapid_Rapid_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_Rapid_to_Rapid_Tracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_Rapid_delete(instance: *mut c_void);
pub fn cv_rapid_Tracker_compute_const__InputArrayR_int_int_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const_TermCriteriaR(instance: *mut c_void, img: *const c_void, num: i32, len: i32, k: *const c_void, rvec: *const c_void, tvec: *const c_void, termcrit: *const core::TermCriteria, ocvrs_return: *mut Result<f32>);
pub fn cv_rapid_Tracker_compute_const__InputArrayR_int_int_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, img: *const c_void, num: i32, len: i32, k: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_rapid_Tracker_clearState(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_rapid_Tracker_to_Rapid_GOSTracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_Tracker_to_Rapid_OLSTracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_Tracker_to_Rapid_Rapid(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_Tracker_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_rapid_Tracker_delete(instance: *mut c_void);
