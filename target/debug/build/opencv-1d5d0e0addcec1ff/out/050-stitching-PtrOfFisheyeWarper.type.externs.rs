pub fn cv_PtrLcv_FisheyeWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_FisheyeWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_FisheyeWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_FisheyeWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_FisheyeWarperG_new_const_FisheyeWarper(val: *mut c_void) -> *mut c_void;
