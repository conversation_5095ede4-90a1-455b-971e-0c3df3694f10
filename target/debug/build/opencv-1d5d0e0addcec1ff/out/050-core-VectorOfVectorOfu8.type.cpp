extern "C" {
	std::vector<std::vector<unsigned char>>* std_vectorLstd_vectorLunsigned_charGG_new_const() {
			std::vector<std::vector<unsigned char>>* ret = new std::vector<std::vector<unsigned char>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_delete(std::vector<std::vector<unsigned char>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLunsigned_charGG_len_const(const std::vector<std::vector<unsigned char>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLunsigned_charGG_isEmpty_const(const std::vector<std::vector<unsigned char>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLunsigned_charGG_capacity_const(const std::vector<std::vector<unsigned char>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_shrinkToFit(std::vector<std::vector<unsigned char>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_reserve_size_t(std::vector<std::vector<unsigned char>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_remove_size_t(std::vector<std::vector<unsigned char>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_swap_size_t_size_t(std::vector<std::vector<unsigned char>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_clear(std::vector<std::vector<unsigned char>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_push_const_vectorLunsigned_charG(std::vector<std::vector<unsigned char>>* instance, const std::vector<unsigned char>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_insert_size_t_const_vectorLunsigned_charG(std::vector<std::vector<unsigned char>>* instance, size_t index, const std::vector<unsigned char>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_get_const_size_t(const std::vector<std::vector<unsigned char>>* instance, size_t index, std::vector<unsigned char>** ocvrs_return) {
			std::vector<unsigned char> ret = (*instance)[index];
			*ocvrs_return = new std::vector<unsigned char>(ret);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_set_size_t_const_vectorLunsigned_charG(std::vector<std::vector<unsigned char>>* instance, size_t index, const std::vector<unsigned char>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_inputArray_const(const std::vector<std::vector<unsigned char>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_outputArray(std::vector<std::vector<unsigned char>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_inputOutputArray(std::vector<std::vector<unsigned char>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


