extern "C" {
	std::vector<std::vector<float>>* std_vectorLstd_vectorLfloatGG_new_const() {
			std::vector<std::vector<float>>* ret = new std::vector<std::vector<float>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLfloatGG_delete(std::vector<std::vector<float>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLfloatGG_len_const(const std::vector<std::vector<float>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLfloatGG_isEmpty_const(const std::vector<std::vector<float>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLfloatGG_capacity_const(const std::vector<std::vector<float>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLfloatGG_shrinkToFit(std::vector<std::vector<float>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLfloatGG_reserve_size_t(std::vector<std::vector<float>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLfloatGG_remove_size_t(std::vector<std::vector<float>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLfloatGG_swap_size_t_size_t(std::vector<std::vector<float>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLfloatGG_clear(std::vector<std::vector<float>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLfloatGG_push_const_vectorLfloatG(std::vector<std::vector<float>>* instance, const std::vector<float>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLfloatGG_insert_size_t_const_vectorLfloatG(std::vector<std::vector<float>>* instance, size_t index, const std::vector<float>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLfloatGG_get_const_size_t(const std::vector<std::vector<float>>* instance, size_t index, std::vector<float>** ocvrs_return) {
			std::vector<float> ret = (*instance)[index];
			*ocvrs_return = new std::vector<float>(ret);
	}
	
	void std_vectorLstd_vectorLfloatGG_set_size_t_const_vectorLfloatG(std::vector<std::vector<float>>* instance, size_t index, const std::vector<float>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLfloatGG_inputArray_const(const std::vector<std::vector<float>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLfloatGG_outputArray(std::vector<std::vector<float>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLfloatGG_inputOutputArray(std::vector<std::vector<float>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


