pub fn cv_line_descriptor_drawKeylines_const_MatR_const_vectorLKeyLineGR_MatR(image: *const c_void, keylines: *const c_void, out_image: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_drawKeylines_const_MatR_const_vectorLKeyLineGR_MatR_const_ScalarR_int(image: *const c_void, keylines: *const c_void, out_image: *mut c_void, color: *const core::Scalar, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_drawLineMatches_const_MatR_const_vectorLKeyLineGR_const_MatR_const_vectorLKeyLineGR_const_vectorLDMatchGR_MatR(img1: *const c_void, keylines1: *const c_void, img2: *const c_void, keylines2: *const c_void, matches1to2: *const c_void, out_img: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_drawLineMatches_const_MatR_const_vectorLKeyLineGR_const_MatR_const_vectorLKeyLineGR_const_vectorLDMatchGR_MatR_const_ScalarR_const_ScalarR_const_vectorLcharGR_int(img1: *const c_void, keylines1: *const c_void, img2: *const c_void, keylines2: *const c_void, matches1to2: *const c_void, out_img: *mut c_void, match_color: *const core::Scalar, single_line_color: *const core::Scalar, matches_mask: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_BinaryDescriptor_const_ParamsR(parameters: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_BinaryDescriptor_BinaryDescriptor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_BinaryDescriptor_createBinaryDescriptor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_BinaryDescriptor_createBinaryDescriptor_Params(parameters: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_BinaryDescriptor_getNumOfOctaves(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_line_descriptor_BinaryDescriptor_setNumOfOctaves_int(instance: *mut c_void, octaves: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_getWidthOfBand(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_line_descriptor_BinaryDescriptor_setWidthOfBand_int(instance: *mut c_void, width: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_getReductionRatio(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_line_descriptor_BinaryDescriptor_setReductionRatio_int(instance: *mut c_void, r_ratio: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_detect_const_MatR_vectorLKeyLineGR_const_MatR(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_detect_const_MatR_vectorLKeyLineGR(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_detect_const_const_vectorLMatGR_vectorLvectorLKeyLineGGR_const_vectorLMatGR(instance: *const c_void, images: *const c_void, keylines: *mut c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_detect_const_const_vectorLMatGR_vectorLvectorLKeyLineGGR(instance: *const c_void, images: *const c_void, keylines: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_compute_const_const_MatR_vectorLKeyLineGR_MatR_bool(instance: *const c_void, image: *const c_void, keylines: *mut c_void, descriptors: *mut c_void, return_float_descr: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_compute_const_const_MatR_vectorLKeyLineGR_MatR(instance: *const c_void, image: *const c_void, keylines: *mut c_void, descriptors: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_compute_const_const_vectorLMatGR_vectorLvectorLKeyLineGGR_vectorLMatGR_bool(instance: *const c_void, images: *const c_void, keylines: *mut c_void, descriptors: *mut c_void, return_float_descr: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_compute_const_const_vectorLMatGR_vectorLvectorLKeyLineGGR_vectorLMatGR(instance: *const c_void, images: *const c_void, keylines: *mut c_void, descriptors: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_descriptorSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_line_descriptor_BinaryDescriptor_descriptorType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_line_descriptor_BinaryDescriptor_defaultNorm_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_line_descriptor_BinaryDescriptor_operator___const_const__InputArrayR_const__InputArrayR_vectorLKeyLineGR_const__OutputArrayR_bool_bool(instance: *const c_void, image: *const c_void, mask: *const c_void, keylines: *mut c_void, descriptors: *const c_void, use_provided_key_lines: bool, return_float_descr: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_operator___const_const__InputArrayR_const__InputArrayR_vectorLKeyLineGR_const__OutputArrayR(instance: *const c_void, image: *const c_void, mask: *const c_void, keylines: *mut c_void, descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_line_descriptor_BinaryDescriptor_delete(instance: *mut c_void);
pub fn cv_line_descriptor_BinaryDescriptor_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_BinaryDescriptor_Params_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_Params_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptor_Params_propNumOfOctave__const(instance: *const c_void) -> i32;
pub fn cv_line_descriptor_BinaryDescriptor_Params_propNumOfOctave__const_int(instance: *mut c_void, val: i32);
pub fn cv_line_descriptor_BinaryDescriptor_Params_propWidthOfBand__const(instance: *const c_void) -> i32;
pub fn cv_line_descriptor_BinaryDescriptor_Params_propWidthOfBand__const_int(instance: *mut c_void, val: i32);
pub fn cv_line_descriptor_BinaryDescriptor_Params_propReductionRatio_const(instance: *const c_void) -> i32;
pub fn cv_line_descriptor_BinaryDescriptor_Params_propReductionRatio_const_int(instance: *mut c_void, val: i32);
pub fn cv_line_descriptor_BinaryDescriptor_Params_propKsize__const(instance: *const c_void) -> i32;
pub fn cv_line_descriptor_BinaryDescriptor_Params_propKsize__const_int(instance: *mut c_void, val: i32);
pub fn cv_line_descriptor_BinaryDescriptor_Params_delete(instance: *mut c_void);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_match_const_const_MatR_const_MatR_vectorLDMatchGR_const_MatR(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_match_const_const_MatR_const_MatR_vectorLDMatchGR(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_match_const_MatR_vectorLDMatchGR_const_vectorLMatGR(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_match_const_MatR_vectorLDMatchGR(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_knnMatch_const_const_MatR_const_MatR_vectorLvectorLDMatchGGR_int_const_MatR_bool(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, k: i32, mask: *const c_void, compact_result: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_knnMatch_const_const_MatR_const_MatR_vectorLvectorLDMatchGGR_int(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, k: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_knnMatch_const_MatR_vectorLvectorLDMatchGGR_int_const_vectorLMatGR_bool(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, k: i32, masks: *const c_void, compact_result: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_knnMatch_const_MatR_vectorLvectorLDMatchGGR_int(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, k: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_radiusMatch_const_const_MatR_const_MatR_vectorLvectorLDMatchGGR_float_const_MatR_bool(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, max_distance: f32, mask: *const c_void, compact_result: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_radiusMatch_const_const_MatR_const_MatR_vectorLvectorLDMatchGGR_float(instance: *const c_void, query_descriptors: *const c_void, train_descriptors: *const c_void, matches: *mut c_void, max_distance: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_radiusMatch_const_MatR_vectorLvectorLDMatchGGR_float_const_vectorLMatGR_bool(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, max_distance: f32, masks: *const c_void, compact_result: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_radiusMatch_const_MatR_vectorLvectorLDMatchGGR_float(instance: *mut c_void, query_descriptors: *const c_void, matches: *mut c_void, max_distance: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_add_const_vectorLMatGR(instance: *mut c_void, descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_train(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_createBinaryDescriptorMatcher(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_BinaryDescriptorMatcher(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_BinaryDescriptorMatcher_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_line_descriptor_BinaryDescriptorMatcher_delete(instance: *mut c_void);
pub fn cv_line_descriptor_KeyLine_getStartPoint_const(instance: *const crate::line_descriptor::KeyLine, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_line_descriptor_KeyLine_getEndPoint_const(instance: *const crate::line_descriptor::KeyLine, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_line_descriptor_KeyLine_getStartPointInOctave_const(instance: *const crate::line_descriptor::KeyLine, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_line_descriptor_KeyLine_getEndPointInOctave_const(instance: *const crate::line_descriptor::KeyLine, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_line_descriptor_KeyLine_KeyLine(ocvrs_return: *mut Result<crate::line_descriptor::KeyLine>);
pub fn cv_line_descriptor_LSDDetector_LSDDetector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_LSDDetector_LSDDetector_LSDParam(_params: *const crate::line_descriptor::LSDParam, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_LSDDetector_createLSDDetector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_LSDDetector_createLSDDetector_LSDParam(params: *const crate::line_descriptor::LSDParam, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_line_descriptor_LSDDetector_detect_const_MatR_vectorLKeyLineGR_int_int_const_MatR(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, scale: i32, num_octaves: i32, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_LSDDetector_detect_const_MatR_vectorLKeyLineGR_int_int(instance: *mut c_void, image: *const c_void, keypoints: *mut c_void, scale: i32, num_octaves: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_LSDDetector_detect_const_const_vectorLMatGR_vectorLvectorLKeyLineGGR_int_int_const_vectorLMatGR(instance: *const c_void, images: *const c_void, keylines: *mut c_void, scale: i32, num_octaves: i32, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_LSDDetector_detect_const_const_vectorLMatGR_vectorLvectorLKeyLineGGR_int_int(instance: *const c_void, images: *const c_void, keylines: *mut c_void, scale: i32, num_octaves: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_line_descriptor_LSDDetector_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_line_descriptor_LSDDetector_delete(instance: *mut c_void);
pub fn cv_line_descriptor_LSDParam_LSDParam(ocvrs_return: *mut Result<crate::line_descriptor::LSDParam>);
