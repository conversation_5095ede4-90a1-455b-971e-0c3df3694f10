pub fn cv_PtrLcv_face_FacemarkLBFG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_face_FacemarkLBFG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_face_FacemarkLBFG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_face_FacemarkLBFG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_face_FacemarkLBFG_to_PtrOfFacemark(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_face_FacemarkLBFG_to_PtrOfFacemarkTrain(instance: *mut c_void) -> *mut c_void;
