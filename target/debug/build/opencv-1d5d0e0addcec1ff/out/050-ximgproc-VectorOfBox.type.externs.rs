pub fn std_vectorLcv_ximgproc_BoxG_new_const() -> *mut c_void;
pub fn std_vectorLcv_ximgproc_BoxG_delete(instance: *mut c_void);
pub fn std_vectorLcv_ximgproc_BoxG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_ximgproc_BoxG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_ximgproc_BoxG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_ximgproc_BoxG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_ximgproc_BoxG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_ximgproc_BoxG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_ximgproc_BoxG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_ximgproc_BoxG_clear(instance: *mut c_void);
pub fn std_vectorLcv_ximgproc_BoxG_push_const_Box(instance: *mut c_void, val: *const crate::ximgproc::Box);
pub fn std_vectorLcv_ximgproc_BoxG_insert_size_t_const_Box(instance: *mut c_void, index: size_t, val: *const crate::ximgproc::Box);
pub fn std_vectorLcv_ximgproc_BoxG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut crate::ximgproc::Box);
pub fn std_vectorLcv_ximgproc_BoxG_set_size_t_const_Box(instance: *mut c_void, index: size_t, val: *const crate::ximgproc::Box);
pub fn std_vectorLcv_ximgproc_BoxG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_ximgproc_BoxG_data_const(instance: *const c_void) -> *const crate::ximgproc::Box;
pub fn std_vectorLcv_ximgproc_BoxG_dataMut(instance: *mut c_void) -> *mut crate::ximgproc::Box;
pub fn cv_fromSlice_const_const_BoxX_size_t(data: *const crate::ximgproc::Box, len: size_t) -> *mut c_void;
