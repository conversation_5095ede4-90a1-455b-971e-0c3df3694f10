extern "C" {
	const cv::linemod::Modality* cv_PtrLcv_linemod_ModalityG_getInnerPtr_const(const cv::Ptr<cv::linemod::Modality>* instance) {
			return instance->get();
	}
	
	cv::linemod::Modality* cv_PtrLcv_linemod_ModalityG_getInnerPtrMut(cv::Ptr<cv::linemod::Modality>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_linemod_ModalityG_delete(cv::Ptr<cv::linemod::Modality>* instance) {
			delete instance;
	}
	
}

