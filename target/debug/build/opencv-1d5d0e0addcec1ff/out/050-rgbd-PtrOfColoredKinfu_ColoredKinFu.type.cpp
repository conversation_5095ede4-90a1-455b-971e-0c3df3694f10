extern "C" {
	const cv::colored_kinfu::ColoredKinFu* cv_PtrLcv_colored_kinfu_ColoredKinFuG_getInnerPtr_const(const cv::Ptr<cv::colored_kinfu::ColoredKinFu>* instance) {
			return instance->get();
	}
	
	cv::colored_kinfu::ColoredKinFu* cv_PtrLcv_colored_kinfu_ColoredKinFuG_getInnerPtrMut(cv::Ptr<cv::colored_kinfu::ColoredKinFu>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_colored_kinfu_ColoredKinFuG_delete(cv::Ptr<cv::colored_kinfu::ColoredKinFu>* instance) {
			delete instance;
	}
	
}

