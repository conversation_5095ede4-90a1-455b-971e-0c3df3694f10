pub fn cv_PtrLcv_detail_BundleAdjusterAffineG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterAffineG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterAffineG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_BundleAdjusterAffineG_to_PtrOfDetail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterAffineG_to_PtrOfDetail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_BundleAdjusterAffineG_new_const_BundleAdjusterAffine(val: *mut c_void) -> *mut c_void;
