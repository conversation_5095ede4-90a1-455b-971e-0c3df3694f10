pub fn cv_PtrLcv_CompressedRectilinearWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_CompressedRectilinearWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_CompressedRectilinearWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_CompressedRectilinearWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_CompressedRectilinearWarperG_new_const_CompressedRectilinearWarper(val: *mut c_void) -> *mut c_void;
