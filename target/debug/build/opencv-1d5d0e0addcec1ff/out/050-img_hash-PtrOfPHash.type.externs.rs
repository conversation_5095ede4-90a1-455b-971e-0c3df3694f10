pub fn cv_PtrLcv_img_hash_PHashG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_img_hash_PHashG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_img_hash_PHashG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_img_hash_PHashG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_img_hash_PHashG_to_PtrOfImgHashBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_img_hash_PHashG_new_const_PHash(val: *mut c_void) -> *mut c_void;
