extern "C" {
	const cv::videostab::IDenseOptFlowEstimator* cv_PtrLcv_videostab_IDenseOptFlowEstimatorG_getInnerPtr_const(const cv::Ptr<cv::videostab::IDenseOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	cv::videostab::IDenseOptFlowEstimator* cv_PtrLcv_videostab_IDenseOptFlowEstimatorG_getInnerPtrMut(cv::Ptr<cv::videostab::IDenseOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_IDenseOptFlowEstimatorG_delete(cv::Ptr<cv::videostab::IDenseOptFlowEstimator>* instance) {
			delete instance;
	}
	
}

