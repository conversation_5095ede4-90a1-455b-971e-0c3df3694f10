pub fn cv_text_MSERsToERStats_const__InputArrayR_vectorLvectorLPointGGR_vectorLvectorLERStatGGR(image: *const c_void, contours: *mut c_void, regions: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_computeNMChannels_const__InputArrayR_const__OutputArrayR(_src: *const c_void, _channels: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_computeNMChannels_const__InputArrayR_const__OutputArrayR_int(_src: *const c_void, _channels: *const c_void, _mode: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_createERFilterNM1_const_PtrLCallbackGR(cb: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createERFilterNM1_const_PtrLCallbackGR_int_float_float_float_bool_float(cb: *const c_void, threshold_delta: i32, min_area: f32, max_area: f32, min_probability: f32, non_max_suppression: bool, min_probability_diff: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createERFilterNM1_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createERFilterNM1_const_StringR_int_float_float_float_bool_float(filename: *const c_char, threshold_delta: i32, min_area: f32, max_area: f32, min_probability: f32, non_max_suppression: bool, min_probability_diff: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createERFilterNM2_const_PtrLCallbackGR(cb: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createERFilterNM2_const_PtrLCallbackGR_float(cb: *const c_void, min_probability: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createERFilterNM2_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createERFilterNM2_const_StringR_float(filename: *const c_char, min_probability: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createOCRHMMTransitionsTable_const_StringR_vectorLStringGR(vocabulary: *const c_char, lexicon: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_createOCRHMMTransitionsTable_stringR_vectorLstringGR_const__OutputArrayR(vocabulary: *mut *mut c_void, lexicon: *mut c_void, transition_probabilities_table: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_detectRegions_const__InputArrayR_const_PtrLERFilterGR_const_PtrLERFilterGR_vectorLRectGR(image: *const c_void, er_filter1: *const c_void, er_filter2: *const c_void, groups_rects: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_detectRegions_const__InputArrayR_const_PtrLERFilterGR_const_PtrLERFilterGR_vectorLRectGR_int_const_StringR_float(image: *const c_void, er_filter1: *const c_void, er_filter2: *const c_void, groups_rects: *mut c_void, method: i32, filename: *const c_char, min_probability: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_detectRegions_const__InputArrayR_const_PtrLERFilterGR_const_PtrLERFilterGR_vectorLvectorLPointGGR(image: *const c_void, er_filter1: *const c_void, er_filter2: *const c_void, regions: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_detectTextSWT_const__InputArrayR_vectorLRectGR_bool(input: *const c_void, result: *mut c_void, dark_on_light: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_text_detectTextSWT_const__InputArrayR_vectorLRectGR_bool_const__OutputArrayR_const__OutputArrayR(input: *const c_void, result: *mut c_void, dark_on_light: bool, draw: *const c_void, chain_b_bs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_erGrouping_const__InputArrayR_const__InputArrayR_vectorLvectorLERStatGGR_vectorLvectorLVec2iGGR_vectorLRectGR(img: *const c_void, channels: *const c_void, regions: *mut c_void, groups: *mut c_void, groups_rects: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_erGrouping_const__InputArrayR_const__InputArrayR_vectorLvectorLERStatGGR_vectorLvectorLVec2iGGR_vectorLRectGR_int_const_stringR_float(img: *const c_void, channels: *const c_void, regions: *mut c_void, groups: *mut c_void, groups_rects: *mut c_void, method: i32, filename: *const c_char, min_probablity: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_erGrouping_const__InputArrayR_const__InputArrayR_vectorLvectorLPointGG_vectorLRectGR(image: *const c_void, channel: *const c_void, regions: *mut c_void, groups_rects: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_erGrouping_const__InputArrayR_const__InputArrayR_vectorLvectorLPointGG_vectorLRectGR_int_const_StringR_float(image: *const c_void, channel: *const c_void, regions: *mut c_void, groups_rects: *mut c_void, method: i32, filename: *const c_char, min_probablity: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_loadClassifierNM1_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_loadClassifierNM2_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_loadOCRBeamSearchClassifierCNN_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_loadOCRHMMClassifierCNN_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_loadOCRHMMClassifierNM_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_loadOCRHMMClassifier_const_StringR_int(filename: *const c_char, classifier: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_BaseOCR_run_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_BaseOCR_run_MatR_stringR(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_BaseOCR_run_MatR_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_BaseOCR_run_MatR_MatR_stringR(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_BaseOCR_to_OCRBeamSearchDecoder(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_BaseOCR_to_OCRHMMDecoder(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_BaseOCR_to_OCRHolisticWordRecognizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_BaseOCR_to_OCRTesseract(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_BaseOCR_delete(instance: *mut c_void);
pub fn cv_text_ERFilter_run_const__InputArrayR_vectorLERStatGR(instance: *mut c_void, image: *const c_void, regions: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_ERFilter_setCallback_const_PtrLCallbackGR(instance: *mut c_void, cb: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_ERFilter_setThresholdDelta_int(instance: *mut c_void, threshold_delta: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_ERFilter_setMinArea_float(instance: *mut c_void, min_area: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_ERFilter_setMaxArea_float(instance: *mut c_void, max_area: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_ERFilter_setMinProbability_float(instance: *mut c_void, min_probability: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_ERFilter_setMinProbabilityDiff_float(instance: *mut c_void, min_probability_diff: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_ERFilter_setNonMaxSuppression_bool(instance: *mut c_void, non_max_suppression: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_text_ERFilter_getNumRejected_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_text_ERFilter_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_ERFilter_delete(instance: *mut c_void);
pub fn cv_text_ERFilter_Callback_eval_const_ERStatR(instance: *mut c_void, stat: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_text_ERFilter_Callback_delete(instance: *mut c_void);
pub fn cv_text_ERStat_ERStat_int_int_int_int(level: i32, pixel: i32, x: i32, y: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_ERStat_ERStat(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_ERStat_propPixel_const(instance: *const c_void) -> i32;
pub fn cv_text_ERStat_propPixel_const_int(instance: *mut c_void, val: i32);
pub fn cv_text_ERStat_propLevel_const(instance: *const c_void) -> i32;
pub fn cv_text_ERStat_propLevel_const_int(instance: *mut c_void, val: i32);
pub fn cv_text_ERStat_propArea_const(instance: *const c_void) -> i32;
pub fn cv_text_ERStat_propArea_const_int(instance: *mut c_void, val: i32);
pub fn cv_text_ERStat_propPerimeter_const(instance: *const c_void) -> i32;
pub fn cv_text_ERStat_propPerimeter_const_int(instance: *mut c_void, val: i32);
pub fn cv_text_ERStat_propEuler_const(instance: *const c_void) -> i32;
pub fn cv_text_ERStat_propEuler_const_int(instance: *mut c_void, val: i32);
pub fn cv_text_ERStat_propRect_const(instance: *const c_void, ocvrs_return: *mut core::Rect);
pub fn cv_text_ERStat_propRect_const_Rect(instance: *mut c_void, val: *const core::Rect);
pub fn cv_text_ERStat_propRaw_moments_const(instance: *const c_void) -> *const [f64; 2];
pub fn cv_text_ERStat_propRaw_moments(instance: *mut c_void) -> *mut [f64; 2];
pub fn cv_text_ERStat_propCentral_moments_const(instance: *const c_void) -> *const [f64; 3];
pub fn cv_text_ERStat_propCentral_moments(instance: *mut c_void) -> *mut [f64; 3];
pub fn cv_text_ERStat_propMed_crossings_const(instance: *const c_void) -> f32;
pub fn cv_text_ERStat_propMed_crossings_const_float(instance: *mut c_void, val: f32);
pub fn cv_text_ERStat_propHole_area_ratio_const(instance: *const c_void) -> f32;
pub fn cv_text_ERStat_propHole_area_ratio_const_float(instance: *mut c_void, val: f32);
pub fn cv_text_ERStat_propConvex_hull_ratio_const(instance: *const c_void) -> f32;
pub fn cv_text_ERStat_propConvex_hull_ratio_const_float(instance: *mut c_void, val: f32);
pub fn cv_text_ERStat_propNum_inflexion_points_const(instance: *const c_void) -> f32;
pub fn cv_text_ERStat_propNum_inflexion_points_const_float(instance: *mut c_void, val: f32);
pub fn cv_text_ERStat_propProbability_const(instance: *const c_void) -> f64;
pub fn cv_text_ERStat_propProbability_const_double(instance: *mut c_void, val: f64);
pub fn cv_text_ERStat_propParent(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_ERStat_propParent_ERStatX(instance: *mut c_void, val: *const c_void);
pub fn cv_text_ERStat_propChild(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_ERStat_propChild_ERStatX(instance: *mut c_void, val: *const c_void);
pub fn cv_text_ERStat_propNext(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_ERStat_propNext_ERStatX(instance: *mut c_void, val: *const c_void);
pub fn cv_text_ERStat_propPrev(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_ERStat_propPrev_ERStatX(instance: *mut c_void, val: *const c_void);
pub fn cv_text_ERStat_propLocal_maxima_const(instance: *const c_void) -> bool;
pub fn cv_text_ERStat_propLocal_maxima_const_bool(instance: *mut c_void, val: bool);
pub fn cv_text_ERStat_propMax_probability_ancestor(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_ERStat_propMax_probability_ancestor_ERStatX(instance: *mut c_void, val: *const c_void);
pub fn cv_text_ERStat_propMin_probability_ancestor(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_ERStat_propMin_probability_ancestor_ERStatX(instance: *mut c_void, val: *const c_void);
pub fn cv_text_ERStat_delete(instance: *mut c_void);
pub fn cv_text_OCRBeamSearchDecoder_run_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRBeamSearchDecoder_run_MatR_stringR(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRBeamSearchDecoder_run_MatR_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRBeamSearchDecoder_run_MatR_MatR_stringR(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRBeamSearchDecoder_run_const__InputArrayR_int_int(instance: *mut c_void, image: *const c_void, min_confidence: i32, component_level: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRBeamSearchDecoder_run_const__InputArrayR_int(instance: *mut c_void, image: *const c_void, min_confidence: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRBeamSearchDecoder_run_const__InputArrayR_const__InputArrayR_int_int(instance: *mut c_void, image: *const c_void, mask: *const c_void, min_confidence: i32, component_level: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRBeamSearchDecoder_run_const__InputArrayR_const__InputArrayR_int(instance: *mut c_void, image: *const c_void, mask: *const c_void, min_confidence: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRBeamSearchDecoder_create_const_PtrLClassifierCallbackG_const_stringR_const__InputArrayR_const__InputArrayR_decoder_mode_int(classifier: *const c_void, vocabulary: *const c_char, transition_probabilities_table: *const c_void, emission_probabilities_table: *const c_void, mode: crate::text::decoder_mode, beam_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRBeamSearchDecoder_create_const_StringR_const_StringR_const__InputArrayR_const__InputArrayR_decoder_mode_int(filename: *const c_char, vocabulary: *const c_char, transition_probabilities_table: *const c_void, emission_probabilities_table: *const c_void, mode: crate::text::decoder_mode, beam_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRBeamSearchDecoder_create_const_StringR_const_StringR_const__InputArrayR_const__InputArrayR(filename: *const c_char, vocabulary: *const c_char, transition_probabilities_table: *const c_void, emission_probabilities_table: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRBeamSearchDecoder_to_BaseOCR(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_OCRBeamSearchDecoder_delete(instance: *mut c_void);
pub fn cv_text_OCRBeamSearchDecoder_ClassifierCallback_eval_const__InputArrayR_vectorLvectorLdoubleGGR_vectorLintGR(instance: *mut c_void, image: *const c_void, recognition_probabilities: *mut c_void, oversegmentation: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRBeamSearchDecoder_ClassifierCallback_getWindowSize(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_text_OCRBeamSearchDecoder_ClassifierCallback_getStepSize(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_text_OCRBeamSearchDecoder_ClassifierCallback_delete(instance: *mut c_void);
pub fn cv_text_OCRHMMDecoder_run_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHMMDecoder_run_MatR_stringR(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHMMDecoder_run_MatR_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHMMDecoder_run_MatR_MatR_stringR(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHMMDecoder_run_const__InputArrayR_int_int(instance: *mut c_void, image: *const c_void, min_confidence: i32, component_level: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHMMDecoder_run_const__InputArrayR_int(instance: *mut c_void, image: *const c_void, min_confidence: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHMMDecoder_run_const__InputArrayR_const__InputArrayR_int_int(instance: *mut c_void, image: *const c_void, mask: *const c_void, min_confidence: i32, component_level: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHMMDecoder_run_const__InputArrayR_const__InputArrayR_int(instance: *mut c_void, image: *const c_void, mask: *const c_void, min_confidence: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHMMDecoder_create_const_PtrLClassifierCallbackG_const_StringR_const__InputArrayR_const__InputArrayR_int(classifier: *const c_void, vocabulary: *const c_char, transition_probabilities_table: *const c_void, emission_probabilities_table: *const c_void, mode: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHMMDecoder_create_const_PtrLClassifierCallbackG_const_StringR_const__InputArrayR_const__InputArrayR(classifier: *const c_void, vocabulary: *const c_char, transition_probabilities_table: *const c_void, emission_probabilities_table: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHMMDecoder_create_const_StringR_const_StringR_const__InputArrayR_const__InputArrayR_int_int(filename: *const c_char, vocabulary: *const c_char, transition_probabilities_table: *const c_void, emission_probabilities_table: *const c_void, mode: i32, classifier: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHMMDecoder_create_const_StringR_const_StringR_const__InputArrayR_const__InputArrayR(filename: *const c_char, vocabulary: *const c_char, transition_probabilities_table: *const c_void, emission_probabilities_table: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHMMDecoder_to_BaseOCR(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_OCRHMMDecoder_delete(instance: *mut c_void);
pub fn cv_text_OCRHMMDecoder_ClassifierCallback_eval_const__InputArrayR_vectorLintGR_vectorLdoubleGR(instance: *mut c_void, image: *const c_void, out_class: *mut c_void, out_confidence: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHMMDecoder_ClassifierCallback_delete(instance: *mut c_void);
pub fn cv_text_OCRHolisticWordRecognizer_run_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHolisticWordRecognizer_run_MatR_stringR(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHolisticWordRecognizer_run_MatR_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHolisticWordRecognizer_run_MatR_MatR_stringR(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRHolisticWordRecognizer_create_const_stringR_const_stringR_const_stringR(arch_filename: *const c_char, weights_filename: *const c_char, words_filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRHolisticWordRecognizer_to_BaseOCR(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_OCRHolisticWordRecognizer_delete(instance: *mut c_void);
pub fn cv_text_OCRTesseract_run_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRTesseract_run_MatR_stringR(instance: *mut c_void, image: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRTesseract_run_MatR_MatR_stringR_vectorLRectGX_vectorLstringGX_vectorLfloatGX_int(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, component_rects: *mut c_void, component_texts: *mut c_void, component_confidences: *mut c_void, component_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRTesseract_run_MatR_MatR_stringR(instance: *mut c_void, image: *mut c_void, mask: *mut c_void, output_text: *mut *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRTesseract_run_const__InputArrayR_int_int(instance: *mut c_void, image: *const c_void, min_confidence: i32, component_level: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRTesseract_run_const__InputArrayR_int(instance: *mut c_void, image: *const c_void, min_confidence: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRTesseract_run_const__InputArrayR_const__InputArrayR_int_int(instance: *mut c_void, image: *const c_void, mask: *const c_void, min_confidence: i32, component_level: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRTesseract_run_const__InputArrayR_const__InputArrayR_int(instance: *mut c_void, image: *const c_void, mask: *const c_void, min_confidence: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRTesseract_setWhiteList_const_StringR(instance: *mut c_void, char_whitelist: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_text_OCRTesseract_create_const_charX_const_charX_const_charX_int_int(datapath: *const c_char, language: *const c_char, char_whitelist: *const c_char, oem: i32, psmode: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRTesseract_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_OCRTesseract_to_BaseOCR(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_OCRTesseract_delete(instance: *mut c_void);
pub fn cv_text_TextDetector_detect_const__InputArrayR_vectorLRectGR_vectorLfloatGR(instance: *mut c_void, input_image: *const c_void, bbox: *mut c_void, confidence: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_TextDetector_to_TextDetectorCNN(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_TextDetector_delete(instance: *mut c_void);
pub fn cv_text_TextDetectorCNN_detect_const__InputArrayR_vectorLRectGR_vectorLfloatGR(instance: *mut c_void, input_image: *const c_void, bbox: *mut c_void, confidence: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_text_TextDetectorCNN_create_const_StringR_const_StringR_vectorLSizeG(model_arch_filename: *const c_char, model_weights_filename: *const c_char, detection_sizes: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_TextDetectorCNN_create_const_StringR_const_StringR(model_arch_filename: *const c_char, model_weights_filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_text_TextDetectorCNN_to_TextDetector(instance: *mut c_void) -> *mut c_void;
pub fn cv_text_TextDetectorCNN_delete(instance: *mut c_void);
