pub fn cv_PtrLcv_detail_NoSeamFinderG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoSeamFinderG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoSeamFinderG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_NoSeamFinderG_to_PtrOfDetail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_NoSeamFinderG_new_const_NoSeamFinder(val: *mut c_void) -> *mut c_void;
