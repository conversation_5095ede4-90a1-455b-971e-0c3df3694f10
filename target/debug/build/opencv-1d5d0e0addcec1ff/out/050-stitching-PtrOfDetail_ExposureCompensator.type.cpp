extern "C" {
	const cv::detail::ExposureCompensator* cv_PtrLcv_detail_ExposureCompensatorG_getInnerPtr_const(const cv::Ptr<cv::detail::ExposureCompensator>* instance) {
			return instance->get();
	}
	
	cv::detail::ExposureCompensator* cv_PtrLcv_detail_ExposureCompensatorG_getInnerPtrMut(cv::Ptr<cv::detail::ExposureCompensator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_ExposureCompensatorG_delete(cv::Ptr<cv::detail::ExposureCompensator>* instance) {
			delete instance;
	}
	
}

