pub fn cv_PtrLcv_ml_BoostG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_BoostG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_BoostG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ml_BoostG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_BoostG_to_PtrOfDTrees(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ml_BoostG_to_PtrOfStatModel(instance: *mut c_void) -> *mut c_void;
