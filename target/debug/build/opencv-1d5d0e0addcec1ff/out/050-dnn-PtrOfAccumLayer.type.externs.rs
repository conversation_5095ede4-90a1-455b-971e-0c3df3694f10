pub fn cv_PtrLcv_dnn_AccumLayerG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_dnn_AccumLayerG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_dnn_AccumLayerG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_dnn_AccumLayerG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_dnn_AccumLayerG_to_PtrOfLayer(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_dnn_AccumLayerG_new_const_AccumLayer(val: *mut c_void) -> *mut c_void;
