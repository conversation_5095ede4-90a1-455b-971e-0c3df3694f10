extern "C" {
	const cv::mcc::CCheckerDraw* cv_PtrLcv_mcc_CCheckerDrawG_getInnerPtr_const(const cv::Ptr<cv::mcc::CCheckerDraw>* instance) {
			return instance->get();
	}
	
	cv::mcc::CCheckerDraw* cv_PtrLcv_mcc_CCheckerDrawG_getInnerPtrMut(cv::Ptr<cv::mcc::CCheckerDraw>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_mcc_CCheckerDrawG_delete(cv::Ptr<cv::mcc::CCheckerDraw>* instance) {
			delete instance;
	}
	
}

