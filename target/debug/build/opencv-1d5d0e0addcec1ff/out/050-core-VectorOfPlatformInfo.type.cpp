extern "C" {
	std::vector<cv::ocl::PlatformInfo>* std_vectorLcv_ocl_PlatformInfoG_new_const() {
			std::vector<cv::ocl::PlatformInfo>* ret = new std::vector<cv::ocl::PlatformInfo>();
			return ret;
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_delete(std::vector<cv::ocl::PlatformInfo>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_ocl_PlatformInfoG_len_const(const std::vector<cv::ocl::PlatformInfo>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_ocl_PlatformInfoG_isEmpty_const(const std::vector<cv::ocl::PlatformInfo>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_ocl_PlatformInfoG_capacity_const(const std::vector<cv::ocl::PlatformInfo>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_shrinkToFit(std::vector<cv::ocl::PlatformInfo>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_reserve_size_t(std::vector<cv::ocl::PlatformInfo>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_remove_size_t(std::vector<cv::ocl::PlatformInfo>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_swap_size_t_size_t(std::vector<cv::ocl::PlatformInfo>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_clear(std::vector<cv::ocl::PlatformInfo>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_push_const_PlatformInfo(std::vector<cv::ocl::PlatformInfo>* instance, const cv::ocl::PlatformInfo* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_insert_size_t_const_PlatformInfo(std::vector<cv::ocl::PlatformInfo>* instance, size_t index, const cv::ocl::PlatformInfo* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_get_const_size_t(const std::vector<cv::ocl::PlatformInfo>* instance, size_t index, cv::ocl::PlatformInfo** ocvrs_return) {
			cv::ocl::PlatformInfo ret = (*instance)[index];
			*ocvrs_return = new cv::ocl::PlatformInfo(ret);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_set_size_t_const_PlatformInfo(std::vector<cv::ocl::PlatformInfo>* instance, size_t index, const cv::ocl::PlatformInfo* val) {
			(*instance)[index] = *val;
	}
	
}


