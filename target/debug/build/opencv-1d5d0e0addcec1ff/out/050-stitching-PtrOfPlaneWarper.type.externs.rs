pub fn cv_PtrLcv_PlaneWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_PlaneWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_PlaneWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_PlaneWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_PlaneWarperG_new_const_PlaneWarper(val: *mut c_void) -> *mut c_void;
