pub fn cv_PtrLcv_optflow_GPCTreeG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_optflow_GPCTreeG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_optflow_GPCTreeG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_optflow_GPCTreeG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_optflow_GPCTreeG_new_const_GPCTree(val: *mut c_void) -> *mut c_void;
