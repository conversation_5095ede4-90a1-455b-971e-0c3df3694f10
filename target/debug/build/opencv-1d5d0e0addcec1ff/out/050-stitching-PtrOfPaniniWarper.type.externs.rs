pub fn cv_PtrLcv_PaniniWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_PaniniWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_PaniniWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_PaniniWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_PaniniWarperG_new_const_PaniniWarper(val: *mut c_void) -> *mut c_void;
