extern "C" {
	const cv::detail::FeaturesMatcher* cv_PtrLcv_detail_FeaturesMatcherG_getInnerPtr_const(const cv::Ptr<cv::detail::FeaturesMatcher>* instance) {
			return instance->get();
	}
	
	cv::detail::FeaturesMatcher* cv_PtrLcv_detail_FeaturesMatcherG_getInnerPtrMut(cv::Ptr<cv::detail::FeaturesMatcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_FeaturesMatcherG_delete(cv::Ptr<cv::detail::FeaturesMatcher>* instance) {
			delete instance;
	}
	
}

