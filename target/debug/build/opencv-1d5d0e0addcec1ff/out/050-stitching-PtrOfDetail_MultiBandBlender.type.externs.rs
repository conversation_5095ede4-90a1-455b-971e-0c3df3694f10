pub fn cv_PtrLcv_detail_MultiBandBlenderG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_MultiBandBlenderG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_MultiBandBlenderG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_MultiBandBlenderG_to_PtrOfDetail_Blender(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_MultiBandBlenderG_new_const_MultiBandBlender(val: *mut c_void) -> *mut c_void;
