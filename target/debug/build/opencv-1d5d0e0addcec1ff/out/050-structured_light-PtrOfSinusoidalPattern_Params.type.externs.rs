pub fn cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_new_const_Params(val: *mut c_void) -> *mut c_void;
