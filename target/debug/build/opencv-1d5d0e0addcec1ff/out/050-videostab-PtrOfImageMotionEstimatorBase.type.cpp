extern "C" {
	const cv::videostab::ImageMotionEstimatorBase* cv_PtrLcv_videostab_ImageMotionEstimatorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::ImageMotionEstimatorBase* cv_PtrLcv_videostab_ImageMotionEstimatorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ImageMotionEstimatorBaseG_delete(cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* instance) {
			delete instance;
	}
	
}

