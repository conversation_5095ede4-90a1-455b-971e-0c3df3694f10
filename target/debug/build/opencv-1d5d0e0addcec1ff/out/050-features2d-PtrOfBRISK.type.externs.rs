pub fn cv_PtrLcv_BRISKG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_BRISKG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_BRISKG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_BRISKG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_BRISKG_to_PtrOfFeature2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_BRISKG_new_const_BRISK(val: *mut c_void) -> *mut c_void;
