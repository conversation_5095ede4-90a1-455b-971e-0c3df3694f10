pub fn cv_motempl_calcGlobalOrientation_const__InputArrayR_const__InputArrayR_const__InputArrayR_double_double(orientation: *const c_void, mask: *const c_void, mhi: *const c_void, timestamp: f64, duration: f64, ocvrs_return: *mut Result<f64>);
pub fn cv_motempl_calcMotionGradient_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double_double(mhi: *const c_void, mask: *const c_void, orientation: *const c_void, delta1: f64, delta2: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_motempl_calcMotionGradient_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double_double_int(mhi: *const c_void, mask: *const c_void, orientation: *const c_void, delta1: f64, delta2: f64, aperture_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_motempl_segmentMotion_const__InputArrayR_const__OutputArrayR_vectorLRectGR_double_double(mhi: *const c_void, segmask: *const c_void, bounding_rects: *mut c_void, timestamp: f64, seg_thresh: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_motempl_updateMotionHistory_const__InputArrayR_const__InputOutputArrayR_double_double(silhouette: *const c_void, mhi: *const c_void, timestamp: f64, duration: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_calcOpticalFlowDenseRLOF_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR(i0: *const c_void, i1: *const c_void, flow: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_calcOpticalFlowDenseRLOF_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_PtrLRLOFOpticalFlowParameterG_float_Size_InterpolationType_int_float_float_int_int_bool_float_float_bool(i0: *const c_void, i1: *const c_void, flow: *const c_void, rlof_param: *mut c_void, forward_backward_threshold: f32, grid_step: *const core::Size, interp_type: crate::optflow::InterpolationType, epic_k: i32, epic_sigma: f32, epic_lambda: f32, ric_sp_size: i32, ric_slic_type: i32, use_post_proc: bool, fgs_lambda: f32, fgs_sigma: f32, use_variational_refinement: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_calcOpticalFlowSF_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_int_int(from: *const c_void, to: *const c_void, flow: *const c_void, layers: i32, averaging_block_size: i32, max_flow: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_calcOpticalFlowSF_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_int_int_double_double_int_double_double_double_int_double_double_double(from: *const c_void, to: *const c_void, flow: *const c_void, layers: i32, averaging_block_size: i32, max_flow: i32, sigma_dist: f64, sigma_color: f64, postprocess_window: i32, sigma_dist_fix: f64, sigma_color_fix: f64, occ_thr: f64, upscale_averaging_radius: i32, upscale_sigma_dist: f64, upscale_sigma_color: f64, speed_up_thr: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_calcOpticalFlowSparseRLOF_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(prev_img: *const c_void, next_img: *const c_void, prev_pts: *const c_void, next_pts: *const c_void, status: *const c_void, err: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_calcOpticalFlowSparseRLOF_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_PtrLRLOFOpticalFlowParameterG_float(prev_img: *const c_void, next_img: *const c_void, prev_pts: *const c_void, next_pts: *const c_void, status: *const c_void, err: *const c_void, rlof_param: *mut c_void, forward_backward_threshold: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_calcOpticalFlowSparseToDense_const__InputArrayR_const__InputArrayR_const__OutputArrayR(from: *const c_void, to: *const c_void, flow: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_calcOpticalFlowSparseToDense_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_int_float_bool_float_float(from: *const c_void, to: *const c_void, flow: *const c_void, grid_step: i32, k: i32, sigma: f32, use_post_proc: bool, fgs_lambda: f32, fgs_sigma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_createOptFlow_DeepFlow(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_createOptFlow_DenseRLOF(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_createOptFlow_DualTVL1(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_createOptFlow_Farneback(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_createOptFlow_PCAFlow(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_createOptFlow_SimpleFlow(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_createOptFlow_SparseRLOF(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_createOptFlow_SparseToDense(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_read_const_FileNodeR_NodeR_Node(fn_: *const c_void, node: *mut crate::optflow::GPCTree_Node, unnamed: *const crate::optflow::GPCTree_Node, ocvrs_return: *mut ResultVoid);
pub fn cv_write_FileStorageR_const_StringR_const_NodeR(fs: *mut c_void, name: *const c_char, node: *const crate::optflow::GPCTree_Node, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_setRLOFOpticalFlowParameter_PtrLRLOFOpticalFlowParameterG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getRLOFOpticalFlowParameter_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setForwardBackward_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getForwardBackward_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_DenseRLOFOpticalFlow_getGridStep_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setGridStep_Size(instance: *mut c_void, val: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_setInterpolation_InterpolationType(instance: *mut c_void, val: crate::optflow::InterpolationType, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getInterpolation_const(instance: *const c_void, ocvrs_return: *mut Result<crate::optflow::InterpolationType>);
pub fn cv_optflow_DenseRLOFOpticalFlow_getEPICK_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setEPICK_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getEPICSigma_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setEPICSigma_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getEPICLambda_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setEPICLambda_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getFgsLambda_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setFgsLambda_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getFgsSigma_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setFgsSigma_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_setUsePostProc_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getUsePostProc_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setUseVariationalRefinement_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getUseVariationalRefinement_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setRICSPSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getRICSPSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_DenseRLOFOpticalFlow_setRICSLICType_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DenseRLOFOpticalFlow_getRICSLICType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_DenseRLOFOpticalFlow_create_PtrLRLOFOpticalFlowParameterG_float_Size_InterpolationType_int_float_float_int_int_bool_float_float_bool(rlof_param: *mut c_void, forward_backward_threshold: f32, grid_step: *const core::Size, interp_type: crate::optflow::InterpolationType, epic_k: i32, epic_sigma: f32, epic_lambda: f32, ric_sp_size: i32, ric_slic_type: i32, use_post_proc: bool, fgs_lambda: f32, fgs_sigma: f32, use_variational_refinement: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_DenseRLOFOpticalFlow_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_DenseRLOFOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_DenseRLOFOpticalFlow_to_DenseOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_DenseRLOFOpticalFlow_delete(instance: *mut c_void);
pub fn cv_optflow_DualTVL1OpticalFlow_getTau_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_optflow_DualTVL1OpticalFlow_setTau_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getLambda_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_optflow_DualTVL1OpticalFlow_setLambda_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getTheta_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_optflow_DualTVL1OpticalFlow_setTheta_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getGamma_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_optflow_DualTVL1OpticalFlow_setGamma_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getScalesNumber_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_DualTVL1OpticalFlow_setScalesNumber_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getWarpingsNumber_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_DualTVL1OpticalFlow_setWarpingsNumber_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getEpsilon_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_optflow_DualTVL1OpticalFlow_setEpsilon_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getInnerIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_DualTVL1OpticalFlow_setInnerIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getOuterIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_DualTVL1OpticalFlow_setOuterIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getUseInitialFlow_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_DualTVL1OpticalFlow_setUseInitialFlow_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getScaleStep_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_optflow_DualTVL1OpticalFlow_setScaleStep_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_getMedianFiltering_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_DualTVL1OpticalFlow_setMedianFiltering_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_DualTVL1OpticalFlow_create_double_double_double_int_int_double_int_int_double_double_int_bool(tau: f64, lambda: f64, theta: f64, nscales: i32, warps: i32, epsilon: f64, innner_iterations: i32, outer_iterations: i32, scale_step: f64, gamma: f64, median_filtering: i32, use_initial_flow: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_DualTVL1OpticalFlow_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_DualTVL1OpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_DualTVL1OpticalFlow_to_DenseOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_DualTVL1OpticalFlow_delete(instance: *mut c_void);
pub fn cv_optflow_GPCDetails_dropOutliers_vectorLpairLcv_Point2i__cv_Point2iGGR(corr: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCDetails_getAllDescriptorsForImage_const_MatX_vectorLGPCPatchDescriptorGR_const_GPCMatchingParamsR_int(img_ch: *const c_void, descr: *mut c_void, mp: *const crate::optflow::GPCMatchingParams, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCDetails_getCoordinatesFromIndex_size_t_Size_intR_intR(index: size_t, sz: *const core::Size, x: *mut i32, y: *mut i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCDetails_delete(instance: *mut c_void);
pub fn cv_optflow_GPCMatchingParams_GPCMatchingParams_bool(_use_opencl: bool, ocvrs_return: *mut Result<crate::optflow::GPCMatchingParams>);
pub fn cv_optflow_GPCMatchingParams_GPCMatchingParams(ocvrs_return: *mut Result<crate::optflow::GPCMatchingParams>);
pub fn cv_optflow_GPCMatchingParams_GPCMatchingParams_const_GPCMatchingParamsR(params: *const crate::optflow::GPCMatchingParams, ocvrs_return: *mut Result<crate::optflow::GPCMatchingParams>);
pub fn cv_optflow_GPCPatchDescriptor_dot_const_const_VecLdouble__18GR(instance: *const c_void, coef: *const core::VecN<f64, 18>, ocvrs_return: *mut Result<f64>);
pub fn cv_optflow_GPCPatchDescriptor_markAsSeparated(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCPatchDescriptor_isSeparated_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_GPCPatchDescriptor_propFeature_const(instance: *const c_void, ocvrs_return: *mut core::VecN<f64, 18>);
pub fn cv_optflow_GPCPatchDescriptor_propFeature_const_VecLdouble__18G(instance: *mut c_void, val: *const core::VecN<f64, 18>);
pub fn cv_optflow_GPCPatchDescriptor_delete(instance: *mut c_void);
pub fn cv_optflow_GPCPatchSample_getDirections_const_boolR_boolR_boolR_const_VecLdouble__18GR_double(instance: *const c_void, refdir: *mut bool, posdir: *mut bool, negdir: *mut bool, coef: *const core::VecN<f64, 18>, rhs: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCPatchSample_propRef_const(instance: *const c_void) -> *mut c_void;
pub fn cv_optflow_GPCPatchSample_propRef_const_GPCPatchDescriptor(instance: *mut c_void, val: *const c_void);
pub fn cv_optflow_GPCPatchSample_propPos_const(instance: *const c_void) -> *mut c_void;
pub fn cv_optflow_GPCPatchSample_propPos_const_GPCPatchDescriptor(instance: *mut c_void, val: *const c_void);
pub fn cv_optflow_GPCPatchSample_propNeg_const(instance: *const c_void) -> *mut c_void;
pub fn cv_optflow_GPCPatchSample_propNeg_const_GPCPatchDescriptor(instance: *mut c_void, val: *const c_void);
pub fn cv_optflow_GPCPatchSample_delete(instance: *mut c_void);
pub fn cv_optflow_GPCTrainingParams_GPCTrainingParams_unsigned_int_int_GPCDescType_bool(_max_tree_depth: u32, _min_number_of_samples: i32, _descriptor_type: crate::optflow::GPCDescType, _print_progress: bool, ocvrs_return: *mut Result<crate::optflow::GPCTrainingParams>);
pub fn cv_optflow_GPCTrainingParams_GPCTrainingParams(ocvrs_return: *mut Result<crate::optflow::GPCTrainingParams>);
pub fn cv_optflow_GPCTrainingParams_check_const(instance: *const crate::optflow::GPCTrainingParams, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_GPCTrainingSamples_create_const_vectorLStringGR_const_vectorLStringGR_const_vectorLStringGR_int(images_from: *const c_void, images_to: *const c_void, gt: *const c_void, descriptor_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_GPCTrainingSamples_create_const__InputArrayR_const__InputArrayR_const__InputArrayR_int(images_from: *const c_void, images_to: *const c_void, gt: *const c_void, descriptor_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_GPCTrainingSamples_size_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_optflow_GPCTrainingSamples_type_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_GPCTrainingSamples_delete(instance: *mut c_void);
pub fn cv_optflow_GPCTree_train_GPCTrainingSamplesR_const_GPCTrainingParams(instance: *mut c_void, samples: *mut c_void, params: *const crate::optflow::GPCTrainingParams, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCTree_train_GPCTrainingSamplesR(instance: *mut c_void, samples: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCTree_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCTree_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_GPCTree_findLeafForPatch_const_const_GPCPatchDescriptorR(instance: *const c_void, descr: *const c_void, ocvrs_return: *mut Result<u32>);
pub fn cv_optflow_GPCTree_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_GPCTree_operatorEQ_const_const_GPCTreeR(instance: *const c_void, t: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_GPCTree_getDescriptorType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_GPCTree_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_GPCTree_delete(instance: *mut c_void);
pub fn cv_optflow_GPCTree_Node_operatorEQ_const_const_NodeR(instance: *const crate::optflow::GPCTree_Node, n: *const crate::optflow::GPCTree_Node, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_OpticalFlowPCAFlow_OpticalFlowPCAFlow_PtrLconst_PCAPriorG_const_Size_float_float_float_float_float(_prior: *const c_void, _basis_size: *const core::Size, _sparse_rate: f32, _retained_corners_fraction: f32, _occlusions_threshold: f32, _damping_factor: f32, _clahe_clip: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_OpticalFlowPCAFlow_OpticalFlowPCAFlow(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_OpticalFlowPCAFlow_calc_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR(instance: *mut c_void, i0: *const c_void, i1: *const c_void, flow: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_OpticalFlowPCAFlow_collectGarbage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_OpticalFlowPCAFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_OpticalFlowPCAFlow_to_DenseOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_OpticalFlowPCAFlow_delete(instance: *mut c_void);
pub fn cv_optflow_PCAPrior_PCAPrior_const_charX(path_to_prior: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_PCAPrior_getPadding_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_PCAPrior_getBasisSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_PCAPrior_fillConstraints_const_floatX_floatX_floatX_floatX(instance: *const c_void, a1: *mut f32, a2: *mut f32, b1: *mut f32, b2: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_PCAPrior_delete(instance: *mut c_void);
pub fn cv_optflow_RLOFOpticalFlowParameter_RLOFOpticalFlowParameter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setUseMEstimator_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_setSolverType_SolverType(instance: *mut c_void, val: crate::optflow::SolverType, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getSolverType_const(instance: *const c_void, ocvrs_return: *mut Result<crate::optflow::SolverType>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setSupportRegionType_SupportRegionType(instance: *mut c_void, val: crate::optflow::SupportRegionType, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getSupportRegionType_const(instance: *const c_void, ocvrs_return: *mut Result<crate::optflow::SupportRegionType>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setNormSigma0_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getNormSigma0_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setNormSigma1_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getNormSigma1_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setSmallWinSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getSmallWinSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setLargeWinSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getLargeWinSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setCrossSegmentationThreshold_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getCrossSegmentationThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setMaxLevel_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getMaxLevel_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setUseInitialFlow_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getUseInitialFlow_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setUseIlluminationModel_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getUseIlluminationModel_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setUseGlobalMotionPrior_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getUseGlobalMotionPrior_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setMaxIteration_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getMaxIteration_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setMinEigenValue_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getMinEigenValue_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_setGlobalMotionRansacThreshold_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_RLOFOpticalFlowParameter_getGlobalMotionRansacThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_RLOFOpticalFlowParameter_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_RLOFOpticalFlowParameter_propSolverType_const(instance: *const c_void, ocvrs_return: *mut crate::optflow::SolverType);
pub fn cv_optflow_RLOFOpticalFlowParameter_propSolverType_const_SolverType(instance: *mut c_void, val: crate::optflow::SolverType);
pub fn cv_optflow_RLOFOpticalFlowParameter_propSupportRegionType_const(instance: *const c_void, ocvrs_return: *mut crate::optflow::SupportRegionType);
pub fn cv_optflow_RLOFOpticalFlowParameter_propSupportRegionType_const_SupportRegionType(instance: *mut c_void, val: crate::optflow::SupportRegionType);
pub fn cv_optflow_RLOFOpticalFlowParameter_propNormSigma0_const(instance: *const c_void) -> f32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propNormSigma0_const_float(instance: *mut c_void, val: f32);
pub fn cv_optflow_RLOFOpticalFlowParameter_propNormSigma1_const(instance: *const c_void) -> f32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propNormSigma1_const_float(instance: *mut c_void, val: f32);
pub fn cv_optflow_RLOFOpticalFlowParameter_propSmallWinSize_const(instance: *const c_void) -> i32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propSmallWinSize_const_int(instance: *mut c_void, val: i32);
pub fn cv_optflow_RLOFOpticalFlowParameter_propLargeWinSize_const(instance: *const c_void) -> i32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propLargeWinSize_const_int(instance: *mut c_void, val: i32);
pub fn cv_optflow_RLOFOpticalFlowParameter_propCrossSegmentationThreshold_const(instance: *const c_void) -> i32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propCrossSegmentationThreshold_const_int(instance: *mut c_void, val: i32);
pub fn cv_optflow_RLOFOpticalFlowParameter_propMaxLevel_const(instance: *const c_void) -> i32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propMaxLevel_const_int(instance: *mut c_void, val: i32);
pub fn cv_optflow_RLOFOpticalFlowParameter_propUseInitialFlow_const(instance: *const c_void) -> bool;
pub fn cv_optflow_RLOFOpticalFlowParameter_propUseInitialFlow_const_bool(instance: *mut c_void, val: bool);
pub fn cv_optflow_RLOFOpticalFlowParameter_propUseIlluminationModel_const(instance: *const c_void) -> bool;
pub fn cv_optflow_RLOFOpticalFlowParameter_propUseIlluminationModel_const_bool(instance: *mut c_void, val: bool);
pub fn cv_optflow_RLOFOpticalFlowParameter_propUseGlobalMotionPrior_const(instance: *const c_void) -> bool;
pub fn cv_optflow_RLOFOpticalFlowParameter_propUseGlobalMotionPrior_const_bool(instance: *mut c_void, val: bool);
pub fn cv_optflow_RLOFOpticalFlowParameter_propMaxIteration_const(instance: *const c_void) -> i32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propMaxIteration_const_int(instance: *mut c_void, val: i32);
pub fn cv_optflow_RLOFOpticalFlowParameter_propMinEigenValue_const(instance: *const c_void) -> f32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propMinEigenValue_const_float(instance: *mut c_void, val: f32);
pub fn cv_optflow_RLOFOpticalFlowParameter_propGlobalMotionRansacThreshold_const(instance: *const c_void) -> f32;
pub fn cv_optflow_RLOFOpticalFlowParameter_propGlobalMotionRansacThreshold_const_float(instance: *mut c_void, val: f32);
pub fn cv_optflow_RLOFOpticalFlowParameter_delete(instance: *mut c_void);
pub fn cv_optflow_SparseRLOFOpticalFlow_setRLOFOpticalFlowParameter_PtrLRLOFOpticalFlowParameterG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_SparseRLOFOpticalFlow_getRLOFOpticalFlowParameter_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_SparseRLOFOpticalFlow_setForwardBackward_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_optflow_SparseRLOFOpticalFlow_getForwardBackward_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_optflow_SparseRLOFOpticalFlow_create_PtrLRLOFOpticalFlowParameterG_float(rlof_param: *mut c_void, forward_backward_threshold: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_SparseRLOFOpticalFlow_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_optflow_SparseRLOFOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_SparseRLOFOpticalFlow_to_SparseOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_optflow_SparseRLOFOpticalFlow_delete(instance: *mut c_void);
