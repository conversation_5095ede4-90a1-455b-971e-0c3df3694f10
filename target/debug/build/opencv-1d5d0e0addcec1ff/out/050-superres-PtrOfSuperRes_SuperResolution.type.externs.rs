pub fn cv_PtrLcv_superres_SuperResolutionG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_superres_SuperResolutionG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_superres_SuperResolutionG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_superres_SuperResolutionG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_superres_SuperResolutionG_to_PtrOfSuperRes_FrameSource(instance: *mut c_void) -> *mut c_void;
