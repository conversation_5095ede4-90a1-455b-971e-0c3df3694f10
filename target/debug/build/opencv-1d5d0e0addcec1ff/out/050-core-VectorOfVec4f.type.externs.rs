pub fn std_vectorLcv_Vec4fG_new_const() -> *mut c_void;
pub fn std_vectorLcv_Vec4fG_delete(instance: *mut c_void);
pub fn std_vectorLcv_Vec4fG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec4fG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_Vec4fG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec4fG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_Vec4fG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_Vec4fG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_Vec4fG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_Vec4fG_clear(instance: *mut c_void);
pub fn std_vectorLcv_Vec4fG_push_const_Vec4f(instance: *mut c_void, val: *const core::Vec4f);
pub fn std_vectorLcv_Vec4fG_insert_size_t_const_Vec4f(instance: *mut c_void, index: size_t, val: *const core::Vec4f);
pub fn std_vectorLcv_Vec4fG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Vec4f);
pub fn std_vectorLcv_Vec4fG_set_size_t_const_Vec4f(instance: *mut c_void, index: size_t, val: *const core::Vec4f);
pub fn std_vectorLcv_Vec4fG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_Vec4fG_data_const(instance: *const c_void) -> *const core::Vec4f;
pub fn std_vectorLcv_Vec4fG_dataMut(instance: *mut c_void) -> *mut core::Vec4f;
pub fn cv_fromSlice_const_const_Vec4fX_size_t(data: *const core::Vec4f, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_Vec4fG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec4fG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec4fG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
