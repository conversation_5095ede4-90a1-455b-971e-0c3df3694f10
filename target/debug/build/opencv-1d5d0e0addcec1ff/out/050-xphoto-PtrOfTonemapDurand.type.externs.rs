pub fn cv_PtrLcv_xphoto_TonemapDurandG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_TonemapDurandG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_TonemapDurandG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_xphoto_TonemapDurandG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_TonemapDurandG_to_PtrOfTonemap(instance: *mut c_void) -> *mut c_void;
