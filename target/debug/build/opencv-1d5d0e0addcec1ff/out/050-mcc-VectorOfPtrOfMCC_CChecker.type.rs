pub type VectorOfPtrOfMCC_CChecker = core::Vector<core::Ptr<crate::mcc::MCC_CChecker>>;

impl core::Vector<core::Ptr<crate::mcc::MCC_CChecker>> {
	pub fn as_raw_VectorOfPtrOfMCC_CChecker(&self) -> extern_send!(Self) { self.as_raw() }
	pub fn as_raw_mut_VectorOfPtrOfMCC_CChecker(&mut self) -> extern_send!(mut Self) { self.as_raw_mut() }
}

vector_extern! { core::Ptr<crate::mcc::MCC_CChecker>,
	std_vectorLcv_PtrLcv_mcc_CCheckerGG_new_const, std_vectorLcv_PtrLcv_mcc_CCheckerGG_delete,
	std_vectorLcv_PtrLcv_mcc_CCheckerGG_len_const, std_vectorLcv_PtrLcv_mcc_CCheckerGG_isEmpty_const,
	std_vectorLcv_PtrLcv_mcc_CCheckerGG_capacity_const, std_vectorLcv_PtrLcv_mcc_CCheckerGG_shrinkToFit,
	std_vectorLcv_PtrLcv_mcc_CCheckerGG_reserve_size_t, std_vectorLcv_PtrLcv_mcc_CCheckerGG_remove_size_t,
	std_vectorLcv_PtrLcv_mcc_CCheckerGG_swap_size_t_size_t, std_vectorLcv_PtrLcv_mcc_CCheckerGG_clear,
	std_vectorLcv_PtrLcv_mcc_CCheckerGG_get_const_size_t, std_vectorLcv_PtrLcv_mcc_CCheckerGG_set_size_t_const_PtrLCCheckerG,
	std_vectorLcv_PtrLcv_mcc_CCheckerGG_push_const_PtrLCCheckerG, std_vectorLcv_PtrLcv_mcc_CCheckerGG_insert_size_t_const_PtrLCCheckerG,
}
vector_non_copy_or_bool! { core::Ptr<crate::mcc::MCC_CChecker> }

