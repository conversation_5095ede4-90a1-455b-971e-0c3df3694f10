extern "C" {
	const cv::videostab::IMotionStabilizer* cv_PtrLcv_videostab_IMotionStabilizerG_getInnerPtr_const(const cv::Ptr<cv::videostab::IMotionStabilizer>* instance) {
			return instance->get();
	}
	
	cv::videostab::IMotionStabilizer* cv_PtrLcv_videostab_IMotionStabilizerG_getInnerPtrMut(cv::Ptr<cv::videostab::IMotionStabilizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_IMotionStabilizerG_delete(cv::Ptr<cv::videostab::IMotionStabilizer>* instance) {
			delete instance;
	}
	
}

