pub fn cv_PtrLcv_detail_ChannelsCompensatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_ChannelsCompensatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_ChannelsCompensatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_ChannelsCompensatorG_to_PtrOfDetail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_ChannelsCompensatorG_new_const_ChannelsCompensator(val: *mut c_void) -> *mut c_void;
