pub fn cv_PtrLcv_detail_DpSeamFinderG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_DpSeamFinderG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_DpSeamFinderG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_detail_DpSeamFinderG_to_PtrOfDetail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_detail_DpSeamFinderG_new_const_DpSeamFinder(val: *mut c_void) -> *mut c_void;
