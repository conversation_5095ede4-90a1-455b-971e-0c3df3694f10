extern "C" {
	const cv::face::PredictCollector* cv_PtrLcv_face_PredictCollectorG_getInnerPtr_const(const cv::Ptr<cv::face::PredictCollector>* instance) {
			return instance->get();
	}
	
	cv::face::PredictCollector* cv_PtrLcv_face_PredictCollectorG_getInnerPtrMut(cv::Ptr<cv::face::PredictCollector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_PredictCollectorG_delete(cv::Ptr<cv::face::PredictCollector>* instance) {
			delete instance;
	}
	
}

