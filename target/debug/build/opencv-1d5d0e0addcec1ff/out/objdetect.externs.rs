pub fn cv_createFaceDetectionMaskGenerator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_groupRectangles_meanshift_vectorLRectGR_vectorLdoubleGR_vectorLdoubleGR(rect_list: *mut c_void, found_weights: *mut c_void, found_scales: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_groupRectangles_meanshift_vectorLRectGR_vectorLdoubleGR_vectorLdoubleGR_double_Size(rect_list: *mut c_void, found_weights: *mut c_void, found_scales: *mut c_void, detect_threshold: f64, win_det_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_groupRectangles_vectorLRectGR_int(rect_list: *mut c_void, group_threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_groupRectangles_vectorLRectGR_int_double(rect_list: *mut c_void, group_threshold: i32, eps: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_groupRectangles_vectorLRectGR_int_double_vectorLintGX_vectorLdoubleGX(rect_list: *mut c_void, group_threshold: i32, eps: f64, weights: *mut c_void, level_weights: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_groupRectangles_vectorLRectGR_vectorLintGR_int(rect_list: *mut c_void, weights: *mut c_void, group_threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_groupRectangles_vectorLRectGR_vectorLintGR_int_double(rect_list: *mut c_void, weights: *mut c_void, group_threshold: i32, eps: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_groupRectangles_vectorLRectGR_vectorLintGR_vectorLdoubleGR_int(rect_list: *mut c_void, reject_levels: *mut c_void, level_weights: *mut c_void, group_threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_groupRectangles_vectorLRectGR_vectorLintGR_vectorLdoubleGR_int_double(rect_list: *mut c_void, reject_levels: *mut c_void, level_weights: *mut c_void, group_threshold: i32, eps: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BaseCascadeClassifier_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_BaseCascadeClassifier_load_const_StringR(instance: *mut c_void, filename: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_BaseCascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR_double_int_int_Size_Size(instance: *mut c_void, image: *const c_void, objects: *mut c_void, scale_factor: f64, min_neighbors: i32, flags: i32, min_size: *const core::Size, max_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_BaseCascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR_vectorLintGR_double_int_int_Size_Size(instance: *mut c_void, image: *const c_void, objects: *mut c_void, num_detections: *mut c_void, scale_factor: f64, min_neighbors: i32, flags: i32, min_size: *const core::Size, max_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_BaseCascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR_vectorLintGR_vectorLdoubleGR_double_int_int_Size_Size_bool(instance: *mut c_void, image: *const c_void, objects: *mut c_void, reject_levels: *mut c_void, level_weights: *mut c_void, scale_factor: f64, min_neighbors: i32, flags: i32, min_size: *const core::Size, max_size: *const core::Size, output_reject_levels: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_BaseCascadeClassifier_isOldFormatCascade_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_BaseCascadeClassifier_getOriginalWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_BaseCascadeClassifier_getFeatureType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BaseCascadeClassifier_getOldCascade(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BaseCascadeClassifier_setMaskGenerator_const_PtrLMaskGeneratorGR(instance: *mut c_void, mask_generator: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BaseCascadeClassifier_getMaskGenerator(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BaseCascadeClassifier_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_BaseCascadeClassifier_delete(instance: *mut c_void);
pub fn cv_BaseCascadeClassifier_MaskGenerator_generateMask_const_MatR(instance: *mut c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_BaseCascadeClassifier_MaskGenerator_initializeMask_const_MatR(instance: *mut c_void, unnamed: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BaseCascadeClassifier_MaskGenerator_delete(instance: *mut c_void);
pub fn cv_CascadeClassifier_CascadeClassifier(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CascadeClassifier_CascadeClassifier_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CascadeClassifier_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_CascadeClassifier_load_const_StringR(instance: *mut c_void, filename: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_CascadeClassifier_read_const_FileNodeR(instance: *mut c_void, node: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_CascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR_double_int_int_Size_Size(instance: *mut c_void, image: *const c_void, objects: *mut c_void, scale_factor: f64, min_neighbors: i32, flags: i32, min_size: *const core::Size, max_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_CascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR(instance: *mut c_void, image: *const c_void, objects: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR_vectorLintGR_double_int_int_Size_Size(instance: *mut c_void, image: *const c_void, objects: *mut c_void, num_detections: *mut c_void, scale_factor: f64, min_neighbors: i32, flags: i32, min_size: *const core::Size, max_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_CascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR_vectorLintGR(instance: *mut c_void, image: *const c_void, objects: *mut c_void, num_detections: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR_vectorLintGR_vectorLdoubleGR_double_int_int_Size_Size_bool(instance: *mut c_void, image: *const c_void, objects: *mut c_void, reject_levels: *mut c_void, level_weights: *mut c_void, scale_factor: f64, min_neighbors: i32, flags: i32, min_size: *const core::Size, max_size: *const core::Size, output_reject_levels: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_CascadeClassifier_detectMultiScale_const__InputArrayR_vectorLRectGR_vectorLintGR_vectorLdoubleGR(instance: *mut c_void, image: *const c_void, objects: *mut c_void, reject_levels: *mut c_void, level_weights: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CascadeClassifier_isOldFormatCascade_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_CascadeClassifier_getOriginalWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_CascadeClassifier_getFeatureType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_CascadeClassifier_getOldCascade(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CascadeClassifier_convert_const_StringR_const_StringR(oldcascade: *const c_char, newcascade: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_CascadeClassifier_setMaskGenerator_const_PtrLMaskGeneratorGR(instance: *mut c_void, mask_generator: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CascadeClassifier_getMaskGenerator(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CascadeClassifier_propCc(instance: *mut c_void) -> *mut c_void;
pub fn cv_CascadeClassifier_propCc_const_PtrLBaseCascadeClassifierG(instance: *mut c_void, val: *const c_void);
pub fn cv_CascadeClassifier_delete(instance: *mut c_void);
pub fn cv_DetectionBasedTracker_DetectionBasedTracker_PtrLIDetectorG_PtrLIDetectorG_const_ParametersR(main_detector: *mut c_void, tracking_detector: *mut c_void, params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DetectionBasedTracker_run(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_DetectionBasedTracker_stop(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_resetTracking(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_process_const_MatR(instance: *mut c_void, image_gray: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_setParameters_const_ParametersR(instance: *mut c_void, params: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_DetectionBasedTracker_getParameters_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DetectionBasedTracker_getObjects_const_vectorLRectGR(instance: *const c_void, result: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_getObjects_const_vectorLObjectGR(instance: *const c_void, result: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_getObjects_const_vectorLExtObjectGR(instance: *const c_void, result: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_addObject_const_RectR(instance: *mut c_void, location: *const core::Rect, ocvrs_return: *mut Result<i32>);
pub fn cv_DetectionBasedTracker_delete(instance: *mut c_void);
pub fn cv_DetectionBasedTracker_ExtObject_ExtObject_int_Rect_ObjectStatus(_id: i32, _location: *const core::Rect, _status: crate::objdetect::DetectionBasedTracker_ObjectStatus, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DetectionBasedTracker_ExtObject_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_DetectionBasedTracker_ExtObject_propId_const(instance: *const c_void) -> i32;
pub fn cv_DetectionBasedTracker_ExtObject_propId_const_int(instance: *mut c_void, val: i32);
pub fn cv_DetectionBasedTracker_ExtObject_propLocation_const(instance: *const c_void, ocvrs_return: *mut core::Rect);
pub fn cv_DetectionBasedTracker_ExtObject_propLocation_const_Rect(instance: *mut c_void, val: *const core::Rect);
pub fn cv_DetectionBasedTracker_ExtObject_propStatus_const(instance: *const c_void, ocvrs_return: *mut crate::objdetect::DetectionBasedTracker_ObjectStatus);
pub fn cv_DetectionBasedTracker_ExtObject_propStatus_const_ObjectStatus(instance: *mut c_void, val: crate::objdetect::DetectionBasedTracker_ObjectStatus);
pub fn cv_DetectionBasedTracker_ExtObject_delete(instance: *mut c_void);
pub fn cv_DetectionBasedTracker_IDetector_detect_const_MatR_vectorLRectGR(instance: *mut c_void, image: *const c_void, objects: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_IDetector_setMinObjectSize_const_SizeR(instance: *mut c_void, min: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_IDetector_setMaxObjectSize_const_SizeR(instance: *mut c_void, max: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_IDetector_getMinObjectSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_DetectionBasedTracker_IDetector_getMaxObjectSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_DetectionBasedTracker_IDetector_getScaleFactor(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_DetectionBasedTracker_IDetector_setScaleFactor_float(instance: *mut c_void, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_IDetector_getMinNeighbours(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_DetectionBasedTracker_IDetector_setMinNeighbours_int(instance: *mut c_void, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_DetectionBasedTracker_IDetector_delete(instance: *mut c_void);
pub fn cv_DetectionBasedTracker_Parameters_Parameters(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DetectionBasedTracker_Parameters_propMaxTrackLifetime_const(instance: *const c_void) -> i32;
pub fn cv_DetectionBasedTracker_Parameters_propMaxTrackLifetime_const_int(instance: *mut c_void, val: i32);
pub fn cv_DetectionBasedTracker_Parameters_propMinDetectionPeriod_const(instance: *const c_void) -> i32;
pub fn cv_DetectionBasedTracker_Parameters_propMinDetectionPeriod_const_int(instance: *mut c_void, val: i32);
pub fn cv_DetectionBasedTracker_Parameters_delete(instance: *mut c_void);
pub fn cv_DetectionROI_propScale_const(instance: *const c_void) -> f64;
pub fn cv_DetectionROI_propScale_const_double(instance: *mut c_void, val: f64);
pub fn cv_DetectionROI_propLocations_const(instance: *const c_void) -> *mut c_void;
pub fn cv_DetectionROI_propLocations_const_vectorLPointG(instance: *mut c_void, val: *const c_void);
pub fn cv_DetectionROI_propConfidences_const(instance: *const c_void) -> *mut c_void;
pub fn cv_DetectionROI_propConfidences_const_vectorLdoubleG(instance: *mut c_void, val: *const c_void);
pub fn cv_DetectionROI_delete(instance: *mut c_void);
pub fn cv_FaceDetectorYN_setInputSize_const_SizeR(instance: *mut c_void, input_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_FaceDetectorYN_getInputSize(instance: *mut c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_FaceDetectorYN_setScoreThreshold_float(instance: *mut c_void, score_threshold: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_FaceDetectorYN_getScoreThreshold(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_FaceDetectorYN_setNMSThreshold_float(instance: *mut c_void, nms_threshold: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_FaceDetectorYN_getNMSThreshold(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_FaceDetectorYN_setTopK_int(instance: *mut c_void, top_k: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FaceDetectorYN_getTopK(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FaceDetectorYN_detect_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, faces: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FaceDetectorYN_create_const_StringR_const_StringR_const_SizeR_float_float_int_int_int(model: *const c_char, config: *const c_char, input_size: *const core::Size, score_threshold: f32, nms_threshold: f32, top_k: i32, backend_id: i32, target_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FaceDetectorYN_create_const_StringR_const_StringR_const_SizeR(model: *const c_char, config: *const c_char, input_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FaceDetectorYN_delete(instance: *mut c_void);
pub fn cv_FaceRecognizerSF_alignCrop_const_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *const c_void, src_img: *const c_void, face_box: *const c_void, aligned_img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FaceRecognizerSF_feature_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, aligned_img: *const c_void, face_feature: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_FaceRecognizerSF_match_const_const__InputArrayR_const__InputArrayR_int(instance: *const c_void, _face_feature1: *const c_void, _face_feature2: *const c_void, dis_type: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_FaceRecognizerSF_match_const_const__InputArrayR_const__InputArrayR(instance: *const c_void, _face_feature1: *const c_void, _face_feature2: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_FaceRecognizerSF_create_const_StringR_const_StringR_int_int(model: *const c_char, config: *const c_char, backend_id: i32, target_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FaceRecognizerSF_create_const_StringR_const_StringR(model: *const c_char, config: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FaceRecognizerSF_delete(instance: *mut c_void);
pub fn cv_HOGDescriptor_HOGDescriptor(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_HOGDescriptor_HOGDescriptor_Size_Size_Size_Size_int_int_double_HistogramNormType_double_bool_int_bool(_win_size: *const core::Size, _block_size: *const core::Size, _block_stride: *const core::Size, _cell_size: *const core::Size, _nbins: i32, _deriv_aperture: i32, _win_sigma: f64, _histogram_norm_type: crate::objdetect::HOGDescriptor_HistogramNormType, _l2_hys_threshold: f64, _gamma_correction: bool, _nlevels: i32, _signed_gradient: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_HOGDescriptor_HOGDescriptor_Size_Size_Size_Size_int(_win_size: *const core::Size, _block_size: *const core::Size, _block_stride: *const core::Size, _cell_size: *const core::Size, _nbins: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_HOGDescriptor_HOGDescriptor_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_HOGDescriptor_HOGDescriptor_const_HOGDescriptorR(d: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_HOGDescriptor_getDescriptorSize_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_HOGDescriptor_checkDetectorSize_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_HOGDescriptor_getWinSigma_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_HOGDescriptor_setSVMDetector_const__InputArrayR(instance: *mut c_void, svmdetector: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_read_FileNodeR(instance: *mut c_void, fn_: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_HOGDescriptor_write_const_FileStorageR_const_StringR(instance: *const c_void, fs: *mut c_void, objname: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_load_const_StringR_const_StringR(instance: *mut c_void, filename: *const c_char, objname: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_HOGDescriptor_load_const_StringR(instance: *mut c_void, filename: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_HOGDescriptor_save_const_const_StringR_const_StringR(instance: *const c_void, filename: *const c_char, objname: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_save_const_const_StringR(instance: *const c_void, filename: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_copyTo_const_HOGDescriptorR(instance: *const c_void, c: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_compute_const_const__InputArrayR_vectorLfloatGR_Size_Size_const_vectorLPointGR(instance: *const c_void, img: *const c_void, descriptors: *mut c_void, win_stride: *const core::Size, padding: *const core::Size, locations: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_compute_const_const__InputArrayR_vectorLfloatGR(instance: *const c_void, img: *const c_void, descriptors: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detect_const_const__InputArrayR_vectorLPointGR_vectorLdoubleGR_double_Size_Size_const_vectorLPointGR(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, weights: *mut c_void, hit_threshold: f64, win_stride: *const core::Size, padding: *const core::Size, search_locations: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detect_const_const__InputArrayR_vectorLPointGR_vectorLdoubleGR(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, weights: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detect_const_const__InputArrayR_vectorLPointGR_double_Size_Size_const_vectorLPointGR(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, hit_threshold: f64, win_stride: *const core::Size, padding: *const core::Size, search_locations: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detect_const_const__InputArrayR_vectorLPointGR(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detectMultiScale_const_const__InputArrayR_vectorLRectGR_vectorLdoubleGR_double_Size_Size_double_double_bool(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, found_weights: *mut c_void, hit_threshold: f64, win_stride: *const core::Size, padding: *const core::Size, scale: f64, final_threshold: f64, use_meanshift_grouping: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detectMultiScale_const_const__InputArrayR_vectorLRectGR_vectorLdoubleGR(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, found_weights: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detectMultiScale_const_const__InputArrayR_vectorLRectGR_double_Size_Size_double_double_bool(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, hit_threshold: f64, win_stride: *const core::Size, padding: *const core::Size, scale: f64, final_threshold: f64, use_meanshift_grouping: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detectMultiScale_const_const__InputArrayR_vectorLRectGR(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_computeGradient_const_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_Size_Size(instance: *const c_void, img: *const c_void, grad: *const c_void, angle_ofs: *const c_void, padding_tl: *const core::Size, padding_br: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_computeGradient_const_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(instance: *const c_void, img: *const c_void, grad: *const c_void, angle_ofs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_getDefaultPeopleDetector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_HOGDescriptor_getDaimlerPeopleDetector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_HOGDescriptor_detectROI_const_const__InputArrayR_const_vectorLPointGR_vectorLPointGR_vectorLdoubleGR_double_Size_Size(instance: *const c_void, img: *const c_void, locations: *const c_void, found_locations: *mut c_void, confidences: *mut c_void, hit_threshold: f64, win_stride: *const core::Size, padding: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detectROI_const_const__InputArrayR_const_vectorLPointGR_vectorLPointGR_vectorLdoubleGR(instance: *const c_void, img: *const c_void, locations: *const c_void, found_locations: *mut c_void, confidences: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detectMultiScaleROI_const_const__InputArrayR_vectorLRectGR_vectorLDetectionROIGR_double_int(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, locations: *mut c_void, hit_threshold: f64, group_threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_detectMultiScaleROI_const_const__InputArrayR_vectorLRectGR_vectorLDetectionROIGR(instance: *const c_void, img: *const c_void, found_locations: *mut c_void, locations: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_groupRectangles_const_vectorLRectGR_vectorLdoubleGR_int_double(instance: *const c_void, rect_list: *mut c_void, weights: *mut c_void, group_threshold: i32, eps: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_HOGDescriptor_propWinSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_HOGDescriptor_propWinSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_HOGDescriptor_propBlockSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_HOGDescriptor_propBlockSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_HOGDescriptor_propBlockStride_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_HOGDescriptor_propBlockStride_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_HOGDescriptor_propCellSize_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_HOGDescriptor_propCellSize_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_HOGDescriptor_propNbins_const(instance: *const c_void) -> i32;
pub fn cv_HOGDescriptor_propNbins_const_int(instance: *mut c_void, val: i32);
pub fn cv_HOGDescriptor_propDerivAperture_const(instance: *const c_void) -> i32;
pub fn cv_HOGDescriptor_propDerivAperture_const_int(instance: *mut c_void, val: i32);
pub fn cv_HOGDescriptor_propWinSigma_const(instance: *const c_void) -> f64;
pub fn cv_HOGDescriptor_propWinSigma_const_double(instance: *mut c_void, val: f64);
pub fn cv_HOGDescriptor_propHistogramNormType_const(instance: *const c_void, ocvrs_return: *mut crate::objdetect::HOGDescriptor_HistogramNormType);
pub fn cv_HOGDescriptor_propHistogramNormType_const_HistogramNormType(instance: *mut c_void, val: crate::objdetect::HOGDescriptor_HistogramNormType);
pub fn cv_HOGDescriptor_propL2HysThreshold_const(instance: *const c_void) -> f64;
pub fn cv_HOGDescriptor_propL2HysThreshold_const_double(instance: *mut c_void, val: f64);
pub fn cv_HOGDescriptor_propGammaCorrection_const(instance: *const c_void) -> bool;
pub fn cv_HOGDescriptor_propGammaCorrection_const_bool(instance: *mut c_void, val: bool);
pub fn cv_HOGDescriptor_propSvmDetector_const(instance: *const c_void) -> *mut c_void;
pub fn cv_HOGDescriptor_propSvmDetector_const_vectorLfloatG(instance: *mut c_void, val: *const c_void);
pub fn cv_HOGDescriptor_propOclSvmDetector_const(instance: *const c_void) -> *mut c_void;
pub fn cv_HOGDescriptor_propOclSvmDetector_const_UMat(instance: *mut c_void, val: *const c_void);
pub fn cv_HOGDescriptor_propFree_coef_const(instance: *const c_void) -> f32;
pub fn cv_HOGDescriptor_propFree_coef_const_float(instance: *mut c_void, val: f32);
pub fn cv_HOGDescriptor_propNlevels_const(instance: *const c_void) -> i32;
pub fn cv_HOGDescriptor_propNlevels_const_int(instance: *mut c_void, val: i32);
pub fn cv_HOGDescriptor_propSignedGradient_const(instance: *const c_void) -> bool;
pub fn cv_HOGDescriptor_propSignedGradient_const_bool(instance: *mut c_void, val: bool);
pub fn cv_HOGDescriptor_delete(instance: *mut c_void);
pub fn cv_QRCodeDetector_QRCodeDetector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_setEpsX_double(instance: *mut c_void, eps_x: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_QRCodeDetector_setEpsY_double(instance: *mut c_void, eps_y: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_QRCodeDetector_detect_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, img: *const c_void, points: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_QRCodeDetector_decode_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, img: *const c_void, points: *const c_void, straight_qrcode: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_decode_const__InputArrayR_const__InputArrayR(instance: *mut c_void, img: *const c_void, points: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_decodeCurved_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, img: *const c_void, points: *const c_void, straight_qrcode: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_decodeCurved_const__InputArrayR_const__InputArrayR(instance: *mut c_void, img: *const c_void, points: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_detectAndDecode_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, img: *const c_void, points: *const c_void, straight_qrcode: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_detectAndDecode_const__InputArrayR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_detectAndDecodeCurved_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, img: *const c_void, points: *const c_void, straight_qrcode: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_detectAndDecodeCurved_const__InputArrayR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_QRCodeDetector_detectMulti_const_const__InputArrayR_const__OutputArrayR(instance: *const c_void, img: *const c_void, points: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_QRCodeDetector_decodeMulti_const_const__InputArrayR_const__InputArrayR_vectorLstringGR_const__OutputArrayR(instance: *const c_void, img: *const c_void, points: *const c_void, decoded_info: *mut c_void, straight_qrcode: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_QRCodeDetector_decodeMulti_const_const__InputArrayR_const__InputArrayR_vectorLstringGR(instance: *const c_void, img: *const c_void, points: *const c_void, decoded_info: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_QRCodeDetector_delete(instance: *mut c_void);
pub fn cv_SimilarRects_SimilarRects_double(_eps: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SimilarRects_operator___const_const_RectR_const_RectR(instance: *const c_void, r1: *const core::Rect, r2: *const core::Rect, ocvrs_return: *mut Result<bool>);
pub fn cv_SimilarRects_propEps_const(instance: *const c_void) -> f64;
pub fn cv_SimilarRects_propEps_const_double(instance: *mut c_void, val: f64);
pub fn cv_SimilarRects_delete(instance: *mut c_void);
