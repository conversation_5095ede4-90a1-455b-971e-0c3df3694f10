pub fn cv_PtrLcv_videostab_MotionEstimatorL1G_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionEstimatorL1G_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionEstimatorL1G_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_MotionEstimatorL1G_to_PtrOfMotionEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionEstimatorL1G_new_const_MotionEstimatorL1(val: *mut c_void) -> *mut c_void;
