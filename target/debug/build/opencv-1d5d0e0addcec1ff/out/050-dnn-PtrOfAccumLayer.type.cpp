extern "C" {
	const cv::dnn::AccumLayer* cv_PtrLcv_dnn_AccumLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::AccumLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::AccumLayer* cv_PtrLcv_dnn_AccumLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::AccumLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_AccumLayerG_delete(cv::Ptr<cv::dnn::AccumLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_AccumLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::AccumLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_AccumLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::AccumLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::AccumLayer>* cv_PtrLcv_dnn_AccumLayerG_new_const_AccumLayer(cv::dnn::AccumLayer* val) {
			return new cv::Ptr<cv::dnn::AccumLayer>(val);
	}
	
}

