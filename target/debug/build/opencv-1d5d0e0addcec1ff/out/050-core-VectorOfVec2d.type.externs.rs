pub fn std_vectorLcv_Vec2dG_new_const() -> *mut c_void;
pub fn std_vectorLcv_Vec2dG_delete(instance: *mut c_void);
pub fn std_vectorLcv_Vec2dG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec2dG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_Vec2dG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_Vec2dG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_Vec2dG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_Vec2dG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_Vec2dG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_Vec2dG_clear(instance: *mut c_void);
pub fn std_vectorLcv_Vec2dG_push_const_Vec2d(instance: *mut c_void, val: *const core::Vec2d);
pub fn std_vectorLcv_Vec2dG_insert_size_t_const_Vec2d(instance: *mut c_void, index: size_t, val: *const core::Vec2d);
pub fn std_vectorLcv_Vec2dG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Vec2d);
pub fn std_vectorLcv_Vec2dG_set_size_t_const_Vec2d(instance: *mut c_void, index: size_t, val: *const core::Vec2d);
pub fn std_vectorLcv_Vec2dG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_Vec2dG_data_const(instance: *const c_void) -> *const core::Vec2d;
pub fn std_vectorLcv_Vec2dG_dataMut(instance: *mut c_void) -> *mut core::Vec2d;
pub fn cv_fromSlice_const_const_Vec2dX_size_t(data: *const core::Vec2d, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_Vec2dG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec2dG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_Vec2dG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
