pub fn cv_PtrLcv_superres_FarnebackOpticalFlowG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_superres_FarnebackOpticalFlowG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_superres_FarnebackOpticalFlowG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_superres_FarnebackOpticalFlowG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_superres_FarnebackOpticalFlowG_to_PtrOfSuperRes_DenseOpticalFlowExt(instance: *mut c_void) -> *mut c_void;
