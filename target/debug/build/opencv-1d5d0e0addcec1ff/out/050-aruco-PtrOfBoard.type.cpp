extern "C" {
	const cv::aruco::Board* cv_PtrLcv_aruco_BoardG_getInnerPtr_const(const cv::Ptr<cv::aruco::Board>* instance) {
			return instance->get();
	}
	
	cv::aruco::Board* cv_PtrLcv_aruco_BoardG_getInnerPtrMut(cv::Ptr<cv::aruco::Board>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_BoardG_delete(cv::Ptr<cv::aruco::Board>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::Board>* cv_PtrLcv_aruco_BoardG_new_const_Board(cv::aruco::Board* val) {
			return new cv::Ptr<cv::aruco::Board>(val);
	}
	
}

