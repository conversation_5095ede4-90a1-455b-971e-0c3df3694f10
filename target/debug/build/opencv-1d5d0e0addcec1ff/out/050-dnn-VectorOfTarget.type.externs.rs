pub fn std_vectorLcv_dnn_TargetG_new_const() -> *mut c_void;
pub fn std_vectorLcv_dnn_TargetG_delete(instance: *mut c_void);
pub fn std_vectorLcv_dnn_TargetG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_dnn_TargetG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_dnn_TargetG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_dnn_TargetG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_dnn_TargetG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_dnn_TargetG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_dnn_TargetG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_dnn_TargetG_clear(instance: *mut c_void);
pub fn std_vectorLcv_dnn_TargetG_push_const_Target(instance: *mut c_void, val: crate::dnn::Target);
pub fn std_vectorLcv_dnn_TargetG_insert_size_t_const_Target(instance: *mut c_void, index: size_t, val: crate::dnn::Target);
pub fn std_vectorLcv_dnn_TargetG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut crate::dnn::Target);
pub fn std_vectorLcv_dnn_TargetG_set_size_t_const_Target(instance: *mut c_void, index: size_t, val: crate::dnn::Target);
pub fn std_vectorLcv_dnn_TargetG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_dnn_TargetG_data_const(instance: *const c_void) -> *const crate::dnn::Target;
pub fn std_vectorLcv_dnn_TargetG_dataMut(instance: *mut c_void) -> *mut crate::dnn::Target;
pub fn cv_fromSlice_const_const_TargetX_size_t(data: *const crate::dnn::Target, len: size_t) -> *mut c_void;
