pub fn cv_ft_FT02D_FL_process_const__InputArrayR_const_int_const__OutputArrayR(matrix: *const c_void, radius: i32, output: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT02D_FL_process_float_const__InputArrayR_const_int_const__OutputArrayR(matrix: *const c_void, radius: i32, output: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT02D_components_const__InputArrayR_const__InputArrayR_const__OutputArrayR(matrix: *const c_void, kernel: *const c_void, components: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT02D_components_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR(matrix: *const c_void, kernel: *const c_void, components: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT02D_inverseFT_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_int(components: *const c_void, kernel: *const c_void, output: *const c_void, width: i32, height: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT02D_iteration_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__OutputArrayR_bool(matrix: *const c_void, kernel: *const c_void, output: *const c_void, mask: *const c_void, mask_output: *const c_void, first_stop: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_ft_FT02D_process_const__InputArrayR_const__InputArrayR_const__OutputArrayR(matrix: *const c_void, kernel: *const c_void, output: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT02D_process_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR(matrix: *const c_void, kernel: *const c_void, output: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT12D_components_const__InputArrayR_const__InputArrayR_const__OutputArrayR(matrix: *const c_void, kernel: *const c_void, components: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT12D_createPolynomMatrixHorizontal_int_const__OutputArrayR_const_int(radius: i32, matrix: *const c_void, chn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT12D_createPolynomMatrixVertical_int_const__OutputArrayR_const_int(radius: i32, matrix: *const c_void, chn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT12D_inverseFT_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_int(components: *const c_void, kernel: *const c_void, output: *const c_void, width: i32, height: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT12D_polynomial_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(matrix: *const c_void, kernel: *const c_void, c00: *const c_void, c10: *const c_void, c01: *const c_void, components: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT12D_polynomial_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__InputArrayR(matrix: *const c_void, kernel: *const c_void, c00: *const c_void, c10: *const c_void, c01: *const c_void, components: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT12D_process_const__InputArrayR_const__InputArrayR_const__OutputArrayR(matrix: *const c_void, kernel: *const c_void, output: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_FT12D_process_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR(matrix: *const c_void, kernel: *const c_void, output: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_createKernel_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const_int(a: *const c_void, b: *const c_void, kernel: *const c_void, chn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_createKernel_int_int_const__OutputArrayR_const_int(function: i32, radius: i32, kernel: *const c_void, chn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_filter_const__InputArrayR_const__InputArrayR_const__OutputArrayR(image: *const c_void, kernel: *const c_void, output: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ft_inpaint_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_int_int(image: *const c_void, mask: *const c_void, output: *const c_void, radius: i32, function: i32, algorithm: i32, ocvrs_return: *mut ResultVoid);
