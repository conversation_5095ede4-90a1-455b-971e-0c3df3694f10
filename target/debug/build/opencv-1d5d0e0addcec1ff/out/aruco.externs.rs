pub fn cv_aruco_calibrateCameraAruco_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_PtrLBoardGR_Size_const__InputOutputArrayR_const__InputOutputArrayR(corners: *const c_void, ids: *const c_void, counter: *const c_void, board: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_aruco_calibrateCameraAruco_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_PtrLBoardGR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(corners: *const c_void, ids: *const c_void, counter: *const c_void, board: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, std_deviations_intrinsics: *const c_void, std_deviations_extrinsics: *const c_void, per_view_errors: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_aruco_calibrateCameraAruco_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_PtrLBoardGR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(corners: *const c_void, ids: *const c_void, counter: *const c_void, board: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, std_deviations_intrinsics: *const c_void, std_deviations_extrinsics: *const c_void, per_view_errors: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_aruco_calibrateCameraAruco_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_PtrLBoardGR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(corners: *const c_void, ids: *const c_void, counter: *const c_void, board: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_aruco_calibrateCameraCharuco_const__InputArrayR_const__InputArrayR_const_PtrLCharucoBoardGR_Size_const__InputOutputArrayR_const__InputOutputArrayR(charuco_corners: *const c_void, charuco_ids: *const c_void, board: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_aruco_calibrateCameraCharuco_const__InputArrayR_const__InputArrayR_const_PtrLCharucoBoardGR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(charuco_corners: *const c_void, charuco_ids: *const c_void, board: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, std_deviations_intrinsics: *const c_void, std_deviations_extrinsics: *const c_void, per_view_errors: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_aruco_calibrateCameraCharuco_const__InputArrayR_const__InputArrayR_const_PtrLCharucoBoardGR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(charuco_corners: *const c_void, charuco_ids: *const c_void, board: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, std_deviations_intrinsics: *const c_void, std_deviations_extrinsics: *const c_void, per_view_errors: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_aruco_calibrateCameraCharuco_const__InputArrayR_const__InputArrayR_const_PtrLCharucoBoardGR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(charuco_corners: *const c_void, charuco_ids: *const c_void, board: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_aruco_detectCharucoDiamond_const__InputArrayR_const__InputArrayR_const__InputArrayR_float_const__OutputArrayR_const__OutputArrayR(image: *const c_void, marker_corners: *const c_void, marker_ids: *const c_void, square_marker_length_rate: f32, diamond_corners: *const c_void, diamond_ids: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_detectCharucoDiamond_const__InputArrayR_const__InputArrayR_const__InputArrayR_float_const__OutputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(image: *const c_void, marker_corners: *const c_void, marker_ids: *const c_void, square_marker_length_rate: f32, diamond_corners: *const c_void, diamond_ids: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_detectMarkers_const__InputArrayR_const_PtrLDictionaryGR_const__OutputArrayR_const__OutputArrayR(image: *const c_void, dictionary: *const c_void, corners: *const c_void, ids: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_detectMarkers_const__InputArrayR_const_PtrLDictionaryGR_const__OutputArrayR_const__OutputArrayR_const_PtrLDetectorParametersGR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(image: *const c_void, dictionary: *const c_void, corners: *const c_void, ids: *const c_void, parameters: *const c_void, rejected_img_points: *const c_void, camera_matrix: *const c_void, dist_coeff: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawAxis_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_float(image: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, length: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawCharucoDiamond_const_PtrLDictionaryGR_Vec4i_int_int_const__OutputArrayR(dictionary: *const c_void, ids: *const core::Vec4i, square_length: i32, marker_length: i32, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawCharucoDiamond_const_PtrLDictionaryGR_Vec4i_int_int_const__OutputArrayR_int_int(dictionary: *const c_void, ids: *const core::Vec4i, square_length: i32, marker_length: i32, img: *const c_void, margin_size: i32, border_bits: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawDetectedCornersCharuco_const__InputOutputArrayR_const__InputArrayR(image: *const c_void, charuco_corners: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawDetectedCornersCharuco_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_Scalar(image: *const c_void, charuco_corners: *const c_void, charuco_ids: *const c_void, corner_color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawDetectedDiamonds_const__InputOutputArrayR_const__InputArrayR(image: *const c_void, diamond_corners: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawDetectedDiamonds_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_Scalar(image: *const c_void, diamond_corners: *const c_void, diamond_ids: *const c_void, border_color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawDetectedMarkers_const__InputOutputArrayR_const__InputArrayR(image: *const c_void, corners: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawDetectedMarkers_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_Scalar(image: *const c_void, corners: *const c_void, ids: *const c_void, border_color: *const core::Scalar, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawMarker_const_PtrLDictionaryGR_int_int_const__OutputArrayR(dictionary: *const c_void, id: i32, side_pixels: i32, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawMarker_const_PtrLDictionaryGR_int_int_const__OutputArrayR_int(dictionary: *const c_void, id: i32, side_pixels: i32, img: *const c_void, border_bits: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawPlanarBoard_const_PtrLBoardGR_Size_const__OutputArrayR(board: *const c_void, out_size: *const core::Size, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_drawPlanarBoard_const_PtrLBoardGR_Size_const__OutputArrayR_int_int(board: *const c_void, out_size: *const core::Size, img: *const c_void, margin_size: i32, border_bits: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_estimatePoseBoard_const__InputArrayR_const__InputArrayR_const_PtrLBoardGR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(corners: *const c_void, ids: *const c_void, board: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_aruco_estimatePoseBoard_const__InputArrayR_const__InputArrayR_const_PtrLBoardGR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_bool(corners: *const c_void, ids: *const c_void, board: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, use_extrinsic_guess: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_aruco_estimatePoseCharucoBoard_const__InputArrayR_const__InputArrayR_const_PtrLCharucoBoardGR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(charuco_corners: *const c_void, charuco_ids: *const c_void, board: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_aruco_estimatePoseCharucoBoard_const__InputArrayR_const__InputArrayR_const_PtrLCharucoBoardGR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_bool(charuco_corners: *const c_void, charuco_ids: *const c_void, board: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, use_extrinsic_guess: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_aruco_estimatePoseSingleMarkers_const__InputArrayR_float_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(corners: *const c_void, marker_length: f32, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_estimatePoseSingleMarkers_const__InputArrayR_float_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(corners: *const c_void, marker_length: f32, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, _obj_points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_generateCustomDictionary_int_int(n_markers: i32, marker_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_generateCustomDictionary_int_int_const_PtrLDictionaryGR(n_markers: i32, marker_size: i32, base_dictionary: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_generateCustomDictionary_int_int_const_PtrLDictionaryGR_int(n_markers: i32, marker_size: i32, base_dictionary: *const c_void, random_seed: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_generateCustomDictionary_int_int_int(n_markers: i32, marker_size: i32, random_seed: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_getBoardObjectAndImagePoints_const_PtrLBoardGR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(board: *const c_void, detected_corners: *const c_void, detected_ids: *const c_void, obj_points: *const c_void, img_points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_getPredefinedDictionary_PREDEFINED_DICTIONARY_NAME(name: crate::aruco::PREDEFINED_DICTIONARY_NAME, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_getPredefinedDictionary_int(dict: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_interpolateCornersCharuco_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_PtrLCharucoBoardGR_const__OutputArrayR_const__OutputArrayR(marker_corners: *const c_void, marker_ids: *const c_void, image: *const c_void, board: *const c_void, charuco_corners: *const c_void, charuco_ids: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_aruco_interpolateCornersCharuco_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_PtrLCharucoBoardGR_const__OutputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_int(marker_corners: *const c_void, marker_ids: *const c_void, image: *const c_void, board: *const c_void, charuco_corners: *const c_void, charuco_ids: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, min_markers: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_aruco_refineDetectedMarkers_const__InputArrayR_const_PtrLBoardGR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(image: *const c_void, board: *const c_void, detected_corners: *const c_void, detected_ids: *const c_void, rejected_corners: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_refineDetectedMarkers_const__InputArrayR_const_PtrLBoardGR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_float_float_bool_const__OutputArrayR_const_PtrLDetectorParametersGR(image: *const c_void, board: *const c_void, detected_corners: *const c_void, detected_ids: *const c_void, rejected_corners: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, min_rep_distance: f32, error_correction_rate: f32, check_all_orders: bool, recovered_idxs: *const c_void, parameters: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_testCharucoCornersCollinear_const_PtrLCharucoBoardGR_const__InputArrayR(_board: *const c_void, _charuco_ids: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_aruco_Board_create_const__InputArrayR_const_PtrLDictionaryGR_const__InputArrayR(obj_points: *const c_void, dictionary: *const c_void, ids: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Board_setIds_const__InputArrayR(instance: *mut c_void, ids: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_Board_propObjPoints_const(instance: *const c_void) -> *mut c_void;
pub fn cv_aruco_Board_propObjPoints_const_vectorLvectorLPoint3fGG(instance: *mut c_void, val: *const c_void);
pub fn cv_aruco_Board_propDictionary(instance: *mut c_void) -> *mut c_void;
pub fn cv_aruco_Board_propDictionary_const_PtrLDictionaryG(instance: *mut c_void, val: *const c_void);
pub fn cv_aruco_Board_propIds_const(instance: *const c_void) -> *mut c_void;
pub fn cv_aruco_Board_propIds_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_aruco_Board_delete(instance: *mut c_void);
pub fn cv_aruco_CharucoBoard_draw_Size_const__OutputArrayR_int_int(instance: *mut c_void, out_size: *const core::Size, img: *const c_void, margin_size: i32, border_bits: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_CharucoBoard_draw_Size_const__OutputArrayR(instance: *mut c_void, out_size: *const core::Size, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_CharucoBoard_create_int_int_float_float_const_PtrLDictionaryGR(squares_x: i32, squares_y: i32, square_length: f32, marker_length: f32, dictionary: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_CharucoBoard_getChessboardSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_aruco_CharucoBoard_getSquareLength_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_aruco_CharucoBoard_getMarkerLength_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_aruco_CharucoBoard_propChessboardCorners_const(instance: *const c_void) -> *mut c_void;
pub fn cv_aruco_CharucoBoard_propChessboardCorners_const_vectorLPoint3fG(instance: *mut c_void, val: *const c_void);
pub fn cv_aruco_CharucoBoard_propNearestMarkerIdx_const(instance: *const c_void) -> *mut c_void;
pub fn cv_aruco_CharucoBoard_propNearestMarkerIdx_const_vectorLvectorLintGG(instance: *mut c_void, val: *const c_void);
pub fn cv_aruco_CharucoBoard_propNearestMarkerCorners_const(instance: *const c_void) -> *mut c_void;
pub fn cv_aruco_CharucoBoard_propNearestMarkerCorners_const_vectorLvectorLintGG(instance: *mut c_void, val: *const c_void);
pub fn cv_aruco_CharucoBoard_to_Board(instance: *mut c_void) -> *mut c_void;
pub fn cv_aruco_CharucoBoard_delete(instance: *mut c_void);
pub fn cv_aruco_DetectorParameters_DetectorParameters(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_DetectorParameters_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_DetectorParameters_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_aruco_DetectorParameters_propAdaptiveThreshWinSizeMin_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propAdaptiveThreshWinSizeMin_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propAdaptiveThreshWinSizeMax_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propAdaptiveThreshWinSizeMax_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propAdaptiveThreshWinSizeStep_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propAdaptiveThreshWinSizeStep_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propAdaptiveThreshConstant_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propAdaptiveThreshConstant_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propMinMarkerPerimeterRate_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propMinMarkerPerimeterRate_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propMaxMarkerPerimeterRate_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propMaxMarkerPerimeterRate_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propPolygonalApproxAccuracyRate_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propPolygonalApproxAccuracyRate_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propMinCornerDistanceRate_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propMinCornerDistanceRate_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propMinDistanceToBorder_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propMinDistanceToBorder_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propMinMarkerDistanceRate_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propMinMarkerDistanceRate_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propCornerRefinementMethod_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propCornerRefinementMethod_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propCornerRefinementWinSize_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propCornerRefinementWinSize_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propCornerRefinementMaxIterations_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propCornerRefinementMaxIterations_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propCornerRefinementMinAccuracy_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propCornerRefinementMinAccuracy_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propMarkerBorderBits_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propMarkerBorderBits_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propPerspectiveRemovePixelPerCell_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propPerspectiveRemovePixelPerCell_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propPerspectiveRemoveIgnoredMarginPerCell_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propPerspectiveRemoveIgnoredMarginPerCell_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propMaxErroneousBitsInBorderRate_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propMaxErroneousBitsInBorderRate_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propMinOtsuStdDev_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propMinOtsuStdDev_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propErrorCorrectionRate_const(instance: *const c_void) -> f64;
pub fn cv_aruco_DetectorParameters_propErrorCorrectionRate_const_double(instance: *mut c_void, val: f64);
pub fn cv_aruco_DetectorParameters_propAprilTagQuadDecimate_const(instance: *const c_void) -> f32;
pub fn cv_aruco_DetectorParameters_propAprilTagQuadDecimate_const_float(instance: *mut c_void, val: f32);
pub fn cv_aruco_DetectorParameters_propAprilTagQuadSigma_const(instance: *const c_void) -> f32;
pub fn cv_aruco_DetectorParameters_propAprilTagQuadSigma_const_float(instance: *mut c_void, val: f32);
pub fn cv_aruco_DetectorParameters_propAprilTagMinClusterPixels_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propAprilTagMinClusterPixels_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propAprilTagMaxNmaxima_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propAprilTagMaxNmaxima_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propAprilTagCriticalRad_const(instance: *const c_void) -> f32;
pub fn cv_aruco_DetectorParameters_propAprilTagCriticalRad_const_float(instance: *mut c_void, val: f32);
pub fn cv_aruco_DetectorParameters_propAprilTagMaxLineFitMse_const(instance: *const c_void) -> f32;
pub fn cv_aruco_DetectorParameters_propAprilTagMaxLineFitMse_const_float(instance: *mut c_void, val: f32);
pub fn cv_aruco_DetectorParameters_propAprilTagMinWhiteBlackDiff_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propAprilTagMinWhiteBlackDiff_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propAprilTagDeglitch_const(instance: *const c_void) -> i32;
pub fn cv_aruco_DetectorParameters_propAprilTagDeglitch_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_DetectorParameters_propDetectInvertedMarker_const(instance: *const c_void) -> bool;
pub fn cv_aruco_DetectorParameters_propDetectInvertedMarker_const_bool(instance: *mut c_void, val: bool);
pub fn cv_aruco_DetectorParameters_delete(instance: *mut c_void);
pub fn cv_aruco_Dictionary_Dictionary_const_MatR_int_int(_bytes_list: *const c_void, _marker_size: i32, _maxcorr: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_Dictionary(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_Dictionary_const_PtrLDictionaryGR(_dictionary: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_create_int_int_int(n_markers: i32, marker_size: i32, random_seed: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_create_int_int(n_markers: i32, marker_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_create_int_int_const_PtrLDictionaryGR_int(n_markers: i32, marker_size: i32, base_dictionary: *const c_void, random_seed: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_create_int_int_const_PtrLDictionaryGR(n_markers: i32, marker_size: i32, base_dictionary: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_get_int(dict: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_identify_const_const_MatR_intR_intR_double(instance: *const c_void, only_bits: *const c_void, idx: *mut i32, rotation: *mut i32, max_correction_rate: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_aruco_Dictionary_getDistanceToId_const_const__InputArrayR_int_bool(instance: *const c_void, bits: *const c_void, id: i32, all_rotations: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_aruco_Dictionary_getDistanceToId_const_const__InputArrayR_int(instance: *const c_void, bits: *const c_void, id: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_aruco_Dictionary_drawMarker_const_int_int_const__OutputArrayR_int(instance: *const c_void, id: i32, side_pixels: i32, _img: *const c_void, border_bits: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_Dictionary_drawMarker_const_int_int_const__OutputArrayR(instance: *const c_void, id: i32, side_pixels: i32, _img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_Dictionary_getByteListFromBits_const_MatR(bits: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_getBitsFromByteList_const_MatR_int(byte_list: *const c_void, marker_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_Dictionary_propBytesList_const(instance: *const c_void) -> *mut c_void;
pub fn cv_aruco_Dictionary_propBytesList_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_aruco_Dictionary_propMarkerSize_const(instance: *const c_void) -> i32;
pub fn cv_aruco_Dictionary_propMarkerSize_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_Dictionary_propMaxCorrectionBits_const(instance: *const c_void) -> i32;
pub fn cv_aruco_Dictionary_propMaxCorrectionBits_const_int(instance: *mut c_void, val: i32);
pub fn cv_aruco_Dictionary_delete(instance: *mut c_void);
pub fn cv_aruco_GridBoard_draw_Size_const__OutputArrayR_int_int(instance: *mut c_void, out_size: *const core::Size, img: *const c_void, margin_size: i32, border_bits: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_GridBoard_draw_Size_const__OutputArrayR(instance: *mut c_void, out_size: *const core::Size, img: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_aruco_GridBoard_create_int_int_float_float_const_PtrLDictionaryGR_int(markers_x: i32, markers_y: i32, marker_length: f32, marker_separation: f32, dictionary: *const c_void, first_marker: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_GridBoard_create_int_int_float_float_const_PtrLDictionaryGR(markers_x: i32, markers_y: i32, marker_length: f32, marker_separation: f32, dictionary: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_aruco_GridBoard_getGridSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_aruco_GridBoard_getMarkerLength_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_aruco_GridBoard_getMarkerSeparation_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_aruco_GridBoard_to_Board(instance: *mut c_void) -> *mut c_void;
pub fn cv_aruco_GridBoard_delete(instance: *mut c_void);
