pub fn cv_PtrLcv_videostab_NullInpainterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullInpainterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullInpainterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_NullInpainterG_to_PtrOfInpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullInpainterG_new_const_NullInpainter(val: *mut c_void) -> *mut c_void;
