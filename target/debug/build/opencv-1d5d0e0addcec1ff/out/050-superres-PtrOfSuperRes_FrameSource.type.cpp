extern "C" {
	const cv::superres::FrameSource* cv_PtrLcv_superres_FrameSourceG_getInnerPtr_const(const cv::Ptr<cv::superres::FrameSource>* instance) {
			return instance->get();
	}
	
	cv::superres::FrameSource* cv_PtrLcv_superres_FrameSourceG_getInnerPtrMut(cv::Ptr<cv::superres::FrameSource>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_superres_FrameSourceG_delete(cv::Ptr<cv::superres::FrameSource>* instance) {
			delete instance;
	}
	
}

