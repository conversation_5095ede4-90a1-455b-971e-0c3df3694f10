pub fn cv_PtrLcv_videostab_VideoFileSourceG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_VideoFileSourceG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_VideoFileSourceG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_VideoFileSourceG_to_PtrOfIFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_VideoFileSourceG_new_const_VideoFileSource(val: *mut c_void) -> *mut c_void;
