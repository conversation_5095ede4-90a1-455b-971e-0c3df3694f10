pub fn cv_PtrLcv_SphericalWarperG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_SphericalWarperG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_SphericalWarperG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_SphericalWarperG_to_PtrOfWarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_SphericalWarperG_new_const_SphericalWarper(val: *mut c_void) -> *mut c_void;
