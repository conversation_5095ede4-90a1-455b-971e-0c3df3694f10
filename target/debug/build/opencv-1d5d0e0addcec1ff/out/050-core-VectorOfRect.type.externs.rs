pub fn std_vectorLcv_RectG_new_const() -> *mut c_void;
pub fn std_vectorLcv_RectG_delete(instance: *mut c_void);
pub fn std_vectorLcv_RectG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_RectG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_RectG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_RectG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_RectG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_RectG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_RectG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_RectG_clear(instance: *mut c_void);
pub fn std_vectorLcv_RectG_push_const_Rect(instance: *mut c_void, val: *const core::Rect);
pub fn std_vectorLcv_RectG_insert_size_t_const_Rect(instance: *mut c_void, index: size_t, val: *const core::Rect);
pub fn std_vectorLcv_RectG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::Rect);
pub fn std_vectorLcv_RectG_set_size_t_const_Rect(instance: *mut c_void, index: size_t, val: *const core::Rect);
pub fn std_vectorLcv_RectG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_RectG_data_const(instance: *const c_void) -> *const core::Rect;
pub fn std_vectorLcv_RectG_dataMut(instance: *mut c_void) -> *mut core::Rect;
pub fn cv_fromSlice_const_const_RectX_size_t(data: *const core::Rect, len: size_t) -> *mut c_void;
pub fn std_vectorLcv_RectG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_RectG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLcv_RectG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
