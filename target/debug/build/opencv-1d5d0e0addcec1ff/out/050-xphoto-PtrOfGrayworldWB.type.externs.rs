pub fn cv_PtrLcv_xphoto_GrayworldWBG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_GrayworldWBG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_GrayworldWBG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_xphoto_GrayworldWBG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_GrayworldWBG_to_PtrOfWhiteBalancer(instance: *mut c_void) -> *mut c_void;
