pub fn cv_PtrLcv_videostab_NullWobbleSuppressorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullWobbleSuppressorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullWobbleSuppressorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_NullWobbleSuppressorG_to_PtrOfWobbleSuppressorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullWobbleSuppressorG_new_const_NullWobbleSuppressor(val: *mut c_void) -> *mut c_void;
