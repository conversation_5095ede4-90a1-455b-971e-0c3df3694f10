extern "C" {
	const cv::linemod::Detector* cv_PtrLcv_linemod_DetectorG_getInnerPtr_const(const cv::Ptr<cv::linemod::Detector>* instance) {
			return instance->get();
	}
	
	cv::linemod::Detector* cv_PtrLcv_linemod_DetectorG_getInnerPtrMut(cv::Ptr<cv::linemod::Detector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_linemod_DetectorG_delete(cv::Ptr<cv::linemod::Detector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::linemod::Detector>* cv_PtrLcv_linemod_DetectorG_new_const_Detector(cv::linemod::Detector* val) {
			return new cv::Ptr<cv::linemod::Detector>(val);
	}
	
}

