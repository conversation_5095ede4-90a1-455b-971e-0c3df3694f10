pub fn cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_new_const_PyrLkOptFlowEstimatorBase(val: *mut c_void) -> *mut c_void;
