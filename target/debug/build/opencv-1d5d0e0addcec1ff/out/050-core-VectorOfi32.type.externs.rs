pub fn std_vectorLintG_new_const() -> *mut c_void;
pub fn std_vectorLintG_delete(instance: *mut c_void);
pub fn std_vectorLintG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLintG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLintG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLintG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLintG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLintG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLintG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLintG_clear(instance: *mut c_void);
pub fn std_vectorLintG_push_const_int(instance: *mut c_void, val: i32);
pub fn std_vectorLintG_insert_size_t_const_int(instance: *mut c_void, index: size_t, val: i32);
pub fn std_vectorLintG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut i32);
pub fn std_vectorLintG_set_size_t_const_int(instance: *mut c_void, index: size_t, val: i32);
pub fn std_vectorLintG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLintG_data_const(instance: *const c_void) -> *const i32;
pub fn std_vectorLintG_dataMut(instance: *mut c_void) -> *mut i32;
pub fn cv_fromSlice_const_const_intX_size_t(data: *const i32, len: size_t) -> *mut c_void;
pub fn std_vectorLintG_inputArray_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLintG_outputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn std_vectorLintG_inputOutputArray(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
