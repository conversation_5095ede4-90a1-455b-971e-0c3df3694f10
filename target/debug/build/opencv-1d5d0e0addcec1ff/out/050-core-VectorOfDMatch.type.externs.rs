pub fn std_vectorLcv_DMatchG_new_const() -> *mut c_void;
pub fn std_vectorLcv_DMatchG_delete(instance: *mut c_void);
pub fn std_vectorLcv_DMatchG_len_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_DMatchG_isEmpty_const(instance: *const c_void) -> bool;
pub fn std_vectorLcv_DMatchG_capacity_const(instance: *const c_void) -> size_t;
pub fn std_vectorLcv_DMatchG_shrinkToFit(instance: *mut c_void);
pub fn std_vectorLcv_DMatchG_reserve_size_t(instance: *mut c_void, additional: size_t);
pub fn std_vectorLcv_DMatchG_remove_size_t(instance: *mut c_void, index: size_t);
pub fn std_vectorLcv_DMatchG_swap_size_t_size_t(instance: *mut c_void, index1: size_t, index2: size_t);
pub fn std_vectorLcv_DMatchG_clear(instance: *mut c_void);
pub fn std_vectorLcv_DMatchG_push_const_DMatch(instance: *mut c_void, val: *const core::DMatch);
pub fn std_vectorLcv_DMatchG_insert_size_t_const_DMatch(instance: *mut c_void, index: size_t, val: *const core::DMatch);
pub fn std_vectorLcv_DMatchG_get_const_size_t(instance: *const c_void, index: size_t, ocvrs_return: *mut core::DMatch);
pub fn std_vectorLcv_DMatchG_set_size_t_const_DMatch(instance: *mut c_void, index: size_t, val: *const core::DMatch);
pub fn std_vectorLcv_DMatchG_clone_const(instance: *const c_void) -> *mut c_void;
pub fn std_vectorLcv_DMatchG_data_const(instance: *const c_void) -> *const core::DMatch;
pub fn std_vectorLcv_DMatchG_dataMut(instance: *mut c_void) -> *mut core::DMatch;
pub fn cv_fromSlice_const_const_DMatchX_size_t(data: *const core::DMatch, len: size_t) -> *mut c_void;
