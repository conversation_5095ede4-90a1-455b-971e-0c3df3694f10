pub fn cv_PtrLcv_ximgproc_RICInterpolatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_RICInterpolatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_RICInterpolatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_RICInterpolatorG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_RICInterpolatorG_to_PtrOfSparseMatchInterpolator(instance: *mut c_void) -> *mut c_void;
