pub fn cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_to_PtrOfSelectiveSearchSegmentationStrategy(instance: *mut c_void) -> *mut c_void;
