extern "C" {
	const cv::videostab::WobbleSuppressorBase* cv_PtrLcv_videostab_WobbleSuppressorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::WobbleSuppressorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::WobbleSuppressorBase* cv_PtrLcv_videostab_WobbleSuppressorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::WobbleSuppressorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_WobbleSuppressorBaseG_delete(cv::Ptr<cv::videostab::WobbleSuppressorBase>* instance) {
			delete instance;
	}
	
}

