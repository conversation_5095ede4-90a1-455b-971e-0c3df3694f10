extern "C" {
	const cv::xphoto::LearningBasedWB* cv_PtrLcv_xphoto_LearningBasedWBG_getInnerPtr_const(const cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			return instance->get();
	}
	
	cv::xphoto::LearningBasedWB* cv_PtrLcv_xphoto_LearningBasedWBG_getInnerPtrMut(cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_LearningBasedWBG_delete(cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_LearningBasedWBG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::xphoto::WhiteBalancer>* cv_PtrLcv_xphoto_LearningBasedWBG_to_PtrOfWhiteBalancer(cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			return new cv::Ptr<cv::xphoto::WhiteBalancer>(instance->dynamicCast<cv::xphoto::WhiteBalancer>());
	}
	
}

