extern "C" {
	const cv::xphoto::WhiteBalancer* cv_PtrLcv_xphoto_WhiteBalancerG_getInnerPtr_const(const cv::Ptr<cv::xphoto::WhiteBalancer>* instance) {
			return instance->get();
	}
	
	cv::xphoto::WhiteBalancer* cv_PtrLcv_xphoto_WhiteBalancerG_getInnerPtrMut(cv::Ptr<cv::xphoto::WhiteBalancer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_WhiteBalancerG_delete(cv::Ptr<cv::xphoto::WhiteBalancer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_WhiteBalancerG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::WhiteBalancer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

