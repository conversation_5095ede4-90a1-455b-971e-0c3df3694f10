pub fn cv_saliency_MotionSaliency_to_MotionSaliencyBinWangApr2014(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_MotionSaliency_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_MotionSaliency_to_Saliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_MotionSaliency_delete(instance: *mut c_void);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_MotionSaliencyBinWangApr2014(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_computeSaliency_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, saliency_map: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_setImagesize_int_int(instance: *mut c_void, w: i32, h: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_init(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_getImageWidth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_setImageWidth_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_getImageHeight_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_setImageHeight_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_MotionSaliencyBinWangApr2014_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_MotionSaliencyBinWangApr2014_to_MotionSaliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_MotionSaliencyBinWangApr2014_to_Saliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_MotionSaliencyBinWangApr2014_delete(instance: *mut c_void);
pub fn cv_saliency_Objectness_to_ObjectnessBING(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Objectness_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Objectness_to_Saliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Objectness_delete(instance: *mut c_void);
pub fn cv_saliency_ObjectnessBING_ObjectnessBING(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_ObjectnessBING_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_ObjectnessBING_computeSaliency_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, saliency_map: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_saliency_ObjectnessBING_read(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_ObjectnessBING_write_const(instance: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_ObjectnessBING_getobjectnessValues(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_ObjectnessBING_setTrainingPath_const_StringR(instance: *mut c_void, training_path: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_ObjectnessBING_setBBResDir_const_StringR(instance: *mut c_void, results_dir: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_ObjectnessBING_getBase_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_saliency_ObjectnessBING_setBase_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_ObjectnessBING_getNSS_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_saliency_ObjectnessBING_setNSS_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_ObjectnessBING_getW_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_saliency_ObjectnessBING_setW_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_ObjectnessBING_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_ObjectnessBING_to_Objectness(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_ObjectnessBING_to_Saliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_ObjectnessBING_delete(instance: *mut c_void);
pub fn cv_saliency_Saliency_computeSaliency_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, saliency_map: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_saliency_Saliency_to_MotionSaliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Saliency_to_MotionSaliencyBinWangApr2014(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Saliency_to_Objectness(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Saliency_to_ObjectnessBING(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Saliency_to_StaticSaliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Saliency_to_StaticSaliencyFineGrained(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Saliency_to_StaticSaliencySpectralResidual(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Saliency_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_Saliency_delete(instance: *mut c_void);
pub fn cv_saliency_StaticSaliency_computeBinaryMap_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, _saliency_map: *const c_void, _binary_map: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_saliency_StaticSaliency_to_StaticSaliencyFineGrained(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliency_to_StaticSaliencySpectralResidual(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliency_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliency_to_Saliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliency_delete(instance: *mut c_void);
pub fn cv_saliency_StaticSaliencyFineGrained_StaticSaliencyFineGrained(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_StaticSaliencyFineGrained_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_StaticSaliencyFineGrained_computeSaliency_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, saliency_map: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_saliency_StaticSaliencyFineGrained_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliencyFineGrained_to_Saliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliencyFineGrained_to_StaticSaliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliencyFineGrained_delete(instance: *mut c_void);
pub fn cv_saliency_StaticSaliencySpectralResidual_StaticSaliencySpectralResidual(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_StaticSaliencySpectralResidual_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_saliency_StaticSaliencySpectralResidual_computeSaliency_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, saliency_map: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_saliency_StaticSaliencySpectralResidual_read_const_FileNodeR(instance: *mut c_void, fn_: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_StaticSaliencySpectralResidual_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_StaticSaliencySpectralResidual_getImageWidth_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_saliency_StaticSaliencySpectralResidual_setImageWidth_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_StaticSaliencySpectralResidual_getImageHeight_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_saliency_StaticSaliencySpectralResidual_setImageHeight_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_saliency_StaticSaliencySpectralResidual_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliencySpectralResidual_to_Saliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliencySpectralResidual_to_StaticSaliency(instance: *mut c_void) -> *mut c_void;
pub fn cv_saliency_StaticSaliencySpectralResidual_delete(instance: *mut c_void);
