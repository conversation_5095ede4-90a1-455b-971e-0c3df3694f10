pub fn cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_to_PtrOfSparseMatchInterpolator(instance: *mut c_void) -> *mut c_void;
