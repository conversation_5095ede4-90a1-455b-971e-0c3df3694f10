pub fn cv_img_hash_averageHash_const__InputArrayR_const__OutputArrayR(input_arr: *const c_void, output_arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_blockMeanHash_const__InputArrayR_const__OutputArrayR(input_arr: *const c_void, output_arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_blockMeanHash_const__InputArrayR_const__OutputArrayR_int(input_arr: *const c_void, output_arr: *const c_void, mode: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_colorMomentHash_const__InputArrayR_const__OutputArrayR(input_arr: *const c_void, output_arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_marrHildrethHash_const__InputArrayR_const__OutputArrayR(input_arr: *const c_void, output_arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_marrHildrethHash_const__InputArrayR_const__OutputArrayR_float_float(input_arr: *const c_void, output_arr: *const c_void, alpha: f32, scale: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_pHash_const__InputArrayR_const__OutputArrayR(input_arr: *const c_void, output_arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_radialVarianceHash_const__InputArrayR_const__OutputArrayR(input_arr: *const c_void, output_arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_radialVarianceHash_const__InputArrayR_const__OutputArrayR_double_int(input_arr: *const c_void, output_arr: *const c_void, sigma: f64, num_of_angle_line: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_AverageHash_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_AverageHash_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_AverageHash_to_ImgHashBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_AverageHash_delete(instance: *mut c_void);
pub fn cv_img_hash_BlockMeanHash_setMode_int(instance: *mut c_void, mode: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_BlockMeanHash_getMean_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_BlockMeanHash_create_int(mode: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_BlockMeanHash_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_BlockMeanHash_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_BlockMeanHash_to_ImgHashBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_BlockMeanHash_delete(instance: *mut c_void);
pub fn cv_img_hash_ColorMomentHash_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_ColorMomentHash_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_ColorMomentHash_to_ImgHashBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_ColorMomentHash_delete(instance: *mut c_void);
pub fn cv_img_hash_ImgHashBase_compute_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, input_arr: *const c_void, output_arr: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_ImgHashBase_compare_const_const__InputArrayR_const__InputArrayR(instance: *const c_void, hash_one: *const c_void, hash_two: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_img_hash_ImgHashBase_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_ImgHashBase_delete(instance: *mut c_void);
pub fn cv_img_hash_MarrHildrethHash_getAlpha_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_img_hash_MarrHildrethHash_getScale_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_img_hash_MarrHildrethHash_setKernelParam_float_float(instance: *mut c_void, alpha: f32, scale: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_MarrHildrethHash_create_float_float(alpha: f32, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_MarrHildrethHash_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_MarrHildrethHash_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_MarrHildrethHash_to_ImgHashBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_MarrHildrethHash_delete(instance: *mut c_void);
pub fn cv_img_hash_PHash_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_PHash_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_PHash_to_ImgHashBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_PHash_delete(instance: *mut c_void);
pub fn cv_img_hash_RadialVarianceHash_create_double_int(sigma: f64, num_of_angle_line: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_RadialVarianceHash_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_RadialVarianceHash_getNumOfAngleLine_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_img_hash_RadialVarianceHash_getSigma_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_img_hash_RadialVarianceHash_setNumOfAngleLine_int(instance: *mut c_void, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_RadialVarianceHash_setSigma_double(instance: *mut c_void, value: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_img_hash_RadialVarianceHash_getFeatures(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_RadialVarianceHash_getHash(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_RadialVarianceHash_getPixPerLine_const_MatR(instance: *mut c_void, input: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_RadialVarianceHash_getProjection(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_img_hash_RadialVarianceHash_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_RadialVarianceHash_to_ImgHashBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_img_hash_RadialVarianceHash_delete(instance: *mut c_void);
