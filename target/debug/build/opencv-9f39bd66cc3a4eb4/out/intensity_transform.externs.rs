pub fn cv_intensity_transform_BIMEF_const__InputArrayR_const__OutputArrayR(input: *const c_void, output: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_intensity_transform_BIMEF_const__InputArrayR_const__OutputArrayR_float_float_float(input: *const c_void, output: *const c_void, mu: f32, a: f32, b: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_intensity_transform_BIMEF_const__InputArrayR_const__OutputArrayR_float_float_float_float(input: *const c_void, output: *const c_void, k: f32, mu: f32, a: f32, b: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_intensity_transform_autoscaling_const_Mat_MatR(input: *const c_void, output: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_intensity_transform_contrastStretching_const_Mat_MatR_const_int_const_int_const_int_const_int(input: *const c_void, output: *mut c_void, r1: i32, s1: i32, r2: i32, s2: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_intensity_transform_gammaCorrection_const_Mat_MatR_const_float(input: *const c_void, output: *mut c_void, gamma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_intensity_transform_logTransform_const_Mat_MatR(input: *const c_void, output: *mut c_void, ocvrs_return: *mut ResultVoid);
