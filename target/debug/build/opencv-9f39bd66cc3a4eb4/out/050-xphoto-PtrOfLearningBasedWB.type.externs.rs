pub fn cv_PtrLcv_xphoto_LearningBasedWBG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_LearningBasedWBG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_LearningBasedWBG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_xphoto_LearningBasedWBG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_xphoto_LearningBasedWBG_to_PtrOfWhiteBalancer(instance: *mut c_void) -> *mut c_void;
