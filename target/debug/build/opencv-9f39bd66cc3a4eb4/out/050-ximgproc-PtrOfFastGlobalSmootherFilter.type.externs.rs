pub fn cv_PtrLcv_ximgproc_FastGlobalSmootherFilterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_FastGlobalSmootherFilterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_FastGlobalSmootherFilterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_FastGlobalSmootherFilterG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
