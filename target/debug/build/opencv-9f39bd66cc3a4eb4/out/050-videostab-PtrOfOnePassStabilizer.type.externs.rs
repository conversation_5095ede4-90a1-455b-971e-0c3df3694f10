pub fn cv_PtrLcv_videostab_OnePassStabilizerG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_OnePassStabilizerG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_OnePassStabilizerG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_OnePassStabilizerG_to_PtrOfIFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_OnePassStabilizerG_to_PtrOfStabilizerBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_OnePassStabilizerG_new_const_OnePassStabilizer(val: *mut c_void) -> *mut c_void;
