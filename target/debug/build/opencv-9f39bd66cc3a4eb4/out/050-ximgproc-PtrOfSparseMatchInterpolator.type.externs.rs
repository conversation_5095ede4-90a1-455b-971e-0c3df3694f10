pub fn cv_PtrLcv_ximgproc_SparseMatchInterpolatorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_SparseMatchInterpolatorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_SparseMatchInterpolatorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_SparseMatchInterpolatorG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
