pub fn cv_CamShift_const__InputArrayR_RectR_TermCriteria(prob_image: *const c_void, window: *mut core::Rect, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<core::RotatedRect>);
pub fn cv_buildOpticalFlowPyramid_const__InputArrayR_const__OutputArrayR_Size_int(img: *const c_void, pyramid: *const c_void, win_size: *const core::Size, max_level: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_buildOpticalFlowPyramid_const__InputArrayR_const__OutputArrayR_Size_int_bool_int_int_bool(img: *const c_void, pyramid: *const c_void, win_size: *const core::Size, max_level: i32, with_derivatives: bool, pyr_border: i32, deriv_border: i32, try_reuse_input_image: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_calcOpticalFlowFarneback_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_double_int_int_int_int_double_int(prev: *const c_void, next: *const c_void, flow: *const c_void, pyr_scale: f64, levels: i32, winsize: i32, iterations: i32, poly_n: i32, poly_sigma: f64, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_calcOpticalFlowPyrLK_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(prev_img: *const c_void, next_img: *const c_void, prev_pts: *const c_void, next_pts: *const c_void, status: *const c_void, err: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_calcOpticalFlowPyrLK_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_Size_int_TermCriteria_int_double(prev_img: *const c_void, next_img: *const c_void, prev_pts: *const c_void, next_pts: *const c_void, status: *const c_void, err: *const c_void, win_size: *const core::Size, max_level: i32, criteria: *const core::TermCriteria, flags: i32, min_eig_threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_computeECC_const__InputArrayR_const__InputArrayR(template_image: *const c_void, input_image: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_computeECC_const__InputArrayR_const__InputArrayR_const__InputArrayR(template_image: *const c_void, input_image: *const c_void, input_mask: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_createBackgroundSubtractorKNN(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createBackgroundSubtractorKNN_int_double_bool(history: i32, dist2_threshold: f64, detect_shadows: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createBackgroundSubtractorMOG2(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createBackgroundSubtractorMOG2_int_double_bool(history: i32, var_threshold: f64, detect_shadows: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_estimateRigidTransform_const__InputArrayR_const__InputArrayR_bool(src: *const c_void, dst: *const c_void, full_affine: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findTransformECC_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR(template_image: *const c_void, input_image: *const c_void, warp_matrix: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_findTransformECC_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_int_TermCriteria_const__InputArrayR(template_image: *const c_void, input_image: *const c_void, warp_matrix: *const c_void, motion_type: i32, criteria: *const core::TermCriteria, input_mask: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_findTransformECC_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_int_TermCriteria_const__InputArrayR_int(template_image: *const c_void, input_image: *const c_void, warp_matrix: *const c_void, motion_type: i32, criteria: *const core::TermCriteria, input_mask: *const c_void, gauss_filt_size: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_meanShift_const__InputArrayR_RectR_TermCriteria(prob_image: *const c_void, window: *mut core::Rect, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<i32>);
pub fn cv_readOpticalFlow_const_StringR(path: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_writeOpticalFlow_const_StringR_const__InputArrayR(path: *const c_char, flow: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_BackgroundSubtractor_apply_const__InputArrayR_const__OutputArrayR_double(instance: *mut c_void, image: *const c_void, fgmask: *const c_void, learning_rate: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractor_apply_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, fgmask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractor_getBackgroundImage_const_const__OutputArrayR(instance: *const c_void, background_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractor_to_BackgroundSubtractorKNN(instance: *mut c_void) -> *mut c_void;
pub fn cv_BackgroundSubtractor_to_BackgroundSubtractorMOG2(instance: *mut c_void) -> *mut c_void;
pub fn cv_BackgroundSubtractor_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_BackgroundSubtractor_delete(instance: *mut c_void);
pub fn cv_BackgroundSubtractorKNN_getHistory_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BackgroundSubtractorKNN_setHistory_int(instance: *mut c_void, history: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorKNN_getNSamples_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BackgroundSubtractorKNN_setNSamples_int(instance: *mut c_void, _n_n: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorKNN_getDist2Threshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorKNN_setDist2Threshold_double(instance: *mut c_void, _dist2_threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorKNN_getkNNSamples_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BackgroundSubtractorKNN_setkNNSamples_int(instance: *mut c_void, _nk_nn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorKNN_getDetectShadows_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_BackgroundSubtractorKNN_setDetectShadows_bool(instance: *mut c_void, detect_shadows: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorKNN_getShadowValue_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BackgroundSubtractorKNN_setShadowValue_int(instance: *mut c_void, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorKNN_getShadowThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorKNN_setShadowThreshold_double(instance: *mut c_void, threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorKNN_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_BackgroundSubtractorKNN_to_BackgroundSubtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_BackgroundSubtractorKNN_delete(instance: *mut c_void);
pub fn cv_BackgroundSubtractorMOG2_getHistory_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BackgroundSubtractorMOG2_setHistory_int(instance: *mut c_void, history: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getNMixtures_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BackgroundSubtractorMOG2_setNMixtures_int(instance: *mut c_void, nmixtures: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getBackgroundRatio_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorMOG2_setBackgroundRatio_double(instance: *mut c_void, ratio: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getVarThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorMOG2_setVarThreshold_double(instance: *mut c_void, var_threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getVarThresholdGen_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorMOG2_setVarThresholdGen_double(instance: *mut c_void, var_threshold_gen: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getVarInit_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorMOG2_setVarInit_double(instance: *mut c_void, var_init: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getVarMin_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorMOG2_setVarMin_double(instance: *mut c_void, var_min: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getVarMax_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorMOG2_setVarMax_double(instance: *mut c_void, var_max: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getComplexityReductionThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorMOG2_setComplexityReductionThreshold_double(instance: *mut c_void, ct: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getDetectShadows_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_BackgroundSubtractorMOG2_setDetectShadows_bool(instance: *mut c_void, detect_shadows: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getShadowValue_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_BackgroundSubtractorMOG2_setShadowValue_int(instance: *mut c_void, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_getShadowThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_BackgroundSubtractorMOG2_setShadowThreshold_double(instance: *mut c_void, threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_apply_const__InputArrayR_const__OutputArrayR_double(instance: *mut c_void, image: *const c_void, fgmask: *const c_void, learning_rate: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_apply_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, fgmask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_BackgroundSubtractorMOG2_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_BackgroundSubtractorMOG2_to_BackgroundSubtractor(instance: *mut c_void) -> *mut c_void;
pub fn cv_BackgroundSubtractorMOG2_delete(instance: *mut c_void);
pub fn cv_DISOpticalFlow_getFinestScale_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_DISOpticalFlow_setFinestScale_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getPatchSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_DISOpticalFlow_setPatchSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getPatchStride_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_DISOpticalFlow_setPatchStride_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getGradientDescentIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_DISOpticalFlow_setGradientDescentIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getVariationalRefinementIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_DISOpticalFlow_setVariationalRefinementIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getVariationalRefinementAlpha_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_DISOpticalFlow_setVariationalRefinementAlpha_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getVariationalRefinementDelta_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_DISOpticalFlow_setVariationalRefinementDelta_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getVariationalRefinementGamma_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_DISOpticalFlow_setVariationalRefinementGamma_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getUseMeanNormalization_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_DISOpticalFlow_setUseMeanNormalization_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_getUseSpatialPropagation_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_DISOpticalFlow_setUseSpatialPropagation_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_DISOpticalFlow_create_int(preset: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DISOpticalFlow_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_DISOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_DISOpticalFlow_to_DenseOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_DISOpticalFlow_delete(instance: *mut c_void);
pub fn cv_DenseOpticalFlow_calc_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR(instance: *mut c_void, i0: *const c_void, i1: *const c_void, flow: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DenseOpticalFlow_collectGarbage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_DenseOpticalFlow_to_DISOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_DenseOpticalFlow_to_FarnebackOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_DenseOpticalFlow_to_VariationalRefinement(instance: *mut c_void) -> *mut c_void;
pub fn cv_DenseOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_DenseOpticalFlow_delete(instance: *mut c_void);
pub fn cv_FarnebackOpticalFlow_getNumLevels_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FarnebackOpticalFlow_setNumLevels_int(instance: *mut c_void, num_levels: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FarnebackOpticalFlow_getPyrScale_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_FarnebackOpticalFlow_setPyrScale_double(instance: *mut c_void, pyr_scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_FarnebackOpticalFlow_getFastPyramids_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_FarnebackOpticalFlow_setFastPyramids_bool(instance: *mut c_void, fast_pyramids: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_FarnebackOpticalFlow_getWinSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FarnebackOpticalFlow_setWinSize_int(instance: *mut c_void, win_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FarnebackOpticalFlow_getNumIters_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FarnebackOpticalFlow_setNumIters_int(instance: *mut c_void, num_iters: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FarnebackOpticalFlow_getPolyN_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FarnebackOpticalFlow_setPolyN_int(instance: *mut c_void, poly_n: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FarnebackOpticalFlow_getPolySigma_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_FarnebackOpticalFlow_setPolySigma_double(instance: *mut c_void, poly_sigma: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_FarnebackOpticalFlow_getFlags_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_FarnebackOpticalFlow_setFlags_int(instance: *mut c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_FarnebackOpticalFlow_create_int_double_bool_int_int_int_double_int(num_levels: i32, pyr_scale: f64, fast_pyramids: bool, win_size: i32, num_iters: i32, poly_n: i32, poly_sigma: f64, flags: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FarnebackOpticalFlow_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FarnebackOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_FarnebackOpticalFlow_to_DenseOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_FarnebackOpticalFlow_delete(instance: *mut c_void);
pub fn cv_KalmanFilter_KalmanFilter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KalmanFilter_KalmanFilter_int_int_int_int(dynam_params: i32, measure_params: i32, control_params: i32, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KalmanFilter_KalmanFilter_int_int(dynam_params: i32, measure_params: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KalmanFilter_init_int_int_int_int(instance: *mut c_void, dynam_params: i32, measure_params: i32, control_params: i32, typ: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_KalmanFilter_init_int_int(instance: *mut c_void, dynam_params: i32, measure_params: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_KalmanFilter_predict_const_MatR(instance: *mut c_void, control: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KalmanFilter_predict(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KalmanFilter_correct_const_MatR(instance: *mut c_void, measurement: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_KalmanFilter_propStatePre_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propStatePre_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propStatePost_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propStatePost_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propTransitionMatrix_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propTransitionMatrix_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propControlMatrix_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propControlMatrix_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propMeasurementMatrix_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propMeasurementMatrix_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propProcessNoiseCov_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propProcessNoiseCov_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propMeasurementNoiseCov_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propMeasurementNoiseCov_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propErrorCovPre_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propErrorCovPre_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propGain_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propGain_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propErrorCovPost_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propErrorCovPost_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propTemp1_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propTemp1_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propTemp2_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propTemp2_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propTemp3_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propTemp3_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propTemp4_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propTemp4_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_propTemp5_const(instance: *const c_void) -> *mut c_void;
pub fn cv_KalmanFilter_propTemp5_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_KalmanFilter_delete(instance: *mut c_void);
pub fn cv_SparseOpticalFlow_calc_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, prev_img: *const c_void, next_img: *const c_void, prev_pts: *const c_void, next_pts: *const c_void, status: *const c_void, err: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseOpticalFlow_calc_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR(instance: *mut c_void, prev_img: *const c_void, next_img: *const c_void, prev_pts: *const c_void, next_pts: *const c_void, status: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_SparseOpticalFlow_to_SparsePyrLKOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_SparseOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_SparseOpticalFlow_delete(instance: *mut c_void);
pub fn cv_SparsePyrLKOpticalFlow_getWinSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_SparsePyrLKOpticalFlow_setWinSize_Size(instance: *mut c_void, win_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_SparsePyrLKOpticalFlow_getMaxLevel_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_SparsePyrLKOpticalFlow_setMaxLevel_int(instance: *mut c_void, max_level: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparsePyrLKOpticalFlow_getTermCriteria_const(instance: *const c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_SparsePyrLKOpticalFlow_setTermCriteria_TermCriteriaR(instance: *mut c_void, crit: *mut core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_SparsePyrLKOpticalFlow_getFlags_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_SparsePyrLKOpticalFlow_setFlags_int(instance: *mut c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_SparsePyrLKOpticalFlow_getMinEigThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_SparsePyrLKOpticalFlow_setMinEigThreshold_double(instance: *mut c_void, min_eig_threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_SparsePyrLKOpticalFlow_create_Size_int_TermCriteria_int_double(win_size: *const core::Size, max_level: i32, crit: *const core::TermCriteria, flags: i32, min_eig_threshold: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparsePyrLKOpticalFlow_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SparsePyrLKOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_SparsePyrLKOpticalFlow_to_SparseOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_SparsePyrLKOpticalFlow_delete(instance: *mut c_void);
pub fn cv_Tracker_init_const__InputArrayR_const_RectR(instance: *mut c_void, image: *const c_void, bounding_box: *const core::Rect, ocvrs_return: *mut ResultVoid);
pub fn cv_Tracker_update_const__InputArrayR_RectR(instance: *mut c_void, image: *const c_void, bounding_box: *mut core::Rect, ocvrs_return: *mut Result<bool>);
pub fn cv_Tracker_to_TrackerDaSiamRPN(instance: *mut c_void) -> *mut c_void;
pub fn cv_Tracker_to_TrackerGOTURN(instance: *mut c_void) -> *mut c_void;
pub fn cv_Tracker_to_TrackerMIL(instance: *mut c_void) -> *mut c_void;
pub fn cv_Tracker_delete(instance: *mut c_void);
pub fn cv_TrackerDaSiamRPN_create_const_ParamsR(parameters: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TrackerDaSiamRPN_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TrackerDaSiamRPN_getTrackingScore(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_TrackerDaSiamRPN_to_Tracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_TrackerDaSiamRPN_delete(instance: *mut c_void);
pub fn cv_TrackerDaSiamRPN_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TrackerDaSiamRPN_Params_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_TrackerDaSiamRPN_Params_propModel_const(instance: *const c_void) -> *mut c_void;
pub fn cv_TrackerDaSiamRPN_Params_propModel_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_TrackerDaSiamRPN_Params_propKernel_cls1_const(instance: *const c_void) -> *mut c_void;
pub fn cv_TrackerDaSiamRPN_Params_propKernel_cls1_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_TrackerDaSiamRPN_Params_propKernel_r1_const(instance: *const c_void) -> *mut c_void;
pub fn cv_TrackerDaSiamRPN_Params_propKernel_r1_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_TrackerDaSiamRPN_Params_propBackend_const(instance: *const c_void) -> i32;
pub fn cv_TrackerDaSiamRPN_Params_propBackend_const_int(instance: *mut c_void, val: i32);
pub fn cv_TrackerDaSiamRPN_Params_propTarget_const(instance: *const c_void) -> i32;
pub fn cv_TrackerDaSiamRPN_Params_propTarget_const_int(instance: *mut c_void, val: i32);
pub fn cv_TrackerDaSiamRPN_Params_delete(instance: *mut c_void);
pub fn cv_TrackerGOTURN_create_const_ParamsR(parameters: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TrackerGOTURN_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TrackerGOTURN_to_Tracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_TrackerGOTURN_delete(instance: *mut c_void);
pub fn cv_TrackerGOTURN_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TrackerGOTURN_Params_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_TrackerGOTURN_Params_propModelTxt_const(instance: *const c_void) -> *mut c_void;
pub fn cv_TrackerGOTURN_Params_propModelTxt_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_TrackerGOTURN_Params_propModelBin_const(instance: *const c_void) -> *mut c_void;
pub fn cv_TrackerGOTURN_Params_propModelBin_const_string(instance: *mut c_void, val: *const c_char);
pub fn cv_TrackerGOTURN_Params_delete(instance: *mut c_void);
pub fn cv_TrackerMIL_create_const_ParamsR(parameters: *const crate::video::TrackerMIL_Params, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TrackerMIL_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TrackerMIL_to_Tracker(instance: *mut c_void) -> *mut c_void;
pub fn cv_TrackerMIL_delete(instance: *mut c_void);
pub fn cv_TrackerMIL_Params_Params(ocvrs_return: *mut Result<crate::video::TrackerMIL_Params>);
pub fn cv_VariationalRefinement_calcUV_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, i0: *const c_void, i1: *const c_void, flow_u: *const c_void, flow_v: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_VariationalRefinement_getFixedPointIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_VariationalRefinement_setFixedPointIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_VariationalRefinement_getSorIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_VariationalRefinement_setSorIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_VariationalRefinement_getOmega_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_VariationalRefinement_setOmega_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_VariationalRefinement_getAlpha_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_VariationalRefinement_setAlpha_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_VariationalRefinement_getDelta_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_VariationalRefinement_setDelta_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_VariationalRefinement_getGamma_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_VariationalRefinement_setGamma_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_VariationalRefinement_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VariationalRefinement_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_VariationalRefinement_to_DenseOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_VariationalRefinement_delete(instance: *mut c_void);
