pub fn cv_PtrLcv_videostab_NullOutlierRejectorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullOutlierRejectorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullOutlierRejectorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_NullOutlierRejectorG_to_PtrOfIOutlierRejector(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_NullOutlierRejectorG_new_const_NullOutlierRejector(val: *mut c_void) -> *mut c_void;
