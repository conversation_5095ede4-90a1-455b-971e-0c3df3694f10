pub fn cv_omnidir_calibrate_const__InputArrayR_const__InputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points: *const c_void, size: *const core::Size, k: *const c_void, xi: *const c_void, d: *const c_void, rvecs: *const c_void, tvecs: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_omnidir_calibrate_const__InputArrayR_const__InputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, size: *const core::Size, k: *const c_void, xi: *const c_void, d: *const c_void, rvecs: *const c_void, tvecs: *const c_void, flags: i32, criteria: *const core::TermCriteria, idx: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_omnidir_initUndistortRectifyMap_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR_int_const__OutputArrayR_const__OutputArrayR_int(k: *const c_void, d: *const c_void, xi: *const c_void, r: *const c_void, p: *const c_void, size: *const core::Size, m1type: i32, map1: *const c_void, map2: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_projectPoints_const__InputArrayR_const__OutputArrayR_const_Affine3dR_const__InputArrayR_double_const__InputArrayR(object_points: *const c_void, image_points: *const c_void, affine: *const core::Affine3d, k: *const c_void, xi: f64, d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_projectPoints_const__InputArrayR_const__OutputArrayR_const_Affine3dR_const__InputArrayR_double_const__InputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, affine: *const core::Affine3d, k: *const c_void, xi: f64, d: *const c_void, jacobian: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_projectPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_double_const__InputArrayR(object_points: *const c_void, image_points: *const c_void, rvec: *const c_void, tvec: *const c_void, k: *const c_void, xi: f64, d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_projectPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_double_const__InputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, rvec: *const c_void, tvec: *const c_void, k: *const c_void, xi: f64, d: *const c_void, jacobian: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_stereoCalibrate_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const_SizeR_const_SizeR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points1: *const c_void, image_points2: *const c_void, image_size1: *const core::Size, image_size2: *const core::Size, k1: *const c_void, xi1: *const c_void, d1: *const c_void, k2: *const c_void, xi2: *const c_void, d2: *const c_void, rvec: *const c_void, tvec: *const c_void, rvecs_l: *const c_void, tvecs_l: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_omnidir_stereoCalibrate_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const_SizeR_const_SizeR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria_const__OutputArrayR(object_points: *const c_void, image_points1: *const c_void, image_points2: *const c_void, image_size1: *const core::Size, image_size2: *const core::Size, k1: *const c_void, xi1: *const c_void, d1: *const c_void, k2: *const c_void, xi2: *const c_void, d2: *const c_void, rvec: *const c_void, tvec: *const c_void, rvecs_l: *const c_void, tvecs_l: *const c_void, flags: i32, criteria: *const core::TermCriteria, idx: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_omnidir_stereoReconstruct_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_int_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(image1: *const c_void, image2: *const c_void, k1: *const c_void, d1: *const c_void, xi1: *const c_void, k2: *const c_void, d2: *const c_void, xi2: *const c_void, r: *const c_void, t: *const c_void, flag: i32, num_disparities: i32, sad_window_size: i32, disparity: *const c_void, image1_rec: *const c_void, image2_rec: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_stereoReconstruct_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_int_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const_SizeR_const__InputArrayR_const__OutputArrayR_int(image1: *const c_void, image2: *const c_void, k1: *const c_void, d1: *const c_void, xi1: *const c_void, k2: *const c_void, d2: *const c_void, xi2: *const c_void, r: *const c_void, t: *const c_void, flag: i32, num_disparities: i32, sad_window_size: i32, disparity: *const c_void, image1_rec: *const c_void, image2_rec: *const c_void, new_size: *const core::Size, knew: *const c_void, point_cloud: *const c_void, point_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_stereoRectify_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(r: *const c_void, t: *const c_void, r1: *const c_void, r2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_undistortImage_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_int(distorted: *const c_void, undistorted: *const c_void, k: *const c_void, d: *const c_void, xi: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_undistortImage_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_const__InputArrayR_const_SizeR_const__InputArrayR(distorted: *const c_void, undistorted: *const c_void, k: *const c_void, d: *const c_void, xi: *const c_void, flags: i32, knew: *const c_void, new_size: *const core::Size, r: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_omnidir_undistortPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(distorted: *const c_void, undistorted: *const c_void, k: *const c_void, d: *const c_void, xi: *const c_void, r: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ccalib_CustomPattern_CustomPattern(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccalib_CustomPattern_create_const__InputArrayR_const_Size2f_const__OutputArrayR(instance: *mut c_void, pattern: *const c_void, board_size: *const core::Size2f, output: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_create_const__InputArrayR_const_Size2f(instance: *mut c_void, pattern: *const c_void, board_size: *const core::Size2f, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findPattern_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const_double_const_double_const_bool_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, matched_features: *const c_void, pattern_points: *const c_void, ratio: f64, proj_error: f64, refine_position: bool, out: *const c_void, h: *const c_void, pattern_corners: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findPattern_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, image: *const c_void, matched_features: *const c_void, pattern_points: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_isInitialized(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_getPatternPoints_vectorLKeyPointGR(instance: *mut c_void, original_points: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ccalib_CustomPattern_getPixelSize(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ccalib_CustomPattern_setFeatureDetector_PtrLFeature2DG(instance: *mut c_void, feature_detector: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_setDescriptorExtractor_PtrLFeature2DG(instance: *mut c_void, extractor: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_setDescriptorMatcher_PtrLDescriptorMatcherG(instance: *mut c_void, matcher: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_getFeatureDetector(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccalib_CustomPattern_getDescriptorExtractor(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccalib_CustomPattern_getDescriptorMatcher(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_ccalib_CustomPattern_calibrate_const__InputArrayR_const__InputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(instance: *mut c_void, object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_ccalib_CustomPattern_calibrate_const__InputArrayR_const__InputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_ccalib_CustomPattern_findRt_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_bool_int(instance: *mut c_void, object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, use_extrinsic_guess: bool, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findRt_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findRt_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_bool_int(instance: *mut c_void, image: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, use_extrinsic_guess: bool, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findRt_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, image: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findRtRANSAC_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_bool_int_float_int_const__OutputArrayR_int(instance: *mut c_void, object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, use_extrinsic_guess: bool, iterations_count: i32, reprojection_error: f32, min_inliers_count: i32, inliers: *const c_void, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findRtRANSAC_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findRtRANSAC_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_bool_int_float_int_const__OutputArrayR_int(instance: *mut c_void, image: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, use_extrinsic_guess: bool, iterations_count: i32, reprojection_error: f32, min_inliers_count: i32, inliers: *const c_void, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_findRtRANSAC_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, image: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_ccalib_CustomPattern_drawOrientation_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_double_int(instance: *mut c_void, image: *const c_void, tvec: *const c_void, rvec: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, axis_length: f64, axis_width: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_ccalib_CustomPattern_drawOrientation_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, image: *const c_void, tvec: *const c_void, rvec: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_ccalib_CustomPattern_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_ccalib_CustomPattern_delete(instance: *mut c_void);
pub fn cv_multicalib_MultiCameraCalibration_MultiCameraCalibration_int_int_const_stringR_float_float_int_int_int_int_TermCriteria_PtrLFeature2DG_PtrLFeature2DG_PtrLDescriptorMatcherG(camera_type: i32, n_cameras: i32, file_name: *const c_char, pattern_width: f32, pattern_height: f32, verbose: i32, show_extration: i32, n_mini_matches: i32, flags: i32, criteria: *const core::TermCriteria, detector: *mut c_void, descriptor: *mut c_void, matcher: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_multicalib_MultiCameraCalibration_MultiCameraCalibration_int_int_const_stringR_float_float(camera_type: i32, n_cameras: i32, file_name: *const c_char, pattern_width: f32, pattern_height: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_multicalib_MultiCameraCalibration_loadImages(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_multicalib_MultiCameraCalibration_initialize(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_multicalib_MultiCameraCalibration_optimizeExtrinsics(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_multicalib_MultiCameraCalibration_run(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_multicalib_MultiCameraCalibration_writeParameters_const_stringR(instance: *mut c_void, filename: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_multicalib_MultiCameraCalibration_delete(instance: *mut c_void);
pub fn cv_multicalib_MultiCameraCalibration_edge_edge_int_int_int_Mat(cv: i32, pv: i32, pi: i32, trans: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_multicalib_MultiCameraCalibration_edge_propCameraVertex_const(instance: *const c_void) -> i32;
pub fn cv_multicalib_MultiCameraCalibration_edge_propCameraVertex_const_int(instance: *mut c_void, val: i32);
pub fn cv_multicalib_MultiCameraCalibration_edge_propPhotoVertex_const(instance: *const c_void) -> i32;
pub fn cv_multicalib_MultiCameraCalibration_edge_propPhotoVertex_const_int(instance: *mut c_void, val: i32);
pub fn cv_multicalib_MultiCameraCalibration_edge_propPhotoIndex_const(instance: *const c_void) -> i32;
pub fn cv_multicalib_MultiCameraCalibration_edge_propPhotoIndex_const_int(instance: *mut c_void, val: i32);
pub fn cv_multicalib_MultiCameraCalibration_edge_propTransform_const(instance: *const c_void) -> *mut c_void;
pub fn cv_multicalib_MultiCameraCalibration_edge_propTransform_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_multicalib_MultiCameraCalibration_edge_delete(instance: *mut c_void);
pub fn cv_multicalib_MultiCameraCalibration_vertex_vertex_Mat_int(po: *mut c_void, ts: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_multicalib_MultiCameraCalibration_vertex_vertex(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_multicalib_MultiCameraCalibration_vertex_propPose_const(instance: *const c_void) -> *mut c_void;
pub fn cv_multicalib_MultiCameraCalibration_vertex_propPose_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_multicalib_MultiCameraCalibration_vertex_propTimestamp_const(instance: *const c_void) -> i32;
pub fn cv_multicalib_MultiCameraCalibration_vertex_propTimestamp_const_int(instance: *mut c_void, val: i32);
pub fn cv_multicalib_MultiCameraCalibration_vertex_delete(instance: *mut c_void);
pub fn cv_randpattern_RandomPatternCornerFinder_RandomPatternCornerFinder_float_float_int_int_int_int_PtrLFeature2DG_PtrLFeature2DG_PtrLDescriptorMatcherG(pattern_width: f32, pattern_height: f32, nmini_match: i32, depth: i32, verbose: i32, show_extraction: i32, detector: *mut c_void, descriptor: *mut c_void, matcher: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_randpattern_RandomPatternCornerFinder_RandomPatternCornerFinder_float_float(pattern_width: f32, pattern_height: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_randpattern_RandomPatternCornerFinder_loadPattern_const_MatR(instance: *mut c_void, pattern_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_randpattern_RandomPatternCornerFinder_loadPattern_const_MatR_const_vectorLKeyPointGR_const_MatR(instance: *mut c_void, pattern_image: *const c_void, pattern_key_points: *const c_void, pattern_descriptors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_randpattern_RandomPatternCornerFinder_computeObjectImagePoints_vectorLMatG(instance: *mut c_void, input_images: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_randpattern_RandomPatternCornerFinder_computeObjectImagePointsForSingle_Mat(instance: *mut c_void, input_image: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_randpattern_RandomPatternCornerFinder_getObjectPoints(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_randpattern_RandomPatternCornerFinder_getImagePoints(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_randpattern_RandomPatternCornerFinder_delete(instance: *mut c_void);
pub fn cv_randpattern_RandomPatternGenerator_RandomPatternGenerator_int_int(image_width: i32, image_height: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_randpattern_RandomPatternGenerator_generatePattern(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_randpattern_RandomPatternGenerator_getPattern(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_randpattern_RandomPatternGenerator_delete(instance: *mut c_void);
