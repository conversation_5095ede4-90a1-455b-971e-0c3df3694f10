pub fn cv_xphoto_applyChannelGains_const__InputArrayR_const__OutputArrayR_float_float_float(src: *const c_void, dst: *const c_void, gain_b: f32, gain_g: f32, gain_r: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_bm3dDenoising_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR(src: *const c_void, dst_step1: *const c_void, dst_step2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_bm3dDenoising_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_float_int_int_int_int_int_int_float_int_int_int(src: *const c_void, dst_step1: *const c_void, dst_step2: *const c_void, h: f32, template_window_size: i32, search_window_size: i32, block_matching_step1: i32, block_matching_step2: i32, group_size: i32, sliding_step: i32, beta: f32, norm_type: i32, step: i32, transform_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_bm3dDenoising_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_bm3dDenoising_const__InputArrayR_const__OutputArrayR_float_int_int_int_int_int_int_float_int_int_int(src: *const c_void, dst: *const c_void, h: f32, template_window_size: i32, search_window_size: i32, block_matching_step1: i32, block_matching_step2: i32, group_size: i32, sliding_step: i32, beta: f32, norm_type: i32, step: i32, transform_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_createGrayworldWB(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_xphoto_createLearningBasedWB(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_xphoto_createLearningBasedWB_const_StringR(path_to_model: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_xphoto_createSimpleWB(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_xphoto_createTonemapDurand(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_xphoto_createTonemapDurand_float_float_float_float_float(gamma: f32, contrast: f32, saturation: f32, sigma_color: f32, sigma_space: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_xphoto_dctDenoising_const_MatR_MatR_const_double(src: *const c_void, dst: *mut c_void, sigma: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_dctDenoising_const_MatR_MatR_const_double_const_int(src: *const c_void, dst: *mut c_void, sigma: f64, psize: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_inpaint_const_MatR_const_MatR_MatR_const_int(src: *const c_void, mask: *const c_void, dst: *mut c_void, algorithm_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_oilPainting_const__InputArrayR_const__OutputArrayR_int_int(src: *const c_void, dst: *const c_void, size: i32, dyn_ratio: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_oilPainting_const__InputArrayR_const__OutputArrayR_int_int_int(src: *const c_void, dst: *const c_void, size: i32, dyn_ratio: i32, code: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_GrayworldWB_getSaturationThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_GrayworldWB_setSaturationThreshold_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_GrayworldWB_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_GrayworldWB_to_WhiteBalancer(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_GrayworldWB_delete(instance: *mut c_void);
pub fn cv_xphoto_LearningBasedWB_extractSimpleFeatures_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_LearningBasedWB_getRangeMaxVal_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_xphoto_LearningBasedWB_setRangeMaxVal_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_LearningBasedWB_getSaturationThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_LearningBasedWB_setSaturationThreshold_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_LearningBasedWB_getHistBinNum_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_xphoto_LearningBasedWB_setHistBinNum_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_LearningBasedWB_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_LearningBasedWB_to_WhiteBalancer(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_LearningBasedWB_delete(instance: *mut c_void);
pub fn cv_xphoto_SimpleWB_getInputMin_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_SimpleWB_setInputMin_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_SimpleWB_getInputMax_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_SimpleWB_setInputMax_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_SimpleWB_getOutputMin_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_SimpleWB_setOutputMin_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_SimpleWB_getOutputMax_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_SimpleWB_setOutputMax_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_SimpleWB_getP_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_SimpleWB_setP_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_SimpleWB_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_SimpleWB_to_WhiteBalancer(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_SimpleWB_delete(instance: *mut c_void);
pub fn cv_xphoto_TonemapDurand_getSaturation_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_TonemapDurand_setSaturation_float(instance: *mut c_void, saturation: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_TonemapDurand_getContrast_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_TonemapDurand_setContrast_float(instance: *mut c_void, contrast: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_TonemapDurand_getSigmaSpace_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_TonemapDurand_setSigmaSpace_float(instance: *mut c_void, sigma_space: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_TonemapDurand_getSigmaColor_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_xphoto_TonemapDurand_setSigmaColor_float(instance: *mut c_void, sigma_color: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_TonemapDurand_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_TonemapDurand_to_Tonemap(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_TonemapDurand_delete(instance: *mut c_void);
pub fn cv_xphoto_WhiteBalancer_balanceWhite_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_xphoto_WhiteBalancer_to_GrayworldWB(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_WhiteBalancer_to_LearningBasedWB(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_WhiteBalancer_to_SimpleWB(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_WhiteBalancer_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_xphoto_WhiteBalancer_delete(instance: *mut c_void);
