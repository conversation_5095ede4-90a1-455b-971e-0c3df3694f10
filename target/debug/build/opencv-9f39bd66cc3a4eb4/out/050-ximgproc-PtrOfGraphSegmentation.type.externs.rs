pub fn cv_PtrLcv_ximgproc_segmentation_GraphSegmentationG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_segmentation_GraphSegmentationG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_segmentation_GraphSegmentationG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_segmentation_GraphSegmentationG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
