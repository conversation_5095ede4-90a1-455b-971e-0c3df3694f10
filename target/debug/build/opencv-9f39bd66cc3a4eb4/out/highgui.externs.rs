pub fn cv_addText_const_MatR_const_StringR_Point_const_QtFontR(img: *const c_void, text: *const c_char, org: *const core::Point, font: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_addText_const_MatR_const_StringR_Point_const_StringR(img: *const c_void, text: *const c_char, org: *const core::Point, name_font: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_addText_const_MatR_const_StringR_Point_const_StringR_int_Scalar_int_int_int(img: *const c_void, text: *const c_char, org: *const core::Point, name_font: *const c_char, point_size: i32, color: *const core::Scalar, weight: i32, style: i32, spacing: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_createButton_const_StringR_ButtonCallback_voidX(bar_name: *const c_char, on_change: Option<unsafe extern "C" fn(i32, *mut c_void) -> ()>, userdata: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_createButton_const_StringR_ButtonCallback_voidX_int_bool(bar_name: *const c_char, on_change: Option<unsafe extern "C" fn(i32, *mut c_void) -> ()>, userdata: *mut c_void, typ: i32, initial_button_state: bool, ocvrs_return: *mut Result<i32>);
pub fn cv_createTrackbar_const_StringR_const_StringR_intX_int_TrackbarCallback_voidX(trackbarname: *const c_char, winname: *const c_char, value: *mut i32, count: i32, on_change: Option<unsafe extern "C" fn(i32, *mut c_void) -> ()>, userdata: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_destroyAllWindows(ocvrs_return: *mut ResultVoid);
pub fn cv_destroyWindow_const_StringR(winname: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_displayOverlay_const_StringR_const_StringR(winname: *const c_char, text: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_displayOverlay_const_StringR_const_StringR_int(winname: *const c_char, text: *const c_char, delayms: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_displayStatusBar_const_StringR_const_StringR(winname: *const c_char, text: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_displayStatusBar_const_StringR_const_StringR_int(winname: *const c_char, text: *const c_char, delayms: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fontQt_const_StringR(name_font: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_fontQt_const_StringR_int_Scalar_int_int_int(name_font: *const c_char, point_size: i32, color: *const core::Scalar, weight: i32, style: i32, spacing: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getMouseWheelDelta_int(flags: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_getTrackbarPos_const_StringR_const_StringR(trackbarname: *const c_char, winname: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_getWindowImageRect_const_StringR(winname: *const c_char, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_getWindowProperty_const_StringR_int(winname: *const c_char, prop_id: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_imshow_const_StringR_const__InputArrayR(winname: *const c_char, mat: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_loadWindowParameters_const_StringR(window_name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_moveWindow_const_StringR_int_int(winname: *const c_char, x: i32, y: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_namedWindow_const_StringR(winname: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_namedWindow_const_StringR_int(winname: *const c_char, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_pollKey(ocvrs_return: *mut Result<i32>);
pub fn cv_resizeWindow_const_StringR_const_SizeR(winname: *const c_char, size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_resizeWindow_const_StringR_int_int(winname: *const c_char, width: i32, height: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_saveWindowParameters_const_StringR(window_name: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_selectROI_const_StringR_const__InputArrayR(window_name: *const c_char, img: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_selectROI_const_StringR_const__InputArrayR_bool_bool(window_name: *const c_char, img: *const c_void, show_crosshair: bool, from_center: bool, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_selectROI_const__InputArrayR(img: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_selectROI_const__InputArrayR_bool_bool(img: *const c_void, show_crosshair: bool, from_center: bool, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_selectROIs_const_StringR_const__InputArrayR_vectorLRectGR(window_name: *const c_char, img: *const c_void, bounding_boxes: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_selectROIs_const_StringR_const__InputArrayR_vectorLRectGR_bool_bool(window_name: *const c_char, img: *const c_void, bounding_boxes: *mut c_void, show_crosshair: bool, from_center: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_setMouseCallback_const_StringR_MouseCallback_voidX(winname: *const c_char, on_mouse: Option<unsafe extern "C" fn(i32, i32, i32, i32, *mut c_void) -> ()>, userdata: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_setOpenGlContext_const_StringR(winname: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_setOpenGlDrawCallback_const_StringR_OpenGlDrawCallback_voidX(winname: *const c_char, on_opengl_draw: Option<unsafe extern "C" fn(*mut c_void) -> ()>, userdata: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_setTrackbarMax_const_StringR_const_StringR_int(trackbarname: *const c_char, winname: *const c_char, maxval: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_setTrackbarMin_const_StringR_const_StringR_int(trackbarname: *const c_char, winname: *const c_char, minval: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_setTrackbarPos_const_StringR_const_StringR_int(trackbarname: *const c_char, winname: *const c_char, pos: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_setWindowProperty_const_StringR_int_double(winname: *const c_char, prop_id: i32, prop_value: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_setWindowTitle_const_StringR_const_StringR(winname: *const c_char, title: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_startLoop_int__X__int__charXX__int_charXX(pt2_func: Option<unsafe extern "C" fn(i32, *mut *mut c_char) -> i32>, argc: i32, argv: *mut *mut c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_startWindowThread(ocvrs_return: *mut Result<i32>);
pub fn cv_stopLoop(ocvrs_return: *mut ResultVoid);
pub fn cv_updateWindow_const_StringR(winname: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_waitKey(ocvrs_return: *mut Result<i32>);
pub fn cv_waitKeyEx(ocvrs_return: *mut Result<i32>);
pub fn cv_waitKeyEx_int(delay: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_waitKey_int(delay: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_QtFont_propNameFont_const(instance: *const c_void) -> *mut c_void;
pub fn cv_QtFont_propColor_const(instance: *const c_void, ocvrs_return: *mut core::Scalar);
pub fn cv_QtFont_propColor_const_Scalar(instance: *mut c_void, val: *const core::Scalar);
pub fn cv_QtFont_propFont_face_const(instance: *const c_void) -> i32;
pub fn cv_QtFont_propFont_face_const_int(instance: *mut c_void, val: i32);
pub fn cv_QtFont_propAscii_const(instance: *const c_void) -> *const i32;
pub fn cv_QtFont_propGreek_const(instance: *const c_void) -> *const i32;
pub fn cv_QtFont_propCyrillic_const(instance: *const c_void) -> *const i32;
pub fn cv_QtFont_propHscale_const(instance: *const c_void) -> f32;
pub fn cv_QtFont_propHscale_const_float(instance: *mut c_void, val: f32);
pub fn cv_QtFont_propVscale_const(instance: *const c_void) -> f32;
pub fn cv_QtFont_propVscale_const_float(instance: *mut c_void, val: f32);
pub fn cv_QtFont_propShear_const(instance: *const c_void) -> f32;
pub fn cv_QtFont_propShear_const_float(instance: *mut c_void, val: f32);
pub fn cv_QtFont_propThickness_const(instance: *const c_void) -> i32;
pub fn cv_QtFont_propThickness_const_int(instance: *mut c_void, val: i32);
pub fn cv_QtFont_propDx_const(instance: *const c_void) -> f32;
pub fn cv_QtFont_propDx_const_float(instance: *mut c_void, val: f32);
pub fn cv_QtFont_propLine_type_const(instance: *const c_void) -> i32;
pub fn cv_QtFont_propLine_type_const_int(instance: *mut c_void, val: i32);
pub fn cv_QtFont_delete(instance: *mut c_void);
