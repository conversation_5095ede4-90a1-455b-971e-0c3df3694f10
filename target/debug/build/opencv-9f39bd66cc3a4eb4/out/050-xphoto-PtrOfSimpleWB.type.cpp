extern "C" {
	const cv::xphoto::SimpleWB* cv_PtrLcv_xphoto_SimpleWBG_getInnerPtr_const(const cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			return instance->get();
	}
	
	cv::xphoto::SimpleWB* cv_PtrLcv_xphoto_SimpleWBG_getInnerPtrMut(cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_SimpleWBG_delete(cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_SimpleWBG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::xphoto::WhiteBalancer>* cv_PtrLcv_xphoto_SimpleWBG_to_PtrOfWhiteBalancer(cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			return new cv::Ptr<cv::xphoto::WhiteBalancer>(instance->dynamicCast<cv::xphoto::WhiteBalancer>());
	}
	
}

