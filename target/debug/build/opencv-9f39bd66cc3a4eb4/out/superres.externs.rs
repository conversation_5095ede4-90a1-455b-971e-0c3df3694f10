pub fn cv_superres_createFrameSource_Camera(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createFrameSource_Camera_int(device_id: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createFrameSource_Empty(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createFrameSource_Video_CUDA_const_StringR(file_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createFrameSource_Video_const_StringR(file_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createOptFlow_Brox_CUDA(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createOptFlow_DualTVL1(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createOptFlow_DualTVL1_CUDA(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createOptFlow_Farneback(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createOptFlow_Farneback_CUDA(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createOptFlow_PyrLK_CUDA(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createSuperResolution_BTVL1(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_createSuperResolution_BTVL1_CUDA(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_BroxOpticalFlow_getAlpha_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_BroxOpticalFlow_setAlpha_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_BroxOpticalFlow_getGamma_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_BroxOpticalFlow_setGamma_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_BroxOpticalFlow_getScaleFactor_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_BroxOpticalFlow_setScaleFactor_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_BroxOpticalFlow_getInnerIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_BroxOpticalFlow_setInnerIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_BroxOpticalFlow_getOuterIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_BroxOpticalFlow_setOuterIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_BroxOpticalFlow_getSolverIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_BroxOpticalFlow_setSolverIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_BroxOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_BroxOpticalFlow_to_SuperRes_DenseOpticalFlowExt(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_BroxOpticalFlow_delete(instance: *mut c_void);
pub fn cv_superres_DenseOpticalFlowExt_calc_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, flow1: *const c_void, flow2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DenseOpticalFlowExt_calc_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, flow1: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DenseOpticalFlowExt_collectGarbage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DenseOpticalFlowExt_to_SuperRes_BroxOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_DenseOpticalFlowExt_to_SuperRes_DualTVL1OpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_DenseOpticalFlowExt_to_SuperRes_FarnebackOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_DenseOpticalFlowExt_to_SuperRes_PyrLKOpticalFlow(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_DenseOpticalFlowExt_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_DenseOpticalFlowExt_delete(instance: *mut c_void);
pub fn cv_superres_DualTVL1OpticalFlow_getTau_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_DualTVL1OpticalFlow_setTau_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DualTVL1OpticalFlow_getLambda_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_DualTVL1OpticalFlow_setLambda_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DualTVL1OpticalFlow_getTheta_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_DualTVL1OpticalFlow_setTheta_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DualTVL1OpticalFlow_getScalesNumber_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_DualTVL1OpticalFlow_setScalesNumber_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DualTVL1OpticalFlow_getWarpingsNumber_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_DualTVL1OpticalFlow_setWarpingsNumber_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DualTVL1OpticalFlow_getEpsilon_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_DualTVL1OpticalFlow_setEpsilon_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DualTVL1OpticalFlow_getIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_DualTVL1OpticalFlow_setIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DualTVL1OpticalFlow_getUseInitialFlow_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_superres_DualTVL1OpticalFlow_setUseInitialFlow_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_DualTVL1OpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_DualTVL1OpticalFlow_to_SuperRes_DenseOpticalFlowExt(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_DualTVL1OpticalFlow_delete(instance: *mut c_void);
pub fn cv_superres_FarnebackOpticalFlow_getPyrScale_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_FarnebackOpticalFlow_setPyrScale_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FarnebackOpticalFlow_getLevelsNumber_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_FarnebackOpticalFlow_setLevelsNumber_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FarnebackOpticalFlow_getWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_FarnebackOpticalFlow_setWindowSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FarnebackOpticalFlow_getIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_FarnebackOpticalFlow_setIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FarnebackOpticalFlow_getPolyN_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_FarnebackOpticalFlow_setPolyN_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FarnebackOpticalFlow_getPolySigma_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_FarnebackOpticalFlow_setPolySigma_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FarnebackOpticalFlow_getFlags_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_FarnebackOpticalFlow_setFlags_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FarnebackOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_FarnebackOpticalFlow_to_SuperRes_DenseOpticalFlowExt(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_FarnebackOpticalFlow_delete(instance: *mut c_void);
pub fn cv_superres_FrameSource_nextFrame_const__OutputArrayR(instance: *mut c_void, frame: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FrameSource_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_FrameSource_to_SuperRes_SuperResolution(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_FrameSource_delete(instance: *mut c_void);
pub fn cv_superres_PyrLKOpticalFlow_getWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_PyrLKOpticalFlow_setWindowSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_PyrLKOpticalFlow_getMaxLevel_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_PyrLKOpticalFlow_setMaxLevel_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_PyrLKOpticalFlow_getIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_PyrLKOpticalFlow_setIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_PyrLKOpticalFlow_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_PyrLKOpticalFlow_to_SuperRes_DenseOpticalFlowExt(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_PyrLKOpticalFlow_delete(instance: *mut c_void);
pub fn cv_superres_SuperResolution_setInput_const_PtrLFrameSourceGR(instance: *mut c_void, frame_source: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_nextFrame_const__OutputArrayR(instance: *mut c_void, frame: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_collectGarbage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getScale_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_SuperResolution_setScale_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_SuperResolution_setIterations_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getTau_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_SuperResolution_setTau_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getLambda_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_SuperResolution_setLambda_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getAlpha_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_SuperResolution_setAlpha_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getKernelSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_SuperResolution_setKernelSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getBlurKernelSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_SuperResolution_setBlurKernelSize_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getBlurSigma_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_superres_SuperResolution_setBlurSigma_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getTemporalAreaRadius_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_superres_SuperResolution_setTemporalAreaRadius_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_getOpticalFlow_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_superres_SuperResolution_setOpticalFlow_const_PtrLDenseOpticalFlowExtGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_superres_SuperResolution_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_SuperResolution_to_SuperRes_FrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_superres_SuperResolution_delete(instance: *mut c_void);
