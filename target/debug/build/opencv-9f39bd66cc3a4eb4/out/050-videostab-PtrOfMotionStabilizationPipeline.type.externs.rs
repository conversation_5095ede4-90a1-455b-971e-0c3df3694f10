pub fn cv_PtrLcv_videostab_MotionStabilizationPipelineG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionStabilizationPipelineG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionStabilizationPipelineG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_MotionStabilizationPipelineG_to_PtrOfIMotionStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionStabilizationPipelineG_new_const_MotionStabilizationPipeline(val: *mut c_void) -> *mut c_void;
