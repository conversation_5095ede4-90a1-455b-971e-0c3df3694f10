pub fn cv_videostab_calcBlurriness_const_MatR(frame: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_calcFlowMask_const_MatR_const_MatR_const_MatR_float_const_MatR_const_MatR_MatR(flow_x: *const c_void, flow_y: *const c_void, errors: *const c_void, max_error: f32, mask0: *const c_void, mask1: *const c_void, flow_mask: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_completeFrameAccordingToFlow_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR_float_MatR_MatR(flow_mask: *const c_void, flow_x: *const c_void, flow_y: *const c_void, frame1: *const c_void, mask1: *const c_void, dist_thresh: f32, frame0: *mut c_void, mask0: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ensureInclusionConstraint_const_MatR_Size_float(m: *const c_void, size: *const core::Size, trim_ratio: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_estimateGlobalMotionLeastSquares_const__InputOutputArrayR_const__InputOutputArrayR(points0: *const c_void, points1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_estimateGlobalMotionLeastSquares_const__InputOutputArrayR_const__InputOutputArrayR_int_floatX(points0: *const c_void, points1: *const c_void, model: i32, rmse: *mut f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_estimateGlobalMotionRansac_const__InputArrayR_const__InputArrayR(points0: *const c_void, points1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_estimateGlobalMotionRansac_const__InputArrayR_const__InputArrayR_int_const_RansacParamsR_floatX_intX(points0: *const c_void, points1: *const c_void, model: i32, params: *const c_void, rmse: *mut f32, ninliers: *mut i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_estimateOptimalTrimRatio_const_MatR_Size(m: *const c_void, size: *const core::Size, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_getMotion_int_int_const_vectorLMatGR(from: i32, to: i32, motions: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ColorAverageInpainter_inpaint_int_MatR_MatR(instance: *mut c_void, idx: i32, frame: *mut c_void, mask: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ColorAverageInpainter_to_InpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ColorAverageInpainter_delete(instance: *mut c_void);
pub fn cv_videostab_ColorInpainter_ColorInpainter_int_double(method: i32, radius: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ColorInpainter_ColorInpainter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ColorInpainter_inpaint_int_MatR_MatR(instance: *mut c_void, idx: i32, frame: *mut c_void, mask: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ColorInpainter_to_InpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ColorInpainter_delete(instance: *mut c_void);
pub fn cv_videostab_ConsistentMosaicInpainter_ConsistentMosaicInpainter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ConsistentMosaicInpainter_setStdevThresh_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ConsistentMosaicInpainter_stdevThresh_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_ConsistentMosaicInpainter_inpaint_int_MatR_MatR(instance: *mut c_void, idx: i32, frame: *mut c_void, mask: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ConsistentMosaicInpainter_to_InpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ConsistentMosaicInpainter_delete(instance: *mut c_void);
pub fn cv_videostab_DeblurerBase_setRadius_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_DeblurerBase_radius_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_DeblurerBase_deblur_int_MatR_const_RangeR(instance: *mut c_void, idx: i32, frame: *mut c_void, range: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_DeblurerBase_setFrames_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_DeblurerBase_frames_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_DeblurerBase_setMotions_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_DeblurerBase_motions_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_DeblurerBase_setBlurrinessRates_const_vectorLfloatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_DeblurerBase_blurrinessRates_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_DeblurerBase_to_NullDeblurer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_DeblurerBase_to_WeightingDeblurer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_DeblurerBase_delete(instance: *mut c_void);
pub fn cv_videostab_FastMarchingMethod_FastMarchingMethod(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_FastMarchingMethod_distanceMap_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_FastMarchingMethod_delete(instance: *mut c_void);
pub fn cv_videostab_FromFileMotionReader_FromFileMotionReader_const_StringR(path: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_FromFileMotionReader_estimate_const_MatR_const_MatR_boolX(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ok: *mut bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_FromFileMotionReader_estimate_const_MatR_const_MatR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_FromFileMotionReader_to_ImageMotionEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_FromFileMotionReader_delete(instance: *mut c_void);
pub fn cv_videostab_GaussianMotionFilter_GaussianMotionFilter_int_float(radius: i32, stdev: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_GaussianMotionFilter_GaussianMotionFilter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_GaussianMotionFilter_setParams_int_float(instance: *mut c_void, radius: i32, stdev: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_GaussianMotionFilter_setParams_int(instance: *mut c_void, radius: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_GaussianMotionFilter_radius_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_GaussianMotionFilter_stdev_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_GaussianMotionFilter_stabilize_int_const_vectorLMatGR_const_RangeR(instance: *mut c_void, idx: i32, motions: *const c_void, range: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_GaussianMotionFilter_to_IMotionStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_GaussianMotionFilter_to_MotionFilterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_GaussianMotionFilter_delete(instance: *mut c_void);
pub fn cv_videostab_IDenseOptFlowEstimator_run_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, flow_x: *const c_void, flow_y: *const c_void, errors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_IDenseOptFlowEstimator_delete(instance: *mut c_void);
pub fn cv_videostab_IFrameSource_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_IFrameSource_nextFrame(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_IFrameSource_to_MaskFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IFrameSource_to_NullFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IFrameSource_to_OnePassStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IFrameSource_to_TwoPassStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IFrameSource_to_VideoFileSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IFrameSource_delete(instance: *mut c_void);
pub fn cv_videostab_ILog_print_const_charX(instance: *mut c_void, format: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ILog_to_LogToStdout(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ILog_to_NullLog(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ILog_delete(instance: *mut c_void);
pub fn cv_videostab_IMotionStabilizer_stabilize_int_const_vectorLMatGR_const_RangeR_MatX(instance: *mut c_void, size: i32, motions: *const c_void, range: *const c_void, stabilization_motions: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_IMotionStabilizer_to_GaussianMotionFilter(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IMotionStabilizer_to_LpMotionStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IMotionStabilizer_to_MotionFilterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IMotionStabilizer_to_MotionStabilizationPipeline(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IMotionStabilizer_delete(instance: *mut c_void);
pub fn cv_videostab_IOutlierRejector_process_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, frame_size: *const core::Size, points0: *const c_void, points1: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_IOutlierRejector_to_NullOutlierRejector(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IOutlierRejector_to_TranslationBasedLocalOutlierRejector(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_IOutlierRejector_delete(instance: *mut c_void);
pub fn cv_videostab_ISparseOptFlowEstimator_run_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, points0: *const c_void, points1: *const c_void, status: *const c_void, errors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ISparseOptFlowEstimator_to_SparsePyrLkOptFlowEstimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ISparseOptFlowEstimator_delete(instance: *mut c_void);
pub fn cv_videostab_ImageMotionEstimatorBase_setMotionModel_MotionModel(instance: *mut c_void, val: crate::videostab::MotionModel, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ImageMotionEstimatorBase_motionModel_const(instance: *const c_void, ocvrs_return: *mut Result<crate::videostab::MotionModel>);
pub fn cv_videostab_ImageMotionEstimatorBase_setFrameMask_const__InputArrayR(instance: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ImageMotionEstimatorBase_estimate_const_MatR_const_MatR_boolX(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ok: *mut bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ImageMotionEstimatorBase_estimate_const_MatR_const_MatR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ImageMotionEstimatorBase_to_FromFileMotionReader(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ImageMotionEstimatorBase_to_KeypointBasedMotionEstimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ImageMotionEstimatorBase_to_ToFileMotionWriter(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ImageMotionEstimatorBase_delete(instance: *mut c_void);
pub fn cv_videostab_InpainterBase_setRadius_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpainterBase_radius_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_InpainterBase_setMotionModel_MotionModel(instance: *mut c_void, val: crate::videostab::MotionModel, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpainterBase_motionModel_const(instance: *const c_void, ocvrs_return: *mut Result<crate::videostab::MotionModel>);
pub fn cv_videostab_InpainterBase_inpaint_int_MatR_MatR(instance: *mut c_void, idx: i32, frame: *mut c_void, mask: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpainterBase_setFrames_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpainterBase_frames_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_InpainterBase_setMotions_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpainterBase_motions_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_InpainterBase_setStabilizedFrames_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpainterBase_stabilizedFrames_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_InpainterBase_setStabilizationMotions_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpainterBase_stabilizationMotions_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_InpainterBase_to_ColorAverageInpainter(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_InpainterBase_to_ColorInpainter(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_InpainterBase_to_ConsistentMosaicInpainter(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_InpainterBase_to_InpaintingPipeline(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_InpainterBase_to_MotionInpainter(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_InpainterBase_to_NullInpainter(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_InpainterBase_delete(instance: *mut c_void);
pub fn cv_videostab_InpaintingPipeline_pushBack_PtrLInpainterBaseG(instance: *mut c_void, inpainter: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpaintingPipeline_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_videostab_InpaintingPipeline_setRadius_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpaintingPipeline_setMotionModel_MotionModel(instance: *mut c_void, val: crate::videostab::MotionModel, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpaintingPipeline_setFrames_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpaintingPipeline_setMotions_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpaintingPipeline_setStabilizedFrames_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpaintingPipeline_setStabilizationMotions_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpaintingPipeline_inpaint_int_MatR_MatR(instance: *mut c_void, idx: i32, frame: *mut c_void, mask: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_InpaintingPipeline_to_InpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_InpaintingPipeline_delete(instance: *mut c_void);
pub fn cv_videostab_KeypointBasedMotionEstimator_KeypointBasedMotionEstimator_PtrLMotionEstimatorBaseG(estimator: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_KeypointBasedMotionEstimator_setMotionModel_MotionModel(instance: *mut c_void, val: crate::videostab::MotionModel, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_KeypointBasedMotionEstimator_motionModel_const(instance: *const c_void, ocvrs_return: *mut Result<crate::videostab::MotionModel>);
pub fn cv_videostab_KeypointBasedMotionEstimator_setDetector_PtrLFeature2DG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_KeypointBasedMotionEstimator_detector_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_KeypointBasedMotionEstimator_setOpticalFlowEstimator_PtrLISparseOptFlowEstimatorG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_KeypointBasedMotionEstimator_opticalFlowEstimator_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_KeypointBasedMotionEstimator_setOutlierRejector_PtrLIOutlierRejectorG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_KeypointBasedMotionEstimator_outlierRejector_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_KeypointBasedMotionEstimator_setFrameMask_const__InputArrayR(instance: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_KeypointBasedMotionEstimator_estimate_const_MatR_const_MatR_boolX(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ok: *mut bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_KeypointBasedMotionEstimator_estimate_const_MatR_const_MatR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_KeypointBasedMotionEstimator_estimate_const__InputArrayR_const__InputArrayR_boolX(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ok: *mut bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_KeypointBasedMotionEstimator_estimate_const__InputArrayR_const__InputArrayR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_KeypointBasedMotionEstimator_to_ImageMotionEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_KeypointBasedMotionEstimator_delete(instance: *mut c_void);
pub fn cv_videostab_LogToStdout_print_const_charX(instance: *mut c_void, format: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LogToStdout_to_ILog(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_LogToStdout_delete(instance: *mut c_void);
pub fn cv_videostab_LpMotionStabilizer_LpMotionStabilizer_MotionModel(model: crate::videostab::MotionModel, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_LpMotionStabilizer_LpMotionStabilizer(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_LpMotionStabilizer_setMotionModel_MotionModel(instance: *mut c_void, val: crate::videostab::MotionModel, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LpMotionStabilizer_motionModel_const(instance: *const c_void, ocvrs_return: *mut Result<crate::videostab::MotionModel>);
pub fn cv_videostab_LpMotionStabilizer_setFrameSize_Size(instance: *mut c_void, val: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LpMotionStabilizer_frameSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_videostab_LpMotionStabilizer_setTrimRatio_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LpMotionStabilizer_trimRatio_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_LpMotionStabilizer_setWeight1_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LpMotionStabilizer_weight1_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_LpMotionStabilizer_setWeight2_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LpMotionStabilizer_weight2_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_LpMotionStabilizer_setWeight3_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LpMotionStabilizer_weight3_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_LpMotionStabilizer_setWeight4_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LpMotionStabilizer_weight4_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_LpMotionStabilizer_stabilize_int_const_vectorLMatGR_const_RangeR_MatX(instance: *mut c_void, size: i32, motions: *const c_void, range: *const c_void, stabilization_motions: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_LpMotionStabilizer_to_IMotionStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_LpMotionStabilizer_delete(instance: *mut c_void);
pub fn cv_videostab_MaskFrameSource_MaskFrameSource_const_PtrLIFrameSourceGR(source: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MaskFrameSource_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MaskFrameSource_nextFrame(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MaskFrameSource_to_IFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MaskFrameSource_delete(instance: *mut c_void);
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressor_suppress_int_const_MatR_MatR(instance: *mut c_void, idx: i32, frame: *const c_void, result: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressor_to_MoreAccurateMotionWobbleSuppressorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressor_to_WobbleSuppressorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressor_delete(instance: *mut c_void);
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressorBase_setPeriod_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressorBase_period_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressorBase_to_MoreAccurateMotionWobbleSuppressor(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressorBase_to_WobbleSuppressorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MoreAccurateMotionWobbleSuppressorBase_delete(instance: *mut c_void);
pub fn cv_videostab_MotionEstimatorBase_setMotionModel_MotionModel(instance: *mut c_void, val: crate::videostab::MotionModel, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionEstimatorBase_motionModel_const(instance: *const c_void, ocvrs_return: *mut Result<crate::videostab::MotionModel>);
pub fn cv_videostab_MotionEstimatorBase_estimate_const__InputArrayR_const__InputArrayR_boolX(instance: *mut c_void, points0: *const c_void, points1: *const c_void, ok: *mut bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorBase_estimate_const__InputArrayR_const__InputArrayR(instance: *mut c_void, points0: *const c_void, points1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorBase_to_MotionEstimatorL1(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MotionEstimatorBase_to_MotionEstimatorRansacL2(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MotionEstimatorBase_delete(instance: *mut c_void);
pub fn cv_videostab_MotionEstimatorL1_MotionEstimatorL1_MotionModel(model: crate::videostab::MotionModel, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorL1_MotionEstimatorL1(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorL1_estimate_const__InputArrayR_const__InputArrayR_boolX(instance: *mut c_void, points0: *const c_void, points1: *const c_void, ok: *mut bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorL1_estimate_const__InputArrayR_const__InputArrayR(instance: *mut c_void, points0: *const c_void, points1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorL1_to_MotionEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MotionEstimatorL1_delete(instance: *mut c_void);
pub fn cv_videostab_MotionEstimatorRansacL2_MotionEstimatorRansacL2_MotionModel(model: crate::videostab::MotionModel, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorRansacL2_MotionEstimatorRansacL2(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorRansacL2_setRansacParams_const_RansacParamsR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionEstimatorRansacL2_ransacParams_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorRansacL2_setMinInlierRatio_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionEstimatorRansacL2_minInlierRatio_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_MotionEstimatorRansacL2_estimate_const__InputArrayR_const__InputArrayR_boolX(instance: *mut c_void, points0: *const c_void, points1: *const c_void, ok: *mut bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorRansacL2_estimate_const__InputArrayR_const__InputArrayR(instance: *mut c_void, points0: *const c_void, points1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionEstimatorRansacL2_to_MotionEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MotionEstimatorRansacL2_delete(instance: *mut c_void);
pub fn cv_videostab_MotionFilterBase_stabilize_int_const_vectorLMatGR_const_RangeR(instance: *mut c_void, idx: i32, motions: *const c_void, range: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionFilterBase_stabilize_int_const_vectorLMatGR_const_RangeR_MatX(instance: *mut c_void, size: i32, motions: *const c_void, range: *const c_void, stabilization_motions: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionFilterBase_to_GaussianMotionFilter(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MotionFilterBase_to_IMotionStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MotionFilterBase_delete(instance: *mut c_void);
pub fn cv_videostab_MotionInpainter_MotionInpainter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionInpainter_setOptFlowEstimator_PtrLIDenseOptFlowEstimatorG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionInpainter_optFlowEstimator_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_MotionInpainter_setFlowErrorThreshold_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionInpainter_flowErrorThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_MotionInpainter_setDistThreshold_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionInpainter_distThresh_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_MotionInpainter_setBorderMode_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionInpainter_borderMode_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_MotionInpainter_inpaint_int_MatR_MatR(instance: *mut c_void, idx: i32, frame: *mut c_void, mask: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionInpainter_to_InpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MotionInpainter_delete(instance: *mut c_void);
pub fn cv_videostab_MotionStabilizationPipeline_pushBack_PtrLIMotionStabilizerG(instance: *mut c_void, stabilizer: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionStabilizationPipeline_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_videostab_MotionStabilizationPipeline_stabilize_int_const_vectorLMatGR_const_RangeR_MatX(instance: *mut c_void, size: i32, motions: *const c_void, range: *const c_void, stabilization_motions: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_MotionStabilizationPipeline_to_IMotionStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_MotionStabilizationPipeline_delete(instance: *mut c_void);
pub fn cv_videostab_NullDeblurer_deblur_int_MatR_const_RangeR(instance: *mut c_void, unnamed: i32, unnamed_1: *mut c_void, unnamed_2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_NullDeblurer_to_DeblurerBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_NullDeblurer_delete(instance: *mut c_void);
pub fn cv_videostab_NullFrameSource_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_NullFrameSource_nextFrame(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_NullFrameSource_to_IFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_NullFrameSource_delete(instance: *mut c_void);
pub fn cv_videostab_NullInpainter_inpaint_int_MatR_MatR(instance: *mut c_void, unnamed: i32, unnamed_1: *mut c_void, unnamed_2: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_NullInpainter_to_InpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_NullInpainter_delete(instance: *mut c_void);
pub fn cv_videostab_NullLog_print_const_charX(instance: *mut c_void, unnamed: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_NullLog_to_ILog(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_NullLog_delete(instance: *mut c_void);
pub fn cv_videostab_NullOutlierRejector_process_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, frame_size: *const core::Size, points0: *const c_void, points1: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_NullOutlierRejector_to_IOutlierRejector(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_NullOutlierRejector_delete(instance: *mut c_void);
pub fn cv_videostab_NullWobbleSuppressor_suppress_int_const_MatR_MatR(instance: *mut c_void, idx: i32, frame: *const c_void, result: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_NullWobbleSuppressor_to_WobbleSuppressorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_NullWobbleSuppressor_delete(instance: *mut c_void);
pub fn cv_videostab_OnePassStabilizer_OnePassStabilizer(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_OnePassStabilizer_setMotionFilter_PtrLMotionFilterBaseG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_OnePassStabilizer_motionFilter_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_OnePassStabilizer_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_OnePassStabilizer_nextFrame(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_OnePassStabilizer_to_IFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_OnePassStabilizer_to_StabilizerBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_OnePassStabilizer_delete(instance: *mut c_void);
pub fn cv_videostab_PyrLkOptFlowEstimatorBase_PyrLkOptFlowEstimatorBase(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_PyrLkOptFlowEstimatorBase_setWinSize_Size(instance: *mut c_void, val: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_PyrLkOptFlowEstimatorBase_winSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_videostab_PyrLkOptFlowEstimatorBase_setMaxLevel_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_PyrLkOptFlowEstimatorBase_maxLevel_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_PyrLkOptFlowEstimatorBase_to_SparsePyrLkOptFlowEstimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_PyrLkOptFlowEstimatorBase_delete(instance: *mut c_void);
pub fn cv_videostab_RansacParams_RansacParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_RansacParams_RansacParams_int_float_float_float(size: i32, thresh: f32, eps: f32, prob: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_RansacParams_niters_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_RansacParams_default2dMotion_MotionModel(model: crate::videostab::MotionModel, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_RansacParams_propSize_const(instance: *const c_void) -> i32;
pub fn cv_videostab_RansacParams_propSize_const_int(instance: *mut c_void, val: i32);
pub fn cv_videostab_RansacParams_propThresh_const(instance: *const c_void) -> f32;
pub fn cv_videostab_RansacParams_propThresh_const_float(instance: *mut c_void, val: f32);
pub fn cv_videostab_RansacParams_propEps_const(instance: *const c_void) -> f32;
pub fn cv_videostab_RansacParams_propEps_const_float(instance: *mut c_void, val: f32);
pub fn cv_videostab_RansacParams_propProb_const(instance: *const c_void) -> f32;
pub fn cv_videostab_RansacParams_propProb_const_float(instance: *mut c_void, val: f32);
pub fn cv_videostab_RansacParams_delete(instance: *mut c_void);
pub fn cv_videostab_SparsePyrLkOptFlowEstimator_run_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, points0: *const c_void, points1: *const c_void, status: *const c_void, errors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_SparsePyrLkOptFlowEstimator_to_ISparseOptFlowEstimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_SparsePyrLkOptFlowEstimator_to_PyrLkOptFlowEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_SparsePyrLkOptFlowEstimator_delete(instance: *mut c_void);
pub fn cv_videostab_StabilizerBase_setLog_PtrLILogG(instance: *mut c_void, ilog: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_log_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_StabilizerBase_setRadius_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_radius_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_StabilizerBase_setFrameSource_PtrLIFrameSourceG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_frameSource_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_StabilizerBase_setMaskSource_const_PtrLIFrameSourceGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_maskSource_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_StabilizerBase_setMotionEstimator_PtrLImageMotionEstimatorBaseG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_motionEstimator_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_StabilizerBase_setDeblurer_PtrLDeblurerBaseG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_deblurrer_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_StabilizerBase_setTrimRatio_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_trimRatio_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_StabilizerBase_setCorrectionForInclusion_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_doCorrectionForInclusion_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_videostab_StabilizerBase_setBorderMode_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_borderMode_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_StabilizerBase_setInpainter_PtrLInpainterBaseG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_StabilizerBase_inpainter_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_StabilizerBase_to_OnePassStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_StabilizerBase_to_TwoPassStabilizer(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_StabilizerBase_delete(instance: *mut c_void);
pub fn cv_videostab_ToFileMotionWriter_ToFileMotionWriter_const_StringR_PtrLImageMotionEstimatorBaseG(path: *const c_char, estimator: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ToFileMotionWriter_setMotionModel_MotionModel(instance: *mut c_void, val: crate::videostab::MotionModel, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ToFileMotionWriter_motionModel_const(instance: *const c_void, ocvrs_return: *mut Result<crate::videostab::MotionModel>);
pub fn cv_videostab_ToFileMotionWriter_setFrameMask_const__InputArrayR(instance: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_ToFileMotionWriter_estimate_const_MatR_const_MatR_boolX(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ok: *mut bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ToFileMotionWriter_estimate_const_MatR_const_MatR(instance: *mut c_void, frame0: *const c_void, frame1: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_ToFileMotionWriter_to_ImageMotionEstimatorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_ToFileMotionWriter_delete(instance: *mut c_void);
pub fn cv_videostab_TranslationBasedLocalOutlierRejector_TranslationBasedLocalOutlierRejector(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_TranslationBasedLocalOutlierRejector_setCellSize_Size(instance: *mut c_void, val: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_TranslationBasedLocalOutlierRejector_cellSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_videostab_TranslationBasedLocalOutlierRejector_setRansacParams_RansacParams(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_TranslationBasedLocalOutlierRejector_ransacParams_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_TranslationBasedLocalOutlierRejector_process_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, frame_size: *const core::Size, points0: *const c_void, points1: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_TranslationBasedLocalOutlierRejector_to_IOutlierRejector(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_TranslationBasedLocalOutlierRejector_delete(instance: *mut c_void);
pub fn cv_videostab_TwoPassStabilizer_TwoPassStabilizer(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_TwoPassStabilizer_setMotionStabilizer_PtrLIMotionStabilizerG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_TwoPassStabilizer_motionStabilizer_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_TwoPassStabilizer_setWobbleSuppressor_PtrLWobbleSuppressorBaseG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_TwoPassStabilizer_wobbleSuppressor_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_TwoPassStabilizer_setEstimateTrimRatio_bool(instance: *mut c_void, val: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_TwoPassStabilizer_mustEstimateTrimaRatio_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_videostab_TwoPassStabilizer_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_TwoPassStabilizer_nextFrame(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_TwoPassStabilizer_to_IFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_TwoPassStabilizer_to_StabilizerBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_TwoPassStabilizer_delete(instance: *mut c_void);
pub fn cv_videostab_VideoFileSource_VideoFileSource_const_StringR_bool(path: *const c_char, volatile_frame: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_VideoFileSource_VideoFileSource_const_StringR(path: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_VideoFileSource_reset(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_VideoFileSource_nextFrame(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_VideoFileSource_width(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_VideoFileSource_height(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_VideoFileSource_count(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_VideoFileSource_fps(instance: *mut c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_videostab_VideoFileSource_to_IFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_VideoFileSource_delete(instance: *mut c_void);
pub fn cv_videostab_WeightingDeblurer_WeightingDeblurer(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_WeightingDeblurer_setSensitivity_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_WeightingDeblurer_sensitivity_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_videostab_WeightingDeblurer_deblur_int_MatR_const_RangeR(instance: *mut c_void, idx: i32, frame: *mut c_void, range: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_WeightingDeblurer_to_DeblurerBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_WeightingDeblurer_delete(instance: *mut c_void);
pub fn cv_videostab_WobbleSuppressorBase_setMotionEstimator_PtrLImageMotionEstimatorBaseG(instance: *mut c_void, val: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_WobbleSuppressorBase_motionEstimator_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_WobbleSuppressorBase_suppress_int_const_MatR_MatR(instance: *mut c_void, idx: i32, frame: *const c_void, result: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_WobbleSuppressorBase_setFrameCount_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_WobbleSuppressorBase_frameCount_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_videostab_WobbleSuppressorBase_setMotions_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_WobbleSuppressorBase_motions_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_WobbleSuppressorBase_setMotions2_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_WobbleSuppressorBase_motions2_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_WobbleSuppressorBase_setStabilizationMotions_const_vectorLMatGR(instance: *mut c_void, val: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_videostab_WobbleSuppressorBase_stabilizationMotions_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videostab_WobbleSuppressorBase_to_MoreAccurateMotionWobbleSuppressor(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_WobbleSuppressorBase_to_MoreAccurateMotionWobbleSuppressorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_WobbleSuppressorBase_to_NullWobbleSuppressor(instance: *mut c_void) -> *mut c_void;
pub fn cv_videostab_WobbleSuppressorBase_delete(instance: *mut c_void);
