pub fn cv_phase_unwrapping_HistogramPhaseUnwrapping_create_const_ParamsR(parameters: *const crate::phase_unwrapping::HistogramPhaseUnwrapping_Params, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_phase_unwrapping_HistogramPhaseUnwrapping_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_phase_unwrapping_HistogramPhaseUnwrapping_getInverseReliabilityMap_const__OutputArrayR(instance: *mut c_void, reliability_map: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_phase_unwrapping_HistogramPhaseUnwrapping_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_phase_unwrapping_HistogramPhaseUnwrapping_to_PhaseUnwrapping(instance: *mut c_void) -> *mut c_void;
pub fn cv_phase_unwrapping_HistogramPhaseUnwrapping_delete(instance: *mut c_void);
pub fn cv_phase_unwrapping_HistogramPhaseUnwrapping_Params_Params(ocvrs_return: *mut Result<crate::phase_unwrapping::HistogramPhaseUnwrapping_Params>);
pub fn cv_phase_unwrapping_PhaseUnwrapping_unwrapPhaseMap_const__InputArrayR_const__OutputArrayR_const__InputArrayR(instance: *mut c_void, wrapped_phase_map: *const c_void, unwrapped_phase_map: *const c_void, shadow_mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_phase_unwrapping_PhaseUnwrapping_unwrapPhaseMap_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, wrapped_phase_map: *const c_void, unwrapped_phase_map: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_phase_unwrapping_PhaseUnwrapping_to_HistogramPhaseUnwrapping(instance: *mut c_void) -> *mut c_void;
pub fn cv_phase_unwrapping_PhaseUnwrapping_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_phase_unwrapping_PhaseUnwrapping_delete(instance: *mut c_void);
