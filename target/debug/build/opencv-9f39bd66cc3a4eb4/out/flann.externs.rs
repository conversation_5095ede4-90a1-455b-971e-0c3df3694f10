pub fn cvflann_flann_distance_type(ocvrs_return: *mut Result<crate::flann::flann_distance_t>);
pub fn cvflann_set_distance_type_flann_distance_t_int(distance_type: crate::flann::flann_distance_t, order: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_AutotunedIndexParams_AutotunedIndexParams_float_float_float_float(target_precision: f32, build_weight: f32, memory_weight: f32, sample_fraction: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_AutotunedIndexParams_AutotunedIndexParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_AutotunedIndexParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_AutotunedIndexParams_delete(instance: *mut c_void);
pub fn cv_flann_CompositeIndexParams_CompositeIndexParams_int_int_int_flann_centers_init_t_float(trees: i32, branching: i32, iterations: i32, centers_init: crate::flann::flann_centers_init_t, cb_index: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_CompositeIndexParams_CompositeIndexParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_CompositeIndexParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_CompositeIndexParams_delete(instance: *mut c_void);
pub fn cv_flann_HierarchicalClusteringIndexParams_HierarchicalClusteringIndexParams_int_flann_centers_init_t_int_int(branching: i32, centers_init: crate::flann::flann_centers_init_t, trees: i32, leaf_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_HierarchicalClusteringIndexParams_HierarchicalClusteringIndexParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_HierarchicalClusteringIndexParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_HierarchicalClusteringIndexParams_delete(instance: *mut c_void);
pub fn cv_flann_Index_Index(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_Index_Index_const__InputArrayR_const_IndexParamsR_flann_distance_t(features: *const c_void, params: *const c_void, dist_type: crate::flann::flann_distance_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_Index_Index_const__InputArrayR_const_IndexParamsR(features: *const c_void, params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_Index_build_const__InputArrayR_const_IndexParamsR_flann_distance_t(instance: *mut c_void, features: *const c_void, params: *const c_void, dist_type: crate::flann::flann_distance_t, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_Index_build_const__InputArrayR_const_IndexParamsR(instance: *mut c_void, features: *const c_void, params: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_Index_knnSearch_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int_const_SearchParamsR(instance: *mut c_void, query: *const c_void, indices: *const c_void, dists: *const c_void, knn: i32, params: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_Index_knnSearch_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int(instance: *mut c_void, query: *const c_void, indices: *const c_void, dists: *const c_void, knn: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_Index_radiusSearch_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double_int_const_SearchParamsR(instance: *mut c_void, query: *const c_void, indices: *const c_void, dists: *const c_void, radius: f64, max_results: i32, params: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_flann_Index_radiusSearch_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double_int(instance: *mut c_void, query: *const c_void, indices: *const c_void, dists: *const c_void, radius: f64, max_results: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_flann_Index_save_const_const_StringR(instance: *const c_void, filename: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_Index_load_const__InputArrayR_const_StringR(instance: *mut c_void, features: *const c_void, filename: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_flann_Index_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_Index_getDistance_const(instance: *const c_void, ocvrs_return: *mut Result<crate::flann::flann_distance_t>);
pub fn cv_flann_Index_getAlgorithm_const(instance: *const c_void, ocvrs_return: *mut Result<crate::flann::flann_algorithm_t>);
pub fn cv_flann_Index_delete(instance: *mut c_void);
pub fn cv_flann_IndexParams_IndexParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_IndexParams_getString_const_const_StringR_const_StringR(instance: *const c_void, key: *const c_char, default_val: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_IndexParams_getString_const_const_StringR(instance: *const c_void, key: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_IndexParams_getInt_const_const_StringR_int(instance: *const c_void, key: *const c_char, default_val: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_flann_IndexParams_getInt_const_const_StringR(instance: *const c_void, key: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_flann_IndexParams_getDouble_const_const_StringR_double(instance: *const c_void, key: *const c_char, default_val: f64, ocvrs_return: *mut Result<f64>);
pub fn cv_flann_IndexParams_getDouble_const_const_StringR(instance: *const c_void, key: *const c_char, ocvrs_return: *mut Result<f64>);
pub fn cv_flann_IndexParams_setString_const_StringR_const_StringR(instance: *mut c_void, key: *const c_char, value: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_IndexParams_setInt_const_StringR_int(instance: *mut c_void, key: *const c_char, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_IndexParams_setDouble_const_StringR_double(instance: *mut c_void, key: *const c_char, value: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_IndexParams_setFloat_const_StringR_float(instance: *mut c_void, key: *const c_char, value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_IndexParams_setBool_const_StringR_bool(instance: *mut c_void, key: *const c_char, value: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_IndexParams_setAlgorithm_int(instance: *mut c_void, value: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_IndexParams_getAll_const_vectorLStringGR_vectorLFlannIndexTypeGR_vectorLStringGR_vectorLdoubleGR(instance: *const c_void, names: *mut c_void, types: *mut c_void, str_values: *mut c_void, num_values: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_flann_IndexParams_propParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_IndexParams_propParams_voidX(instance: *mut c_void, val: *const c_void);
pub fn cv_flann_IndexParams_delete(instance: *mut c_void);
pub fn cv_flann_KDTreeIndexParams_KDTreeIndexParams_int(trees: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_KDTreeIndexParams_KDTreeIndexParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_KDTreeIndexParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_KDTreeIndexParams_delete(instance: *mut c_void);
pub fn cv_flann_KMeansIndexParams_KMeansIndexParams_int_int_flann_centers_init_t_float(branching: i32, iterations: i32, centers_init: crate::flann::flann_centers_init_t, cb_index: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_KMeansIndexParams_KMeansIndexParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_KMeansIndexParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_KMeansIndexParams_delete(instance: *mut c_void);
pub fn cv_flann_LinearIndexParams_LinearIndexParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_LinearIndexParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_LinearIndexParams_delete(instance: *mut c_void);
pub fn cv_flann_LshIndexParams_LshIndexParams_int_int_int(table_number: i32, key_size: i32, multi_probe_level: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_LshIndexParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_LshIndexParams_delete(instance: *mut c_void);
pub fn cv_flann_SavedIndexParams_SavedIndexParams_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_SavedIndexParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_SavedIndexParams_delete(instance: *mut c_void);
pub fn cv_flann_SearchParams_SearchParams_int_float_bool_bool(checks: i32, eps: f32, sorted: bool, explore_all_trees: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_SearchParams_SearchParams_int_float_bool(checks: i32, eps: f32, sorted: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_SearchParams_SearchParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_flann_SearchParams_to_IndexParams(instance: *mut c_void) -> *mut c_void;
pub fn cv_flann_SearchParams_delete(instance: *mut c_void);
