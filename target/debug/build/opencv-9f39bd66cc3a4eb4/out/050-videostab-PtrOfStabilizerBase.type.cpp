extern "C" {
	const cv::videostab::StabilizerBase* cv_PtrLcv_videostab_StabilizerBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::StabilizerBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::StabilizerBase* cv_PtrLcv_videostab_StabilizerBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::StabilizerBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_StabilizerBaseG_delete(cv::Ptr<cv::videostab::StabilizerBase>* instance) {
			delete instance;
	}
	
}

