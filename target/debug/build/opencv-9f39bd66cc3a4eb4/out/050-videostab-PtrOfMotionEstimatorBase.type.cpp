extern "C" {
	const cv::videostab::MotionEstimatorBase* cv_PtrLcv_videostab_MotionEstimatorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::MotionEstimatorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::MotionEstimatorBase* cv_PtrLcv_videostab_MotionEstimatorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::MotionEstimatorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MotionEstimatorBaseG_delete(cv::Ptr<cv::videostab::MotionEstimatorBase>* instance) {
			delete instance;
	}
	
}

