pub fn cv_videoio_registry_getBackendName_VideoCaptureAPIs(api: crate::videoio::VideoCaptureAPIs, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videoio_registry_getBackends(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videoio_registry_getCameraBackendPluginVersion_VideoCaptureAPIs_intR_intR(api: crate::videoio::VideoCaptureAPIs, version_abi: *mut i32, version_api: *mut i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videoio_registry_getCameraBackends(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videoio_registry_getStreamBackendPluginVersion_VideoCaptureAPIs_intR_intR(api: crate::videoio::VideoCaptureAPIs, version_abi: *mut i32, version_api: *mut i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videoio_registry_getStreamBackends(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videoio_registry_getWriterBackendPluginVersion_VideoCaptureAPIs_intR_intR(api: crate::videoio::VideoCaptureAPIs, version_abi: *mut i32, version_api: *mut i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videoio_registry_getWriterBackends(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_videoio_registry_hasBackend_VideoCaptureAPIs(api: crate::videoio::VideoCaptureAPIs, ocvrs_return: *mut Result<bool>);
pub fn cv_videoio_registry_isBackendBuiltIn_VideoCaptureAPIs(api: crate::videoio::VideoCaptureAPIs, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_VideoCapture(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoCapture_VideoCapture_const_StringR_int(filename: *const c_char, api_preference: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoCapture_VideoCapture_const_StringR(filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoCapture_VideoCapture_const_StringR_int_const_vectorLintGR(filename: *const c_char, api_preference: i32, params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoCapture_VideoCapture_int_int(index: i32, api_preference: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoCapture_VideoCapture_int(index: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoCapture_VideoCapture_int_int_const_vectorLintGR(index: i32, api_preference: i32, params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoCapture_open_const_StringR_int(instance: *mut c_void, filename: *const c_char, api_preference: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_open_const_StringR(instance: *mut c_void, filename: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_open_const_StringR_int_const_vectorLintGR(instance: *mut c_void, filename: *const c_char, api_preference: i32, params: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_open_int_int(instance: *mut c_void, index: i32, api_preference: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_open_int(instance: *mut c_void, index: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_open_int_int_const_vectorLintGR(instance: *mut c_void, index: i32, api_preference: i32, params: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_isOpened_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_VideoCapture_grab(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_retrieve_const__OutputArrayR_int(instance: *mut c_void, image: *const c_void, flag: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_retrieve_const__OutputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_read_const__OutputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_set_int_double(instance: *mut c_void, prop_id: i32, value: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_get_const_int(instance: *const c_void, prop_id: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_VideoCapture_getBackendName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoCapture_setExceptionMode_bool(instance: *mut c_void, enable: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_VideoCapture_getExceptionMode(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_waitAny_const_vectorLVideoCaptureGR_vectorLintGR_int64_t(streams: *const c_void, ready_index: *mut c_void, timeout_ns: i64, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_waitAny_const_vectorLVideoCaptureGR_vectorLintGR(streams: *const c_void, ready_index: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoCapture_delete(instance: *mut c_void);
pub fn cv_VideoWriter_VideoWriter(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoWriter_VideoWriter_const_StringR_int_double_Size_bool(filename: *const c_char, fourcc: i32, fps: f64, frame_size: *const core::Size, is_color: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoWriter_VideoWriter_const_StringR_int_double_Size(filename: *const c_char, fourcc: i32, fps: f64, frame_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoWriter_VideoWriter_const_StringR_int_int_double_Size_bool(filename: *const c_char, api_preference: i32, fourcc: i32, fps: f64, frame_size: *const core::Size, is_color: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoWriter_VideoWriter_const_StringR_int_int_double_Size(filename: *const c_char, api_preference: i32, fourcc: i32, fps: f64, frame_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoWriter_VideoWriter_const_StringR_int_double_const_SizeR_const_vectorLintGR(filename: *const c_char, fourcc: i32, fps: f64, frame_size: *const core::Size, params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoWriter_VideoWriter_const_StringR_int_int_double_const_SizeR_const_vectorLintGR(filename: *const c_char, api_preference: i32, fourcc: i32, fps: f64, frame_size: *const core::Size, params: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoWriter_open_const_StringR_int_double_Size_bool(instance: *mut c_void, filename: *const c_char, fourcc: i32, fps: f64, frame_size: *const core::Size, is_color: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoWriter_open_const_StringR_int_double_Size(instance: *mut c_void, filename: *const c_char, fourcc: i32, fps: f64, frame_size: *const core::Size, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoWriter_open_const_StringR_int_int_double_Size_bool(instance: *mut c_void, filename: *const c_char, api_preference: i32, fourcc: i32, fps: f64, frame_size: *const core::Size, is_color: bool, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoWriter_open_const_StringR_int_int_double_Size(instance: *mut c_void, filename: *const c_char, api_preference: i32, fourcc: i32, fps: f64, frame_size: *const core::Size, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoWriter_open_const_StringR_int_double_const_SizeR_const_vectorLintGR(instance: *mut c_void, filename: *const c_char, fourcc: i32, fps: f64, frame_size: *const core::Size, params: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoWriter_open_const_StringR_int_int_double_const_SizeR_const_vectorLintGR(instance: *mut c_void, filename: *const c_char, api_preference: i32, fourcc: i32, fps: f64, frame_size: *const core::Size, params: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoWriter_isOpened_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoWriter_release(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_VideoWriter_write_const__InputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_VideoWriter_set_int_double(instance: *mut c_void, prop_id: i32, value: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_VideoWriter_get_const_int(instance: *const c_void, prop_id: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_VideoWriter_fourcc_char_char_char_char(c1: c_char, c2: c_char, c3: c_char, c4: c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_VideoWriter_getBackendName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_VideoWriter_delete(instance: *mut c_void);
