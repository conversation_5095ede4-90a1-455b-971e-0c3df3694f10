pub fn cv_hfs_HfsSegment_setSegEgbThresholdI_float(instance: *mut c_void, c: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_hfs_HfsSegment_getSegEgbThresholdI(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_hfs_HfsSegment_setMinRegionSizeI_int(instance: *mut c_void, n: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hfs_HfsSegment_getMinRegionSizeI(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_hfs_HfsSegment_setSegEgbThresholdII_float(instance: *mut c_void, c: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_hfs_HfsSegment_getSegEgbThresholdII(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_hfs_HfsSegment_setMinRegionSizeII_int(instance: *mut c_void, n: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hfs_HfsSegment_getMinRegionSizeII(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_hfs_HfsSegment_setSpatialWeight_float(instance: *mut c_void, w: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_hfs_HfsSegment_getSpatialWeight(instance: *mut c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_hfs_HfsSegment_setSlicSpixelSize_int(instance: *mut c_void, n: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hfs_HfsSegment_getSlicSpixelSize(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_hfs_HfsSegment_setNumSlicIter_int(instance: *mut c_void, n: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hfs_HfsSegment_getNumSlicIter(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_hfs_HfsSegment_performSegmentGpu_const__InputArrayR_bool(instance: *mut c_void, src: *const c_void, if_draw: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hfs_HfsSegment_performSegmentGpu_const__InputArrayR(instance: *mut c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hfs_HfsSegment_performSegmentCpu_const__InputArrayR_bool(instance: *mut c_void, src: *const c_void, if_draw: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hfs_HfsSegment_performSegmentCpu_const__InputArrayR(instance: *mut c_void, src: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hfs_HfsSegment_create_int_int_float_int_float_int_float_int_int(height: i32, width: i32, seg_egb_threshold_i: f32, min_region_size_i: i32, seg_egb_threshold_ii: f32, min_region_size_ii: i32, spatial_weight: f32, slic_spixel_size: i32, num_slic_iter: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hfs_HfsSegment_create_int_int(height: i32, width: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hfs_HfsSegment_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_hfs_HfsSegment_delete(instance: *mut c_void);
