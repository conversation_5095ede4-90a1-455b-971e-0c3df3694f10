pub fn cv_PtrLcv_ximgproc_ContourFittingG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_ContourFittingG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_ContourFittingG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_ContourFittingG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_ContourFittingG_new_const_ContourFitting(val: *mut c_void) -> *mut c_void;
