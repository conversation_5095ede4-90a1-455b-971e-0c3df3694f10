pub fn cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_to_PtrOfMoreAccurateMotionWobbleSuppressorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_to_PtrOfWobbleSuppressorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_new_const_MoreAccurateMotionWobbleSuppressor(val: *mut c_void) -> *mut c_void;
