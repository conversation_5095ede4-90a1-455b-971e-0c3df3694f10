extern "C" {
	const cv::videostab::PyrLkOptFlowEstimatorBase* cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::PyrLkOptFlowEstimatorBase* cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_delete(cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_new_const_PyrLkOptFlowEstimatorBase(cv::videostab::PyrLkOptFlowEstimatorBase* val) {
			return new cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>(val);
	}
	
}

