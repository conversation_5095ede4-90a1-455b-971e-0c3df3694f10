pub fn cv_RQDecomp3x3_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, mtx_r: *const c_void, mtx_q: *const c_void, ocvrs_return: *mut Result<core::Vec3d>);
pub fn cv_RQDecomp3x3_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, mtx_r: *const c_void, mtx_q: *const c_void, qx: *const c_void, qy: *const c_void, qz: *const c_void, ocvrs_return: *mut Result<core::Vec3d>);
pub fn cv_Rodrigues_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Rodrigues_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, jacobian: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_calibrateCameraRO_const__InputArrayR_const__InputArrayR_Size_int_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, i_fixed_point: i32, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, new_obj_points: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_calibrateCameraRO_const__InputArrayR_const__InputArrayR_Size_int_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, i_fixed_point: i32, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, new_obj_points: *const c_void, std_deviations_intrinsics: *const c_void, std_deviations_extrinsics: *const c_void, std_deviations_obj_points: *const c_void, per_view_errors: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_calibrateCameraRO_const__InputArrayR_const__InputArrayR_Size_int_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, i_fixed_point: i32, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, new_obj_points: *const c_void, std_deviations_intrinsics: *const c_void, std_deviations_extrinsics: *const c_void, std_deviations_obj_points: *const c_void, per_view_errors: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_calibrateCameraRO_const__InputArrayR_const__InputArrayR_Size_int_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, i_fixed_point: i32, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, new_obj_points: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_calibrateCamera_const__InputArrayR_const__InputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_calibrateCamera_const__InputArrayR_const__InputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, std_deviations_intrinsics: *const c_void, std_deviations_extrinsics: *const c_void, per_view_errors: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_calibrateCamera_const__InputArrayR_const__InputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, std_deviations_intrinsics: *const c_void, std_deviations_extrinsics: *const c_void, per_view_errors: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_calibrateCamera_const__InputArrayR_const__InputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_calibrateHandEye_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(r_gripper2base: *const c_void, t_gripper2base: *const c_void, r_target2cam: *const c_void, t_target2cam: *const c_void, r_cam2gripper: *const c_void, t_cam2gripper: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_calibrateHandEye_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_HandEyeCalibrationMethod(r_gripper2base: *const c_void, t_gripper2base: *const c_void, r_target2cam: *const c_void, t_target2cam: *const c_void, r_cam2gripper: *const c_void, t_cam2gripper: *const c_void, method: crate::calib3d::HandEyeCalibrationMethod, ocvrs_return: *mut ResultVoid);
pub fn cv_calibrateRobotWorldHandEye_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(r_world2cam: *const c_void, t_world2cam: *const c_void, r_base2gripper: *const c_void, t_base2gripper: *const c_void, r_base2world: *const c_void, t_base2world: *const c_void, r_gripper2cam: *const c_void, t_gripper2cam: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_calibrateRobotWorldHandEye_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_RobotWorldHandEyeCalibrationMethod(r_world2cam: *const c_void, t_world2cam: *const c_void, r_base2gripper: *const c_void, t_base2gripper: *const c_void, r_base2world: *const c_void, t_base2world: *const c_void, r_gripper2cam: *const c_void, t_gripper2cam: *const c_void, method: crate::calib3d::RobotWorldHandEyeCalibrationMethod, ocvrs_return: *mut ResultVoid);
pub fn cv_calibrationMatrixValues_const__InputArrayR_Size_double_double_doubleR_doubleR_doubleR_Point2dR_doubleR(camera_matrix: *const c_void, image_size: *const core::Size, aperture_width: f64, aperture_height: f64, fovx: *mut f64, fovy: *mut f64, focal_length: *mut f64, principal_point: *mut core::Point2d, aspect_ratio: *mut f64, ocvrs_return: *mut ResultVoid);
pub fn cv_checkChessboard_const__InputArrayR_Size(img: *const c_void, size: *const core::Size, ocvrs_return: *mut Result<bool>);
pub fn cv_composeRT_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(rvec1: *const c_void, tvec1: *const c_void, rvec2: *const c_void, tvec2: *const c_void, rvec3: *const c_void, tvec3: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_composeRT_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(rvec1: *const c_void, tvec1: *const c_void, rvec2: *const c_void, tvec2: *const c_void, rvec3: *const c_void, tvec3: *const c_void, dr3dr1: *const c_void, dr3dt1: *const c_void, dr3dr2: *const c_void, dr3dt2: *const c_void, dt3dr1: *const c_void, dt3dt1: *const c_void, dt3dr2: *const c_void, dt3dt2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_computeCorrespondEpilines_const__InputArrayR_int_const__InputArrayR_const__OutputArrayR(points: *const c_void, which_image: i32, f: *const c_void, lines: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_convertPointsFromHomogeneous_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_convertPointsHomogeneous_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_convertPointsToHomogeneous_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_correctMatches_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(f: *const c_void, points1: *const c_void, points2: *const c_void, new_points1: *const c_void, new_points2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_decomposeEssentialMat_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(e: *const c_void, r1: *const c_void, r2: *const c_void, t: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_decomposeHomographyMat_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(h: *const c_void, k: *const c_void, rotations: *const c_void, translations: *const c_void, normals: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_decomposeProjectionMatrix_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(proj_matrix: *const c_void, camera_matrix: *const c_void, rot_matrix: *const c_void, trans_vect: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_decomposeProjectionMatrix_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(proj_matrix: *const c_void, camera_matrix: *const c_void, rot_matrix: *const c_void, trans_vect: *const c_void, rot_matrix_x: *const c_void, rot_matrix_y: *const c_void, rot_matrix_z: *const c_void, euler_angles: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_drawChessboardCorners_const__InputOutputArrayR_Size_const__InputArrayR_bool(image: *const c_void, pattern_size: *const core::Size, corners: *const c_void, pattern_was_found: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_drawFrameAxes_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_float(image: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, length: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_drawFrameAxes_const__InputOutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_float_int(image: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, length: f32, thickness: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_estimateAffine2D_const__InputArrayR_const__InputArrayR(from: *const c_void, to: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_estimateAffine2D_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const_UsacParamsR(pts1: *const c_void, pts2: *const c_void, inliers: *const c_void, params: *const crate::calib3d::UsacParams, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_estimateAffine2D_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double_size_t_double_size_t(from: *const c_void, to: *const c_void, inliers: *const c_void, method: i32, ransac_reproj_threshold: f64, max_iters: size_t, confidence: f64, refine_iters: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_estimateAffine3D_const__InputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_estimateAffine3D_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, out: *const c_void, inliers: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_estimateAffine3D_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double_double(src: *const c_void, dst: *const c_void, out: *const c_void, inliers: *const c_void, ransac_threshold: f64, confidence: f64, ocvrs_return: *mut Result<i32>);
pub fn cv_estimateAffine3D_const__InputArrayR_const__InputArrayR_doubleX_bool(src: *const c_void, dst: *const c_void, scale: *mut f64, force_rotation: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_estimateAffinePartial2D_const__InputArrayR_const__InputArrayR(from: *const c_void, to: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_estimateAffinePartial2D_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double_size_t_double_size_t(from: *const c_void, to: *const c_void, inliers: *const c_void, method: i32, ransac_reproj_threshold: f64, max_iters: size_t, confidence: f64, refine_iters: size_t, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_estimateChessboardSharpness_const__InputArrayR_Size_const__InputArrayR(image: *const c_void, pattern_size: *const core::Size, corners: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_estimateChessboardSharpness_const__InputArrayR_Size_const__InputArrayR_float_bool_const__OutputArrayR(image: *const c_void, pattern_size: *const core::Size, corners: *const c_void, rise_distance: f32, vertical: bool, sharpness: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_estimateTranslation3D_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, out: *const c_void, inliers: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_estimateTranslation3D_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double_double(src: *const c_void, dst: *const c_void, out: *const c_void, inliers: *const c_void, ransac_threshold: f64, confidence: f64, ocvrs_return: *mut Result<i32>);
pub fn cv_filterHomographyDecompByVisibleRefpoints_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(rotations: *const c_void, normals: *const c_void, before_points: *const c_void, after_points: *const c_void, possible_solutions: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_filterHomographyDecompByVisibleRefpoints_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__InputArrayR(rotations: *const c_void, normals: *const c_void, before_points: *const c_void, after_points: *const c_void, possible_solutions: *const c_void, points_mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_filterSpeckles_const__InputOutputArrayR_double_int_double(img: *const c_void, new_val: f64, max_speckle_size: i32, max_diff: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_filterSpeckles_const__InputOutputArrayR_double_int_double_const__InputOutputArrayR(img: *const c_void, new_val: f64, max_speckle_size: i32, max_diff: f64, buf: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_find4QuadCornerSubpix_const__InputArrayR_const__InputOutputArrayR_Size(img: *const c_void, corners: *const c_void, region_size: *const core::Size, ocvrs_return: *mut Result<bool>);
pub fn cv_findChessboardCornersSB_const__InputArrayR_Size_const__OutputArrayR(image: *const c_void, pattern_size: *const core::Size, corners: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_findChessboardCornersSB_const__InputArrayR_Size_const__OutputArrayR_int(image: *const c_void, pattern_size: *const core::Size, corners: *const c_void, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_findChessboardCornersSB_const__InputArrayR_Size_const__OutputArrayR_int_const__OutputArrayR(image: *const c_void, pattern_size: *const core::Size, corners: *const c_void, flags: i32, meta: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_findChessboardCorners_const__InputArrayR_Size_const__OutputArrayR(image: *const c_void, pattern_size: *const core::Size, corners: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_findChessboardCorners_const__InputArrayR_Size_const__OutputArrayR_int(image: *const c_void, pattern_size: *const core::Size, corners: *const c_void, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_findCirclesGrid_const__InputArrayR_Size_const__OutputArrayR(image: *const c_void, pattern_size: *const core::Size, centers: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_findCirclesGrid_const__InputArrayR_Size_const__OutputArrayR_int_const_PtrLFeature2DGR(image: *const c_void, pattern_size: *const core::Size, centers: *const c_void, flags: i32, blob_detector: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_findCirclesGrid_const__InputArrayR_Size_const__OutputArrayR_int_const_PtrLFeature2DGR_const_CirclesGridFinderParametersR(image: *const c_void, pattern_size: *const core::Size, centers: *const c_void, flags: i32, blob_detector: *const c_void, parameters: *const crate::calib3d::CirclesGridFinderParameters, ocvrs_return: *mut Result<bool>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR(points1: *const c_void, points2: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR_const__InputArrayR(points1: *const c_void, points2: *const c_void, camera_matrix: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(points1: *const c_void, points2: *const c_void, camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const_UsacParamsR(points1: *const c_void, points2: *const c_void, camera_matrix1: *const c_void, camera_matrix2: *const c_void, dist_coeff1: *const c_void, dist_coeff2: *const c_void, mask: *const c_void, params: *const crate::calib3d::UsacParams, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_double_double_const__OutputArrayR(points1: *const c_void, points2: *const c_void, camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, method: i32, prob: f64, threshold: f64, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_double_double_const__OutputArrayR(points1: *const c_void, points2: *const c_void, camera_matrix: *const c_void, method: i32, prob: f64, threshold: f64, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_double_double_int_const__OutputArrayR(points1: *const c_void, points2: *const c_void, camera_matrix: *const c_void, method: i32, prob: f64, threshold: f64, max_iters: i32, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR_double_Point2d_int_double_double_const__OutputArrayR(points1: *const c_void, points2: *const c_void, focal: f64, pp: *const core::Point2d, method: i32, prob: f64, threshold: f64, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findEssentialMat_const__InputArrayR_const__InputArrayR_double_Point2d_int_double_double_int_const__OutputArrayR(points1: *const c_void, points2: *const c_void, focal: f64, pp: *const core::Point2d, method: i32, prob: f64, threshold: f64, max_iters: i32, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findFundamentalMat_const__InputArrayR_const__InputArrayR(points1: *const c_void, points2: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findFundamentalMat_const__InputArrayR_const__InputArrayR_const__OutputArrayR(points1: *const c_void, points2: *const c_void, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findFundamentalMat_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const_UsacParamsR(points1: *const c_void, points2: *const c_void, mask: *const c_void, params: *const crate::calib3d::UsacParams, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findFundamentalMat_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double_double(points1: *const c_void, points2: *const c_void, mask: *const c_void, method: i32, ransac_reproj_threshold: f64, confidence: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findFundamentalMat_const__InputArrayR_const__InputArrayR_int_double_double_const__OutputArrayR(points1: *const c_void, points2: *const c_void, method: i32, ransac_reproj_threshold: f64, confidence: f64, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findFundamentalMat_const__InputArrayR_const__InputArrayR_int_double_double_int(points1: *const c_void, points2: *const c_void, method: i32, ransac_reproj_threshold: f64, confidence: f64, max_iters: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findFundamentalMat_const__InputArrayR_const__InputArrayR_int_double_double_int_const__OutputArrayR(points1: *const c_void, points2: *const c_void, method: i32, ransac_reproj_threshold: f64, confidence: f64, max_iters: i32, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findHomography_const__InputArrayR_const__InputArrayR(src_points: *const c_void, dst_points: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findHomography_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src_points: *const c_void, dst_points: *const c_void, mask: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findHomography_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const_UsacParamsR(src_points: *const c_void, dst_points: *const c_void, mask: *const c_void, params: *const crate::calib3d::UsacParams, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findHomography_const__InputArrayR_const__InputArrayR_const__OutputArrayR_int_double(src_points: *const c_void, dst_points: *const c_void, mask: *const c_void, method: i32, ransac_reproj_threshold: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_findHomography_const__InputArrayR_const__InputArrayR_int_double_const__OutputArrayR_const_int_const_double(src_points: *const c_void, dst_points: *const c_void, method: i32, ransac_reproj_threshold: f64, mask: *const c_void, max_iters: i32, confidence: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_fisheye_calibrate_const__InputArrayR_const__InputArrayR_const_SizeR_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, k: *const c_void, d: *const c_void, rvecs: *const c_void, tvecs: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_fisheye_calibrate_const__InputArrayR_const__InputArrayR_const_SizeR_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, k: *const c_void, d: *const c_void, rvecs: *const c_void, tvecs: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_fisheye_distortPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(undistorted: *const c_void, distorted: *const c_void, k: *const c_void, d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_distortPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_double(undistorted: *const c_void, distorted: *const c_void, k: *const c_void, d: *const c_void, alpha: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_estimateNewCameraMatrixForUndistortRectify_const__InputArrayR_const__InputArrayR_const_SizeR_const__InputArrayR_const__OutputArrayR(k: *const c_void, d: *const c_void, image_size: *const core::Size, r: *const c_void, p: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_estimateNewCameraMatrixForUndistortRectify_const__InputArrayR_const__InputArrayR_const_SizeR_const__InputArrayR_const__OutputArrayR_double_const_SizeR_double(k: *const c_void, d: *const c_void, image_size: *const core::Size, r: *const c_void, p: *const c_void, balance: f64, new_size: *const core::Size, fov_scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_initUndistortRectifyMap_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR_int_const__OutputArrayR_const__OutputArrayR(k: *const c_void, d: *const c_void, r: *const c_void, p: *const c_void, size: *const core::Size, m1type: i32, map1: *const c_void, map2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_projectPoints_const__InputArrayR_const__OutputArrayR_const_Affine3dR_const__InputArrayR_const__InputArrayR(object_points: *const c_void, image_points: *const c_void, affine: *const core::Affine3d, k: *const c_void, d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_projectPoints_const__InputArrayR_const__OutputArrayR_const_Affine3dR_const__InputArrayR_const__InputArrayR_double_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, affine: *const core::Affine3d, k: *const c_void, d: *const c_void, alpha: f64, jacobian: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_projectPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(object_points: *const c_void, image_points: *const c_void, rvec: *const c_void, tvec: *const c_void, k: *const c_void, d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_projectPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_double_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, rvec: *const c_void, tvec: *const c_void, k: *const c_void, d: *const c_void, alpha: f64, jacobian: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_stereoCalibrate_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_Size_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points1: *const c_void, image_points2: *const c_void, k1: *const c_void, d1: *const c_void, k2: *const c_void, d2: *const c_void, image_size: *const core::Size, r: *const c_void, t: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_fisheye_stereoCalibrate_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_Size_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points1: *const c_void, image_points2: *const c_void, k1: *const c_void, d1: *const c_void, k2: *const c_void, d2: *const c_void, image_size: *const core::Size, r: *const c_void, t: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_fisheye_stereoRectify_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int(k1: *const c_void, d1: *const c_void, k2: *const c_void, d2: *const c_void, image_size: *const core::Size, r: *const c_void, tvec: *const c_void, r1: *const c_void, r2: *const c_void, p1: *const c_void, p2: *const c_void, q: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_stereoRectify_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_const_SizeR_double_double(k1: *const c_void, d1: *const c_void, k2: *const c_void, d2: *const c_void, image_size: *const core::Size, r: *const c_void, tvec: *const c_void, r1: *const c_void, r2: *const c_void, p1: *const c_void, p2: *const c_void, q: *const c_void, flags: i32, new_image_size: *const core::Size, balance: f64, fov_scale: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_undistortImage_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(distorted: *const c_void, undistorted: *const c_void, k: *const c_void, d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_undistortImage_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR(distorted: *const c_void, undistorted: *const c_void, k: *const c_void, d: *const c_void, knew: *const c_void, new_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_undistortPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(distorted: *const c_void, undistorted: *const c_void, k: *const c_void, d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fisheye_undistortPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(distorted: *const c_void, undistorted: *const c_void, k: *const c_void, d: *const c_void, r: *const c_void, p: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_getDefaultNewCameraMatrix_const__InputArrayR(camera_matrix: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getDefaultNewCameraMatrix_const__InputArrayR_Size_bool(camera_matrix: *const c_void, imgsize: *const core::Size, center_principal_point: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getOptimalNewCameraMatrix_const__InputArrayR_const__InputArrayR_Size_double(camera_matrix: *const c_void, dist_coeffs: *const c_void, image_size: *const core::Size, alpha: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getOptimalNewCameraMatrix_const__InputArrayR_const__InputArrayR_Size_double_Size_RectX_bool(camera_matrix: *const c_void, dist_coeffs: *const c_void, image_size: *const core::Size, alpha: f64, new_img_size: *const core::Size, valid_pix_roi: *mut core::Rect, center_principal_point: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_getValidDisparityROI_Rect_Rect_int_int_int(roi1: *const core::Rect, roi2: *const core::Rect, min_disparity: i32, number_of_disparities: i32, block_size: i32, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_initCameraMatrix2D_const__InputArrayR_const__InputArrayR_Size(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_initCameraMatrix2D_const__InputArrayR_const__InputArrayR_Size_double(object_points: *const c_void, image_points: *const c_void, image_size: *const core::Size, aspect_ratio: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_initInverseRectificationMap_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const_SizeR_int_const__OutputArrayR_const__OutputArrayR(camera_matrix: *const c_void, dist_coeffs: *const c_void, r: *const c_void, new_camera_matrix: *const c_void, size: *const core::Size, m1type: i32, map1: *const c_void, map2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_initUndistortRectifyMap_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_Size_int_const__OutputArrayR_const__OutputArrayR(camera_matrix: *const c_void, dist_coeffs: *const c_void, r: *const c_void, new_camera_matrix: *const c_void, size: *const core::Size, m1type: i32, map1: *const c_void, map2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_initWideAngleProjMap_const__InputArrayR_const__InputArrayR_Size_int_int_const__OutputArrayR_const__OutputArrayR(camera_matrix: *const c_void, dist_coeffs: *const c_void, image_size: *const core::Size, dest_image_width: i32, m1type: i32, map1: *const c_void, map2: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_initWideAngleProjMap_const__InputArrayR_const__InputArrayR_Size_int_int_const__OutputArrayR_const__OutputArrayR_UndistortTypes_double(camera_matrix: *const c_void, dist_coeffs: *const c_void, image_size: *const core::Size, dest_image_width: i32, m1type: i32, map1: *const c_void, map2: *const c_void, proj_type: crate::calib3d::UndistortTypes, alpha: f64, ocvrs_return: *mut Result<f32>);
pub fn cv_matMulDeriv_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(a: *const c_void, b: *const c_void, d_a_bd_a: *const c_void, d_a_bd_b: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_projectPoints_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(object_points: *const c_void, rvec: *const c_void, tvec: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, image_points: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_projectPoints_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double(object_points: *const c_void, rvec: *const c_void, tvec: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, image_points: *const c_void, jacobian: *const c_void, aspect_ratio: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_recoverPose_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(points1: *const c_void, points2: *const c_void, camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, e: *const c_void, r: *const c_void, t: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_recoverPose_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_double_double_const__InputOutputArrayR(points1: *const c_void, points2: *const c_void, camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, e: *const c_void, r: *const c_void, t: *const c_void, method: i32, prob: f64, threshold: f64, mask: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_recoverPose_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(e: *const c_void, points1: *const c_void, points2: *const c_void, camera_matrix: *const c_void, r: *const c_void, t: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_recoverPose_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__InputOutputArrayR(e: *const c_void, points1: *const c_void, points2: *const c_void, camera_matrix: *const c_void, r: *const c_void, t: *const c_void, mask: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_recoverPose_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double(e: *const c_void, points1: *const c_void, points2: *const c_void, camera_matrix: *const c_void, r: *const c_void, t: *const c_void, distance_thresh: f64, ocvrs_return: *mut Result<i32>);
pub fn cv_recoverPose_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double_const__InputOutputArrayR_const__OutputArrayR(e: *const c_void, points1: *const c_void, points2: *const c_void, camera_matrix: *const c_void, r: *const c_void, t: *const c_void, distance_thresh: f64, mask: *const c_void, triangulated_points: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_recoverPose_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(e: *const c_void, points1: *const c_void, points2: *const c_void, r: *const c_void, t: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_recoverPose_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_double_Point2d_const__InputOutputArrayR(e: *const c_void, points1: *const c_void, points2: *const c_void, r: *const c_void, t: *const c_void, focal: f64, pp: *const core::Point2d, mask: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_rectify3Collinear_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_Size_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_double_Size_RectX_RectX_int(camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, camera_matrix3: *const c_void, dist_coeffs3: *const c_void, imgpt1: *const c_void, imgpt3: *const c_void, image_size: *const core::Size, r12: *const c_void, t12: *const c_void, r13: *const c_void, t13: *const c_void, r1: *const c_void, r2: *const c_void, r3: *const c_void, p1: *const c_void, p2: *const c_void, p3: *const c_void, q: *const c_void, alpha: f64, new_img_size: *const core::Size, roi1: *mut core::Rect, roi2: *mut core::Rect, flags: i32, ocvrs_return: *mut Result<f32>);
pub fn cv_reprojectImageTo3D_const__InputArrayR_const__OutputArrayR_const__InputArrayR(disparity: *const c_void, _3d_image: *const c_void, q: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_reprojectImageTo3D_const__InputArrayR_const__OutputArrayR_const__InputArrayR_bool_int(disparity: *const c_void, _3d_image: *const c_void, q: *const c_void, handle_missing_values: bool, ddepth: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_sampsonDistance_const__InputArrayR_const__InputArrayR_const__InputArrayR(pt1: *const c_void, pt2: *const c_void, f: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_solveP3P_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_int(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, flags: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_solvePnPGeneric_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_solvePnPGeneric_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_bool_SolvePnPMethod_const__InputArrayR_const__InputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvecs: *const c_void, tvecs: *const c_void, use_extrinsic_guess: bool, flags: crate::calib3d::SolvePnPMethod, rvec: *const c_void, tvec: *const c_void, reprojection_error: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_solvePnPRansac_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_solvePnPRansac_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_bool_int_float_double_const__OutputArrayR_int(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, use_extrinsic_guess: bool, iterations_count: i32, reprojection_error: f32, confidence: f64, inliers: *const c_void, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_solvePnPRansac_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, inliers: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_solvePnPRansac_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const_UsacParamsR(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, inliers: *const c_void, params: *const crate::calib3d::UsacParams, ocvrs_return: *mut Result<bool>);
pub fn cv_solvePnPRefineLM_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_solvePnPRefineLM_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_TermCriteria(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, criteria: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_solvePnPRefineVVS_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_solvePnPRefineVVS_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_TermCriteria_double(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, criteria: *const core::TermCriteria, vv_slambda: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_solvePnP_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_solvePnP_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_bool_int(object_points: *const c_void, image_points: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, rvec: *const c_void, tvec: *const c_void, use_extrinsic_guess: bool, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_stereoCalibrate_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points1: *const c_void, image_points2: *const c_void, camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, image_size: *const core::Size, r: *const c_void, t: *const c_void, e: *const c_void, f: *const c_void, per_view_errors: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_stereoCalibrate_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_Size_const__InputOutputArrayR_const__InputOutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points1: *const c_void, image_points2: *const c_void, camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, image_size: *const core::Size, r: *const c_void, t: *const c_void, e: *const c_void, f: *const c_void, per_view_errors: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_stereoCalibrate_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_Size_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(object_points: *const c_void, image_points1: *const c_void, image_points2: *const c_void, camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, image_size: *const core::Size, r: *const c_void, t: *const c_void, e: *const c_void, f: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_stereoCalibrate_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_const__InputOutputArrayR_Size_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_TermCriteria(object_points: *const c_void, image_points1: *const c_void, image_points2: *const c_void, camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, image_size: *const core::Size, r: *const c_void, t: *const c_void, e: *const c_void, f: *const c_void, flags: i32, criteria: *const core::TermCriteria, ocvrs_return: *mut Result<f64>);
pub fn cv_stereoRectifyUncalibrated_const__InputArrayR_const__InputArrayR_const__InputArrayR_Size_const__OutputArrayR_const__OutputArrayR(points1: *const c_void, points2: *const c_void, f: *const c_void, img_size: *const core::Size, h1: *const c_void, h2: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_stereoRectifyUncalibrated_const__InputArrayR_const__InputArrayR_const__InputArrayR_Size_const__OutputArrayR_const__OutputArrayR_double(points1: *const c_void, points2: *const c_void, f: *const c_void, img_size: *const core::Size, h1: *const c_void, h2: *const c_void, threshold: f64, ocvrs_return: *mut Result<bool>);
pub fn cv_stereoRectify_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR(camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, image_size: *const core::Size, r: *const c_void, t: *const c_void, r1: *const c_void, r2: *const c_void, p1: *const c_void, p2: *const c_void, q: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_stereoRectify_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_const__OutputArrayR_int_double_Size_RectX_RectX(camera_matrix1: *const c_void, dist_coeffs1: *const c_void, camera_matrix2: *const c_void, dist_coeffs2: *const c_void, image_size: *const core::Size, r: *const c_void, t: *const c_void, r1: *const c_void, r2: *const c_void, p1: *const c_void, p2: *const c_void, q: *const c_void, flags: i32, alpha: f64, new_image_size: *const core::Size, valid_pix_roi1: *mut core::Rect, valid_pix_roi2: *mut core::Rect, ocvrs_return: *mut ResultVoid);
pub fn cv_triangulatePoints_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR(proj_matr1: *const c_void, proj_matr2: *const c_void, proj_points1: *const c_void, proj_points2: *const c_void, points4_d: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_undistortPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_undistortPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, r: *const c_void, p: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_undistortPoints_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_TermCriteria(src: *const c_void, dst: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, r: *const c_void, p: *const c_void, criteria: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_undistort_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_undistort_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(src: *const c_void, dst: *const c_void, camera_matrix: *const c_void, dist_coeffs: *const c_void, new_camera_matrix: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_validateDisparity_const__InputOutputArrayR_const__InputArrayR_int_int(disparity: *const c_void, cost: *const c_void, min_disparity: i32, number_of_disparities: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_validateDisparity_const__InputOutputArrayR_const__InputArrayR_int_int_int(disparity: *const c_void, cost: *const c_void, min_disparity: i32, number_of_disparities: i32, disp12_max_disp: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_CirclesGridFinderParameters_CirclesGridFinderParameters(ocvrs_return: *mut Result<crate::calib3d::CirclesGridFinderParameters>);
pub fn cv_LMSolver_run_const_const__InputOutputArrayR(instance: *const c_void, param: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_LMSolver_setMaxIters_int(instance: *mut c_void, max_iters: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_LMSolver_getMaxIters_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_LMSolver_create_const_PtrLCallbackGR_int(cb: *const c_void, max_iters: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LMSolver_create_const_PtrLCallbackGR_int_double(cb: *const c_void, max_iters: i32, eps: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_LMSolver_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_LMSolver_delete(instance: *mut c_void);
pub fn cv_LMSolver_Callback_compute_const_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *const c_void, param: *const c_void, err: *const c_void, j: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_LMSolver_Callback_delete(instance: *mut c_void);
pub fn cv_StereoBM_getPreFilterType_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoBM_setPreFilterType_int(instance: *mut c_void, pre_filter_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoBM_getPreFilterSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoBM_setPreFilterSize_int(instance: *mut c_void, pre_filter_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoBM_getPreFilterCap_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoBM_setPreFilterCap_int(instance: *mut c_void, pre_filter_cap: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoBM_getTextureThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoBM_setTextureThreshold_int(instance: *mut c_void, texture_threshold: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoBM_getUniquenessRatio_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoBM_setUniquenessRatio_int(instance: *mut c_void, uniqueness_ratio: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoBM_getSmallerBlockSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoBM_setSmallerBlockSize_int(instance: *mut c_void, block_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoBM_getROI1_const(instance: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_StereoBM_setROI1_Rect(instance: *mut c_void, roi1: *const core::Rect, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoBM_getROI2_const(instance: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_StereoBM_setROI2_Rect(instance: *mut c_void, roi2: *const core::Rect, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoBM_create_int_int(num_disparities: i32, block_size: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_StereoBM_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_StereoBM_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_StereoBM_to_StereoMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_StereoBM_delete(instance: *mut c_void);
pub fn cv_StereoMatcher_compute_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, left: *const c_void, right: *const c_void, disparity: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoMatcher_getMinDisparity_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoMatcher_setMinDisparity_int(instance: *mut c_void, min_disparity: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoMatcher_getNumDisparities_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoMatcher_setNumDisparities_int(instance: *mut c_void, num_disparities: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoMatcher_getBlockSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoMatcher_setBlockSize_int(instance: *mut c_void, block_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoMatcher_getSpeckleWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoMatcher_setSpeckleWindowSize_int(instance: *mut c_void, speckle_window_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoMatcher_getSpeckleRange_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoMatcher_setSpeckleRange_int(instance: *mut c_void, speckle_range: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoMatcher_getDisp12MaxDiff_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoMatcher_setDisp12MaxDiff_int(instance: *mut c_void, disp12_max_diff: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoMatcher_to_StereoBM(instance: *mut c_void) -> *mut c_void;
pub fn cv_StereoMatcher_to_StereoSGBM(instance: *mut c_void) -> *mut c_void;
pub fn cv_StereoMatcher_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_StereoMatcher_delete(instance: *mut c_void);
pub fn cv_StereoSGBM_getPreFilterCap_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoSGBM_setPreFilterCap_int(instance: *mut c_void, pre_filter_cap: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoSGBM_getUniquenessRatio_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoSGBM_setUniquenessRatio_int(instance: *mut c_void, uniqueness_ratio: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoSGBM_getP1_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoSGBM_setP1_int(instance: *mut c_void, p1: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoSGBM_getP2_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoSGBM_setP2_int(instance: *mut c_void, p2: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoSGBM_getMode_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_StereoSGBM_setMode_int(instance: *mut c_void, mode: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_StereoSGBM_create_int_int_int_int_int_int_int_int_int_int_int(min_disparity: i32, num_disparities: i32, block_size: i32, p1: i32, p2: i32, disp12_max_diff: i32, pre_filter_cap: i32, uniqueness_ratio: i32, speckle_window_size: i32, speckle_range: i32, mode: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_StereoSGBM_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_StereoSGBM_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_StereoSGBM_to_StereoMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_StereoSGBM_delete(instance: *mut c_void);
pub fn cv_UsacParams_UsacParams(ocvrs_return: *mut Result<crate::calib3d::UsacParams>);
