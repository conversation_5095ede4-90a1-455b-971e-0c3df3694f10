pub fn cv_bioinspired_Retina_getInputSize(instance: *mut c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_bioinspired_Retina_getOutputSize(instance: *mut c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_bioinspired_Retina_setup_String_const_bool(instance: *mut c_void, retina_parameter_file: *const c_char, apply_default_setup_on_failure: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setup(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setup_FileStorageR_const_bool(instance: *mut c_void, fs: *mut c_void, apply_default_setup_on_failure: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setup_FileStorageR(instance: *mut c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setup_RetinaParameters(instance: *mut c_void, new_parameters: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_getParameters(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_Retina_printSetup(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_Retina_write_const_String(instance: *const c_void, fs: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setupOPLandIPLParvoChannel_const_bool_const_bool_const_float_const_float_const_float_const_float_const_float_const_float_const_float(instance: *mut c_void, color_mode: bool, normalise_output: bool, photoreceptors_local_adaptation_sensitivity: f32, photoreceptors_temporal_constant: f32, photoreceptors_spatial_constant: f32, horizontal_cells_gain: f32, hcells_temporal_constant: f32, hcells_spatial_constant: f32, ganglion_cells_sensitivity: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setupOPLandIPLParvoChannel(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setupIPLMagnoChannel_const_bool_const_float_const_float_const_float_const_float_const_float_const_float_const_float(instance: *mut c_void, normalise_output: bool, parasol_cells_beta: f32, parasol_cells_tau: f32, parasol_cells_k: f32, amacrin_cells_temporal_cut_frequency: f32, v0_compression_parameter: f32, local_adaptintegration_tau: f32, local_adaptintegration_k: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setupIPLMagnoChannel(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_run_const__InputArrayR(instance: *mut c_void, input_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_applyFastToneMapping_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, input_image: *const c_void, output_tone_mapped_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_getParvo_const__OutputArrayR(instance: *mut c_void, retina_output_parvo: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_getParvoRAW_const__OutputArrayR(instance: *mut c_void, retina_output_parvo: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_getMagno_const__OutputArrayR(instance: *mut c_void, retina_output_magno: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_getMagnoRAW_const__OutputArrayR(instance: *mut c_void, retina_output_magno: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_getMagnoRAW_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_Retina_getParvoRAW_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_Retina_setColorSaturation_const_bool_const_float(instance: *mut c_void, saturate_colors: bool, color_saturation_value: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_setColorSaturation(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_clearBuffers(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_activateMovingContoursProcessing_const_bool(instance: *mut c_void, activate: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_activateContoursProcessing_const_bool(instance: *mut c_void, activate: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_Retina_create_Size(input_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_Retina_create_Size_const_bool_int_const_bool_const_float_const_float(input_size: *const core::Size, color_mode: bool, color_sampling_method: i32, use_retina_log_sampling: bool, reduction_factor: f32, sampling_strength: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_Retina_create_Size_const_bool(input_size: *const core::Size, color_mode: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_Retina_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_bioinspired_Retina_delete(instance: *mut c_void);
pub fn cv_bioinspired_RetinaFastToneMapping_applyFastToneMapping_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, input_image: *const c_void, output_tone_mapped_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_RetinaFastToneMapping_setup_const_float_const_float_const_float(instance: *mut c_void, photoreceptors_neighborhood_radius: f32, ganglioncells_neighborhood_radius: f32, mean_luminance_modulator_k: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_RetinaFastToneMapping_setup(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_RetinaFastToneMapping_create_Size(input_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_RetinaFastToneMapping_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_bioinspired_RetinaFastToneMapping_delete(instance: *mut c_void);
pub fn cv_bioinspired_RetinaParameters_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_bioinspired_RetinaParameters_defaultNew_const() -> *mut c_void;
pub fn cv_bioinspired_RetinaParameters_propOPLandIplParvo_const(instance: *const c_void, ocvrs_return: *mut crate::bioinspired::RetinaParameters_OPLandIplParvoParameters);
pub fn cv_bioinspired_RetinaParameters_propOPLandIplParvo_const_OPLandIplParvoParameters(instance: *mut c_void, val: *const crate::bioinspired::RetinaParameters_OPLandIplParvoParameters);
pub fn cv_bioinspired_RetinaParameters_propIplMagno_const(instance: *const c_void, ocvrs_return: *mut crate::bioinspired::RetinaParameters_IplMagnoParameters);
pub fn cv_bioinspired_RetinaParameters_propIplMagno_const_IplMagnoParameters(instance: *mut c_void, val: *const crate::bioinspired::RetinaParameters_IplMagnoParameters);
pub fn cv_bioinspired_RetinaParameters_delete(instance: *mut c_void);
pub fn cv_bioinspired_RetinaParameters_IplMagnoParameters_IplMagnoParameters(ocvrs_return: *mut Result<crate::bioinspired::RetinaParameters_IplMagnoParameters>);
pub fn cv_bioinspired_RetinaParameters_OPLandIplParvoParameters_OPLandIplParvoParameters(ocvrs_return: *mut Result<crate::bioinspired::RetinaParameters_OPLandIplParvoParameters>);
pub fn cv_bioinspired_SegmentationParameters_SegmentationParameters(ocvrs_return: *mut Result<crate::bioinspired::SegmentationParameters>);
pub fn cv_bioinspired_TransientAreasSegmentationModule_getSize(instance: *mut c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_bioinspired_TransientAreasSegmentationModule_setup_String_const_bool(instance: *mut c_void, segmentation_parameter_file: *const c_char, apply_default_setup_on_failure: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_setup(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_setup_FileStorageR_const_bool(instance: *mut c_void, fs: *mut c_void, apply_default_setup_on_failure: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_setup_FileStorageR(instance: *mut c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_setup_SegmentationParameters(instance: *mut c_void, new_parameters: *const crate::bioinspired::SegmentationParameters, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_getParameters(instance: *mut c_void, ocvrs_return: *mut Result<crate::bioinspired::SegmentationParameters>);
pub fn cv_bioinspired_TransientAreasSegmentationModule_printSetup(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_TransientAreasSegmentationModule_write_const_String(instance: *const c_void, fs: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_write_const_FileStorageR(instance: *const c_void, fs: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_run_const__InputArrayR_const_int(instance: *mut c_void, input_to_segment: *const c_void, channel_index: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_run_const__InputArrayR(instance: *mut c_void, input_to_segment: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_getSegmentationPicture_const__OutputArrayR(instance: *mut c_void, transient_areas: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_clearAllBuffers(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_bioinspired_TransientAreasSegmentationModule_create_Size(input_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_bioinspired_TransientAreasSegmentationModule_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_bioinspired_TransientAreasSegmentationModule_delete(instance: *mut c_void);
