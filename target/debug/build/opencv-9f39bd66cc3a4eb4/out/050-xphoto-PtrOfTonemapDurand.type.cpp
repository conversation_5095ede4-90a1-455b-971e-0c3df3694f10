extern "C" {
	const cv::xphoto::TonemapDurand* cv_PtrLcv_xphoto_TonemapDurandG_getInnerPtr_const(const cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			return instance->get();
	}
	
	cv::xphoto::TonemapDurand* cv_PtrLcv_xphoto_TonemapDurandG_getInnerPtrMut(cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_TonemapDurandG_delete(cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_TonemapDurandG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Tonemap>* cv_PtrLcv_xphoto_TonemapDurandG_to_PtrOfTonemap(cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			return new cv::Ptr<cv::Tonemap>(instance->dynamicCast<cv::Tonemap>());
	}
	
}

