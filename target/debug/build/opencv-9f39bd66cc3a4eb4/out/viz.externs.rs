pub fn cv_viz_computeNormals_const_MeshR_const__OutputArrayR(mesh: *const c_void, normals: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_getWindowByName_const_StringR(window_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_imshow_const_StringR_const__InputArrayR(window_name: *const c_char, image: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_imshow_const_StringR_const__InputArrayR_const_SizeR(window_name: *const c_char, image: *const c_void, window_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_makeCameraPose_const_Vec3dR_const_Vec3dR_const_Vec3dR(position: *const core::Vec3d, focal_point: *const core::Vec3d, y_dir: *const core::Vec3d, ocvrs_return: *mut Result<core::Affine3d>);
pub fn cv_viz_makeTransformToGlobal_const_Vec3dR_const_Vec3dR_const_Vec3dR(axis_x: *const core::Vec3d, axis_y: *const core::Vec3d, axis_z: *const core::Vec3d, ocvrs_return: *mut Result<core::Affine3d>);
pub fn cv_viz_makeTransformToGlobal_const_Vec3dR_const_Vec3dR_const_Vec3dR_const_Vec3dR(axis_x: *const core::Vec3d, axis_y: *const core::Vec3d, axis_z: *const core::Vec3d, origin: *const core::Vec3d, ocvrs_return: *mut Result<core::Affine3d>);
pub fn cv_viz_readCloud_const_StringR(file: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_readCloud_const_StringR_const__OutputArrayR_const__OutputArrayR(file: *const c_char, colors: *const c_void, normals: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_readMesh_const_StringR(file: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_readPose_const_StringR_Affine3dR(file: *const c_char, pose: *mut core::Affine3d, ocvrs_return: *mut Result<bool>);
pub fn cv_viz_readPose_const_StringR_Affine3dR_const_StringR(file: *const c_char, pose: *mut core::Affine3d, tag: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_viz_readTrajectory_const__OutputArrayR(traj: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_readTrajectory_const__OutputArrayR_const_StringR_int_int_const_StringR(traj: *const c_void, files_format: *const c_char, start: i32, end: i32, tag: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_unregisterAllWindows(ocvrs_return: *mut ResultVoid);
pub fn cv_viz_writeCloud_const_StringR_const__InputArrayR(file: *const c_char, cloud: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_writeCloud_const_StringR_const__InputArrayR_const__InputArrayR_const__InputArrayR_bool(file: *const c_char, cloud: *const c_void, colors: *const c_void, normals: *const c_void, binary: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_writePose_const_StringR_const_Affine3dR(file: *const c_char, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_writePose_const_StringR_const_Affine3dR_const_StringR(file: *const c_char, pose: *const core::Affine3d, tag: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_writeTrajectory_const__InputArrayR(traj: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_writeTrajectory_const__InputArrayR_const_StringR_int_const_StringR(traj: *const c_void, files_format: *const c_char, start: i32, tag: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Camera_Camera_double_double_double_double_const_SizeR(fx: f64, fy: f64, cx: f64, cy: f64, window_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Camera_Camera_const_Vec2dR_const_SizeR(fov: *const core::Vec2d, window_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Camera_Camera_const_Matx33dR_const_SizeR(k: *const core::Matx33d, window_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Camera_Camera_const_Matx44dR_const_SizeR(proj: *const core::Matx44d, window_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Camera_getClip_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2d>);
pub fn cv_viz_Camera_setClip_const_Vec2dR(instance: *mut c_void, clip: *const core::Vec2d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Camera_getWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_viz_Camera_setWindowSize_const_SizeR(instance: *mut c_void, window_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Camera_getFov_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2d>);
pub fn cv_viz_Camera_setFov_const_Vec2dR(instance: *mut c_void, fov: *const core::Vec2d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Camera_getPrincipalPoint_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2d>);
pub fn cv_viz_Camera_getFocalLength_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec2d>);
pub fn cv_viz_Camera_computeProjectionMatrix_const_Matx44dR(instance: *const c_void, proj: *mut core::Matx44d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Camera_KinectCamera_const_SizeR(window_size: *const core::Size, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Camera_delete(instance: *mut c_void);
pub fn cv_viz_Color_Color(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_Color_double(gray: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_Color_double_double_double(blue: f64, green: f64, red: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_Color_const_ScalarR(color: *const core::Scalar, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_operator_cv_Vec3b_const(instance: *const c_void, ocvrs_return: *mut Result<core::Vec3b>);
pub fn cv_viz_Color_black(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_blue(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_green(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_red(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_cyan(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_yellow(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_magenta(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_white(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_gray(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_silver(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_mlab(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_navy(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_maroon(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_teal(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_olive(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_purple(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_azure(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_chartreuse(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_rose(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_lime(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_gold(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_orange(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_orange_red(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_indigo(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_brown(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_apricot(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_pink(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_raspberry(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_cherry(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_violet(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_amethyst(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_bluberry(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_celestial_blue(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_turquoise(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_not_set(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Color_delete(instance: *mut c_void);
pub fn cv_viz_KeyboardEvent_KeyboardEvent_Action_const_StringR_unsigned_char_int(action: crate::viz::KeyboardEvent_Action, symbol: *const c_char, code: u8, modifiers: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_KeyboardEvent_propAction_const(instance: *const c_void, ocvrs_return: *mut crate::viz::KeyboardEvent_Action);
pub fn cv_viz_KeyboardEvent_propAction_const_Action(instance: *mut c_void, val: crate::viz::KeyboardEvent_Action);
pub fn cv_viz_KeyboardEvent_propSymbol_const(instance: *const c_void) -> *mut c_void;
pub fn cv_viz_KeyboardEvent_propSymbol_const_String(instance: *mut c_void, val: *const c_char);
pub fn cv_viz_KeyboardEvent_propCode_const(instance: *const c_void) -> u8;
pub fn cv_viz_KeyboardEvent_propCode_const_unsigned_char(instance: *mut c_void, val: u8);
pub fn cv_viz_KeyboardEvent_propModifiers_const(instance: *const c_void) -> i32;
pub fn cv_viz_KeyboardEvent_propModifiers_const_int(instance: *mut c_void, val: i32);
pub fn cv_viz_KeyboardEvent_delete(instance: *mut c_void);
pub fn cv_viz_Mesh_Mesh(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Mesh_load_const_StringR_int(file: *const c_char, typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Mesh_load_const_StringR(file: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Mesh_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_viz_Mesh_propCloud_const(instance: *const c_void) -> *mut c_void;
pub fn cv_viz_Mesh_propCloud_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_viz_Mesh_propColors_const(instance: *const c_void) -> *mut c_void;
pub fn cv_viz_Mesh_propColors_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_viz_Mesh_propNormals_const(instance: *const c_void) -> *mut c_void;
pub fn cv_viz_Mesh_propNormals_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_viz_Mesh_propPolygons_const(instance: *const c_void) -> *mut c_void;
pub fn cv_viz_Mesh_propPolygons_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_viz_Mesh_propTexture_const(instance: *const c_void) -> *mut c_void;
pub fn cv_viz_Mesh_propTexture_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_viz_Mesh_propTcoords_const(instance: *const c_void) -> *mut c_void;
pub fn cv_viz_Mesh_propTcoords_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_viz_Mesh_delete(instance: *mut c_void);
pub fn cv_viz_MouseEvent_MouseEvent_const_TypeR_const_MouseButtonR_const_PointR_int(typ: *const crate::viz::MouseEvent_Type, button: *const crate::viz::MouseEvent_MouseButton, pointer: *const core::Point, modifiers: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_MouseEvent_propType_const(instance: *const c_void, ocvrs_return: *mut crate::viz::MouseEvent_Type);
pub fn cv_viz_MouseEvent_propType_const_Type(instance: *mut c_void, val: crate::viz::MouseEvent_Type);
pub fn cv_viz_MouseEvent_propButton_const(instance: *const c_void, ocvrs_return: *mut crate::viz::MouseEvent_MouseButton);
pub fn cv_viz_MouseEvent_propButton_const_MouseButton(instance: *mut c_void, val: crate::viz::MouseEvent_MouseButton);
pub fn cv_viz_MouseEvent_propPointer_const(instance: *const c_void, ocvrs_return: *mut core::Point);
pub fn cv_viz_MouseEvent_propPointer_const_Point(instance: *mut c_void, val: *const core::Point);
pub fn cv_viz_MouseEvent_propModifiers_const(instance: *const c_void) -> i32;
pub fn cv_viz_MouseEvent_propModifiers_const_int(instance: *mut c_void, val: i32);
pub fn cv_viz_MouseEvent_delete(instance: *mut c_void);
pub fn cv_viz_Viz3d_Viz3d_const_StringR(window_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Viz3d_Viz3d(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Viz3d_Viz3d_const_Viz3dR(unnamed: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Viz3d_operatorST_const_Viz3dR(instance: *mut c_void, unnamed: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_showWidget_const_StringR_const_WidgetR_const_Affine3dR(instance: *mut c_void, id: *const c_char, widget: *const c_void, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_showWidget_const_StringR_const_WidgetR(instance: *mut c_void, id: *const c_char, widget: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_removeWidget_const_StringR(instance: *mut c_void, id: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_getWidget_const_const_StringR(instance: *const c_void, id: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Viz3d_removeAllWidgets(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_showImage_const__InputArrayR_const_SizeR(instance: *mut c_void, image: *const c_void, window_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_showImage_const__InputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setWidgetPose_const_StringR_const_Affine3dR(instance: *mut c_void, id: *const c_char, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_updateWidgetPose_const_StringR_const_Affine3dR(instance: *mut c_void, id: *const c_char, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_getWidgetPose_const_const_StringR(instance: *const c_void, id: *const c_char, ocvrs_return: *mut Result<core::Affine3d>);
pub fn cv_viz_Viz3d_setCamera_const_CameraR(instance: *mut c_void, camera: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_getCamera_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Viz3d_getViewerPose_const(instance: *const c_void, ocvrs_return: *mut Result<core::Affine3d>);
pub fn cv_viz_Viz3d_setViewerPose_const_Affine3dR(instance: *mut c_void, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_resetCameraViewpoint_const_StringR(instance: *mut c_void, id: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_resetCamera(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_convertToWindowCoordinates_const_Point3dR_Point3dR(instance: *mut c_void, pt: *const core::Point3d, window_coord: *mut core::Point3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_converTo3DRay_const_Point3dR_Point3dR_Vec3dR(instance: *mut c_void, window_coord: *const core::Point3d, origin: *mut core::Point3d, direction: *mut core::Vec3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_getWindowSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_viz_Viz3d_setWindowSize_const_SizeR(instance: *mut c_void, window_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_getWindowName_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Viz3d_getScreenshot_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Viz3d_saveScreenshot_const_StringR(instance: *mut c_void, file: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setWindowPosition_const_PointR(instance: *mut c_void, window_position: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setFullScreen_bool(instance: *mut c_void, mode: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setFullScreen(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setBackgroundColor_const_ColorR_const_ColorR(instance: *mut c_void, color: *const c_void, color2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setBackgroundColor(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setBackgroundTexture_const__InputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setBackgroundTexture(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setBackgroundMeshLab(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_spin(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_spinOnce_int_bool(instance: *mut c_void, time: i32, force_redraw: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_spinOnce(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setOffScreenRendering(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_removeAllLights(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_addLight_const_Vec3dR_const_Vec3dR_const_ColorR_const_ColorR_const_ColorR_const_ColorR(instance: *mut c_void, position: *const core::Vec3d, focal_point: *const core::Vec3d, color: *const c_void, diffuse_color: *const c_void, ambient_color: *const c_void, specular_color: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_addLight_const_Vec3dR(instance: *mut c_void, position: *const core::Vec3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_wasStopped_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_viz_Viz3d_close(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_registerKeyboardCallback_KeyboardCallback_voidX(instance: *mut c_void, callback: Option<unsafe extern "C" fn(*const c_void, *mut c_void) -> ()>, cookie: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_registerMouseCallback_MouseCallback_voidX(instance: *mut c_void, callback: Option<unsafe extern "C" fn(*const c_void, *mut c_void) -> ()>, cookie: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setRenderingProperty_const_StringR_int_double(instance: *mut c_void, id: *const c_char, property: i32, value: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_getRenderingProperty_const_StringR_int(instance: *mut c_void, id: *const c_char, property: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_viz_Viz3d_setRepresentation_int(instance: *mut c_void, representation: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setGlobalWarnings_bool(instance: *mut c_void, enabled: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_setGlobalWarnings(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Viz3d_delete(instance: *mut c_void);
pub fn cv_viz_WArrow_WArrow_const_Point3dR_const_Point3dR_double_const_ColorR(pt1: *const core::Point3d, pt2: *const core::Point3d, thickness: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WArrow_WArrow_const_Point3dR_const_Point3dR(pt1: *const core::Point3d, pt2: *const core::Point3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WArrow_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WArrow_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WArrow_delete(instance: *mut c_void);
pub fn cv_viz_WCameraPosition_WCameraPosition_double(scale: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition_const_Matx33dR_double_const_ColorR(k: *const core::Matx33d, scale: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition_const_Matx33dR(k: *const core::Matx33d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition_const_Vec2dR_double_const_ColorR(fov: *const core::Vec2d, scale: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition_const_Vec2dR(fov: *const core::Vec2d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition_const_Matx33dR_const__InputArrayR_double_const_ColorR(k: *const core::Matx33d, image: *const c_void, scale: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition_const_Matx33dR_const__InputArrayR(k: *const core::Matx33d, image: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition_const_Vec2dR_const__InputArrayR_double_const_ColorR(fov: *const core::Vec2d, image: *const c_void, scale: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_WCameraPosition_const_Vec2dR_const__InputArrayR(fov: *const core::Vec2d, image: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCameraPosition_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCameraPosition_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCameraPosition_delete(instance: *mut c_void);
pub fn cv_viz_WCircle_WCircle_double_double_const_ColorR(radius: f64, thickness: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCircle_WCircle_double(radius: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCircle_WCircle_double_const_Point3dR_const_Vec3dR_double_const_ColorR(radius: f64, center: *const core::Point3d, normal: *const core::Vec3d, thickness: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCircle_WCircle_double_const_Point3dR_const_Vec3dR(radius: f64, center: *const core::Point3d, normal: *const core::Vec3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCircle_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCircle_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCircle_delete(instance: *mut c_void);
pub fn cv_viz_WCloud_WCloud_const__InputArrayR_const__InputArrayR(cloud: *const c_void, colors: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCloud_WCloud_const__InputArrayR_const_ColorR(cloud: *const c_void, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCloud_WCloud_const__InputArrayR(cloud: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCloud_WCloud_const__InputArrayR_const__InputArrayR_const__InputArrayR(cloud: *const c_void, colors: *const c_void, normals: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCloud_WCloud_const__InputArrayR_const_ColorR_const__InputArrayR(cloud: *const c_void, color: *const c_void, normals: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCloud_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCloud_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCloud_delete(instance: *mut c_void);
pub fn cv_viz_WCloudCollection_WCloudCollection(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCloudCollection_addCloud_const__InputArrayR_const__InputArrayR_const_Affine3dR(instance: *mut c_void, cloud: *const c_void, colors: *const c_void, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WCloudCollection_addCloud_const__InputArrayR_const__InputArrayR(instance: *mut c_void, cloud: *const c_void, colors: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WCloudCollection_addCloud_const__InputArrayR_const_ColorR_const_Affine3dR(instance: *mut c_void, cloud: *const c_void, color: *const c_void, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WCloudCollection_addCloud_const__InputArrayR(instance: *mut c_void, cloud: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WCloudCollection_finalize(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WCloudCollection_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCloudCollection_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCloudCollection_delete(instance: *mut c_void);
pub fn cv_viz_WCloudNormals_WCloudNormals_const__InputArrayR_const__InputArrayR_int_double_const_ColorR(cloud: *const c_void, normals: *const c_void, level: i32, scale: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCloudNormals_WCloudNormals_const__InputArrayR_const__InputArrayR(cloud: *const c_void, normals: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCloudNormals_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCloudNormals_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCloudNormals_delete(instance: *mut c_void);
pub fn cv_viz_WCone_WCone_double_double_int_const_ColorR(length: f64, radius: f64, resolution: i32, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCone_WCone_double_double(length: f64, radius: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCone_WCone_double_const_Point3dR_const_Point3dR_int_const_ColorR(radius: f64, center: *const core::Point3d, tip: *const core::Point3d, resolution: i32, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCone_WCone_double_const_Point3dR_const_Point3dR(radius: f64, center: *const core::Point3d, tip: *const core::Point3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCone_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCone_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCone_delete(instance: *mut c_void);
pub fn cv_viz_WCoordinateSystem_WCoordinateSystem_double(scale: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCoordinateSystem_WCoordinateSystem(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCoordinateSystem_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCoordinateSystem_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCoordinateSystem_delete(instance: *mut c_void);
pub fn cv_viz_WCube_WCube_const_Point3dR_const_Point3dR_bool_const_ColorR(min_point: *const core::Point3d, max_point: *const core::Point3d, wire_frame: bool, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCube_WCube(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCube_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCube_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCube_delete(instance: *mut c_void);
pub fn cv_viz_WCylinder_WCylinder_const_Point3dR_const_Point3dR_double_int_const_ColorR(axis_point1: *const core::Point3d, axis_point2: *const core::Point3d, radius: f64, numsides: i32, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCylinder_WCylinder_const_Point3dR_const_Point3dR_double(axis_point1: *const core::Point3d, axis_point2: *const core::Point3d, radius: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WCylinder_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCylinder_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WCylinder_delete(instance: *mut c_void);
pub fn cv_viz_WGrid_WGrid_const_Vec2iR_const_Vec2dR_const_ColorR(cells: *const core::Vec2i, cells_spacing: *const core::Vec2d, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WGrid_WGrid(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WGrid_WGrid_const_Point3dR_const_Vec3dR_const_Vec3dR_const_Vec2iR_const_Vec2dR_const_ColorR(center: *const core::Point3d, normal: *const core::Vec3d, new_yaxis: *const core::Vec3d, cells: *const core::Vec2i, cells_spacing: *const core::Vec2d, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WGrid_WGrid_const_Point3dR_const_Vec3dR_const_Vec3dR(center: *const core::Point3d, normal: *const core::Vec3d, new_yaxis: *const core::Vec3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WGrid_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WGrid_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WGrid_delete(instance: *mut c_void);
pub fn cv_viz_WImage3D_WImage3D_const__InputArrayR_const_Size2dR(image: *const c_void, size: *const core::Size2d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WImage3D_WImage3D_const__InputArrayR_const_Size2dR_const_Vec3dR_const_Vec3dR_const_Vec3dR(image: *const c_void, size: *const core::Size2d, center: *const core::Vec3d, normal: *const core::Vec3d, up_vector: *const core::Vec3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WImage3D_setImage_const__InputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WImage3D_setSize_const_SizeR(instance: *mut c_void, size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WImage3D_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WImage3D_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WImage3D_delete(instance: *mut c_void);
pub fn cv_viz_WImageOverlay_WImageOverlay_const__InputArrayR_const_RectR(image: *const c_void, rect: *const core::Rect, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WImageOverlay_setImage_const__InputArrayR(instance: *mut c_void, image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WImageOverlay_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WImageOverlay_to_Widget2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WImageOverlay_delete(instance: *mut c_void);
pub fn cv_viz_WLine_WLine_const_Point3dR_const_Point3dR_const_ColorR(pt1: *const core::Point3d, pt2: *const core::Point3d, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WLine_WLine_const_Point3dR_const_Point3dR(pt1: *const core::Point3d, pt2: *const core::Point3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WLine_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WLine_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WLine_delete(instance: *mut c_void);
pub fn cv_viz_WMesh_WMesh_const_MeshR(mesh: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WMesh_WMesh_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR(cloud: *const c_void, polygons: *const c_void, colors: *const c_void, normals: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WMesh_WMesh_const__InputArrayR_const__InputArrayR(cloud: *const c_void, polygons: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WMesh_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WMesh_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WMesh_delete(instance: *mut c_void);
pub fn cv_viz_WPaintedCloud_WPaintedCloud_const__InputArrayR(cloud: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPaintedCloud_WPaintedCloud_const__InputArrayR_const_Point3dR_const_Point3dR(cloud: *const c_void, p1: *const core::Point3d, p2: *const core::Point3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPaintedCloud_WPaintedCloud_const__InputArrayR_const_Point3dR_const_Point3dR_const_ColorR_const_Color(cloud: *const c_void, p1: *const core::Point3d, p2: *const core::Point3d, c1: *const c_void, c2: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPaintedCloud_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WPaintedCloud_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WPaintedCloud_delete(instance: *mut c_void);
pub fn cv_viz_WPlane_WPlane_const_Size2dR_const_ColorR(size: *const core::Size2d, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPlane_WPlane(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPlane_WPlane_const_Point3dR_const_Vec3dR_const_Vec3dR_const_Size2dR_const_ColorR(center: *const core::Point3d, normal: *const core::Vec3d, new_yaxis: *const core::Vec3d, size: *const core::Size2d, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPlane_WPlane_const_Point3dR_const_Vec3dR_const_Vec3dR(center: *const core::Point3d, normal: *const core::Vec3d, new_yaxis: *const core::Vec3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPlane_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WPlane_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WPlane_delete(instance: *mut c_void);
pub fn cv_viz_WPolyLine_WPolyLine_const__InputArrayR_const__InputArrayR(points: *const c_void, colors: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPolyLine_WPolyLine_const__InputArrayR_const_ColorR(points: *const c_void, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPolyLine_WPolyLine_const__InputArrayR(points: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WPolyLine_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WPolyLine_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WPolyLine_delete(instance: *mut c_void);
pub fn cv_viz_WSphere_WSphere_const_Point3dR_double_int_const_ColorR(center: *const core::Point3d, radius: f64, sphere_resolution: i32, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WSphere_WSphere_const_Point3dR_double(center: *const core::Point3d, radius: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WSphere_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WSphere_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WSphere_delete(instance: *mut c_void);
pub fn cv_viz_WText_WText_const_StringR_const_PointR_int_const_ColorR(text: *const c_char, pos: *const core::Point, font_size: i32, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WText_WText_const_StringR_const_PointR(text: *const c_char, pos: *const core::Point, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WText_setText_const_StringR(instance: *mut c_void, text: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WText_getText_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WText_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WText_to_Widget2D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WText_delete(instance: *mut c_void);
pub fn cv_viz_WText3D_WText3D_const_StringR_const_Point3dR_double_bool_const_ColorR(text: *const c_char, position: *const core::Point3d, text_scale: f64, face_camera: bool, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WText3D_WText3D_const_StringR_const_Point3dR(text: *const c_char, position: *const core::Point3d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WText3D_setText_const_StringR(instance: *mut c_void, text: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WText3D_getText_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WText3D_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WText3D_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WText3D_delete(instance: *mut c_void);
pub fn cv_viz_WTrajectory_WTrajectory_const__InputArrayR_int_double_const_ColorR(path: *const c_void, display_mode: i32, scale: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WTrajectory_WTrajectory_const__InputArrayR(path: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WTrajectory_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WTrajectory_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WTrajectory_delete(instance: *mut c_void);
pub fn cv_viz_WTrajectoryFrustums_WTrajectoryFrustums_const__InputArrayR_const_Matx33dR_double_const_ColorR(path: *const c_void, k: *const core::Matx33d, scale: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WTrajectoryFrustums_WTrajectoryFrustums_const__InputArrayR_const_Matx33dR(path: *const c_void, k: *const core::Matx33d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WTrajectoryFrustums_WTrajectoryFrustums_const__InputArrayR_const_Vec2dR_double_const_ColorR(path: *const c_void, fov: *const core::Vec2d, scale: f64, color: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WTrajectoryFrustums_WTrajectoryFrustums_const__InputArrayR_const_Vec2dR(path: *const c_void, fov: *const core::Vec2d, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WTrajectoryFrustums_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WTrajectoryFrustums_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WTrajectoryFrustums_delete(instance: *mut c_void);
pub fn cv_viz_WTrajectorySpheres_WTrajectorySpheres_const__InputArrayR_double_double_const_ColorR_const_ColorR(path: *const c_void, line_length: f64, radius: f64, from: *const c_void, to: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WTrajectorySpheres_WTrajectorySpheres_const__InputArrayR(path: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WTrajectorySpheres_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WTrajectorySpheres_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WTrajectorySpheres_delete(instance: *mut c_void);
pub fn cv_viz_WWidgetMerger_WWidgetMerger(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_WWidgetMerger_addWidget_const_Widget3DR_const_Affine3dR(instance: *mut c_void, widget: *const c_void, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WWidgetMerger_addWidget_const_Widget3DR(instance: *mut c_void, widget: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WWidgetMerger_finalize(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_WWidgetMerger_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WWidgetMerger_to_Widget3D(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_WWidgetMerger_delete(instance: *mut c_void);
pub fn cv_viz_Widget_Widget(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Widget_Widget_const_WidgetR(other: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Widget_operatorST_const_WidgetR(instance: *mut c_void, other: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Widget_fromPlyFile_const_StringR(file_name: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Widget_setRenderingProperty_int_double(instance: *mut c_void, property: i32, value: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Widget_getRenderingProperty_const_int(instance: *const c_void, property: i32, ocvrs_return: *mut Result<f64>);
pub fn cv_viz_Widget_delete(instance: *mut c_void);
pub fn cv_viz_Widget2D_Widget2D(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Widget2D_setColor_const_ColorR(instance: *mut c_void, color: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Widget2D_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_Widget2D_delete(instance: *mut c_void);
pub fn cv_viz_Widget3D_Widget3D(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_viz_Widget3D_setPose_const_Affine3dR(instance: *mut c_void, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Widget3D_updatePose_const_Affine3dR(instance: *mut c_void, pose: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Widget3D_getPose_const(instance: *const c_void, ocvrs_return: *mut Result<core::Affine3d>);
pub fn cv_viz_Widget3D_applyTransform_const_Affine3dR(instance: *mut c_void, transform: *const core::Affine3d, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Widget3D_setColor_const_ColorR(instance: *mut c_void, color: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_viz_Widget3D_to_Widget(instance: *mut c_void) -> *mut c_void;
pub fn cv_viz_Widget3D_delete(instance: *mut c_void);
