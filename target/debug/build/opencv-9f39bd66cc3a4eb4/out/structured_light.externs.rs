pub fn cv_structured_light_GrayCodePattern_create_const_ParamsR(parameters: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_structured_light_GrayCodePattern_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_structured_light_GrayCodePattern_create_int_int(width: i32, height: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_structured_light_GrayCodePattern_getNumberOfPatternImages_const(instance: *const c_void, ocvrs_return: *mut Result<size_t>);
pub fn cv_structured_light_GrayCodePattern_setWhiteThreshold_size_t(instance: *mut c_void, value: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_GrayCodePattern_setBlackThreshold_size_t(instance: *mut c_void, value: size_t, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_GrayCodePattern_getImagesForShadowMasks_const_const__InputOutputArrayR_const__InputOutputArrayR(instance: *const c_void, black_image: *const c_void, white_image: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_GrayCodePattern_getProjPixel_const_const__InputArrayR_int_int_PointR(instance: *const c_void, pattern_images: *const c_void, x: i32, y: i32, proj_pix: *mut core::Point, ocvrs_return: *mut Result<bool>);
pub fn cv_structured_light_GrayCodePattern_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_structured_light_GrayCodePattern_to_StructuredLightPattern(instance: *mut c_void) -> *mut c_void;
pub fn cv_structured_light_GrayCodePattern_delete(instance: *mut c_void);
pub fn cv_structured_light_GrayCodePattern_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_structured_light_GrayCodePattern_Params_propWidth_const(instance: *const c_void) -> i32;
pub fn cv_structured_light_GrayCodePattern_Params_propWidth_const_int(instance: *mut c_void, val: i32);
pub fn cv_structured_light_GrayCodePattern_Params_propHeight_const(instance: *const c_void) -> i32;
pub fn cv_structured_light_GrayCodePattern_Params_propHeight_const_int(instance: *mut c_void, val: i32);
pub fn cv_structured_light_GrayCodePattern_Params_delete(instance: *mut c_void);
pub fn cv_structured_light_SinusoidalPattern_create_PtrLParamsG(parameters: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_structured_light_SinusoidalPattern_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_structured_light_SinusoidalPattern_computePhaseMap_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_const__InputArrayR(instance: *mut c_void, pattern_images: *const c_void, wrapped_phase_map: *const c_void, shadow_mask: *const c_void, fundamental: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_SinusoidalPattern_computePhaseMap_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, pattern_images: *const c_void, wrapped_phase_map: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_SinusoidalPattern_unwrapPhaseMap_const__InputArrayR_const__OutputArrayR_Size_const__InputArrayR(instance: *mut c_void, wrapped_phase_map: *const c_void, unwrapped_phase_map: *const c_void, cam_size: *const core::Size, shadow_mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_SinusoidalPattern_unwrapPhaseMap_const__InputArrayR_const__OutputArrayR_Size(instance: *mut c_void, wrapped_phase_map: *const c_void, unwrapped_phase_map: *const c_void, cam_size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_SinusoidalPattern_findProCamMatches_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, proj_unwrapped_phase_map: *const c_void, cam_unwrapped_phase_map: *const c_void, matches: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_SinusoidalPattern_computeDataModulationTerm_const__InputArrayR_const__OutputArrayR_const__InputArrayR(instance: *mut c_void, pattern_images: *const c_void, data_modulation_term: *const c_void, shadow_mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_structured_light_SinusoidalPattern_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_structured_light_SinusoidalPattern_to_StructuredLightPattern(instance: *mut c_void) -> *mut c_void;
pub fn cv_structured_light_SinusoidalPattern_delete(instance: *mut c_void);
pub fn cv_structured_light_SinusoidalPattern_Params_Params(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_structured_light_SinusoidalPattern_Params_propWidth_const(instance: *const c_void) -> i32;
pub fn cv_structured_light_SinusoidalPattern_Params_propWidth_const_int(instance: *mut c_void, val: i32);
pub fn cv_structured_light_SinusoidalPattern_Params_propHeight_const(instance: *const c_void) -> i32;
pub fn cv_structured_light_SinusoidalPattern_Params_propHeight_const_int(instance: *mut c_void, val: i32);
pub fn cv_structured_light_SinusoidalPattern_Params_propNbrOfPeriods_const(instance: *const c_void) -> i32;
pub fn cv_structured_light_SinusoidalPattern_Params_propNbrOfPeriods_const_int(instance: *mut c_void, val: i32);
pub fn cv_structured_light_SinusoidalPattern_Params_propShiftValue_const(instance: *const c_void) -> f32;
pub fn cv_structured_light_SinusoidalPattern_Params_propShiftValue_const_float(instance: *mut c_void, val: f32);
pub fn cv_structured_light_SinusoidalPattern_Params_propMethodId_const(instance: *const c_void) -> i32;
pub fn cv_structured_light_SinusoidalPattern_Params_propMethodId_const_int(instance: *mut c_void, val: i32);
pub fn cv_structured_light_SinusoidalPattern_Params_propNbrOfPixelsBetweenMarkers_const(instance: *const c_void) -> i32;
pub fn cv_structured_light_SinusoidalPattern_Params_propNbrOfPixelsBetweenMarkers_const_int(instance: *mut c_void, val: i32);
pub fn cv_structured_light_SinusoidalPattern_Params_propHorizontal_const(instance: *const c_void) -> bool;
pub fn cv_structured_light_SinusoidalPattern_Params_propHorizontal_const_bool(instance: *mut c_void, val: bool);
pub fn cv_structured_light_SinusoidalPattern_Params_propSetMarkers_const(instance: *const c_void) -> bool;
pub fn cv_structured_light_SinusoidalPattern_Params_propSetMarkers_const_bool(instance: *mut c_void, val: bool);
pub fn cv_structured_light_SinusoidalPattern_Params_propMarkersLocation_const(instance: *const c_void) -> *mut c_void;
pub fn cv_structured_light_SinusoidalPattern_Params_propMarkersLocation_const_vectorLPoint2fG(instance: *mut c_void, val: *const c_void);
pub fn cv_structured_light_SinusoidalPattern_Params_delete(instance: *mut c_void);
pub fn cv_structured_light_StructuredLightPattern_generate_const__OutputArrayR(instance: *mut c_void, pattern_images: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_structured_light_StructuredLightPattern_decode_const_const_vectorLvectorLMatGGR_const__OutputArrayR_const__InputArrayR_const__InputArrayR_int(instance: *const c_void, pattern_images: *const c_void, disparity_map: *const c_void, black_images: *const c_void, white_images: *const c_void, flags: i32, ocvrs_return: *mut Result<bool>);
pub fn cv_structured_light_StructuredLightPattern_decode_const_const_vectorLvectorLMatGGR_const__OutputArrayR(instance: *const c_void, pattern_images: *const c_void, disparity_map: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_structured_light_StructuredLightPattern_to_GrayCodePattern(instance: *mut c_void) -> *mut c_void;
pub fn cv_structured_light_StructuredLightPattern_to_SinusoidalPattern(instance: *mut c_void) -> *mut c_void;
pub fn cv_structured_light_StructuredLightPattern_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_structured_light_StructuredLightPattern_delete(instance: *mut c_void);
