pub fn cv_PtrLcv_ximgproc_FastBilateralSolverFilterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_FastBilateralSolverFilterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_ximgproc_FastBilateralSolverFilterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_ximgproc_FastBilateralSolverFilterG_to_PtrOfAlgorithm(instance: *mut c_void) -> *mut c_void;
