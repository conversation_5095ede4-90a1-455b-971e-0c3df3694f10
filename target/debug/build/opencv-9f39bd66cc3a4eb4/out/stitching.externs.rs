pub fn cv_detail_autoDetectWaveCorrectKind_const_vectorLMatGR(rmats: *const c_void, ocvrs_return: *mut Result<crate::stitching::Detail_WaveCorrectKind>);
pub fn cv_detail_computeImageFeatures_const_PtrLFeature2DGR_const__InputArrayR_ImageFeaturesR(features_finder: *const c_void, image: *const c_void, features: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_computeImageFeatures_const_PtrLFeature2DGR_const__InputArrayR_ImageFeaturesR_const__InputArrayR(features_finder: *const c_void, image: *const c_void, features: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_computeImageFeatures_const_PtrLFeature2DGR_const__InputArrayR_vectorLImageFeaturesGR(features_finder: *const c_void, images: *const c_void, features: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_computeImageFeatures_const_PtrLFeature2DGR_const__InputArrayR_vectorLImageFeaturesGR_const__InputArrayR(features_finder: *const c_void, images: *const c_void, features: *mut c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_createLaplacePyrGpu_const__InputArrayR_int_vectorLUMatGR(img: *const c_void, num_levels: i32, pyr: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_createLaplacePyr_const__InputArrayR_int_vectorLUMatGR(img: *const c_void, num_levels: i32, pyr: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_createWeightMap_const__InputArrayR_float_const__InputOutputArrayR(mask: *const c_void, sharpness: f32, weight: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_findMaxSpanningTree_int_const_vectorLMatchesInfoGR_GraphR_vectorLintGR(num_images: i32, pairwise_matches: *const c_void, span_tree: *mut c_void, centers: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_leaveBiggestComponent_vectorLImageFeaturesGR_vectorLMatchesInfoGR_float(features: *mut c_void, pairwise_matches: *mut c_void, conf_threshold: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_matchesGraphAsString_vectorLStringGR_vectorLMatchesInfoGR_float(pathes: *mut c_void, pairwise_matches: *mut c_void, conf_threshold: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_normalizeUsingWeightMap_const__InputArrayR_const__InputOutputArrayR(weight: *const c_void, src: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_overlapRoi_Point_Point_Size_Size_RectR(tl1: *const core::Point, tl2: *const core::Point, sz1: *const core::Size, sz2: *const core::Size, roi: *mut core::Rect, ocvrs_return: *mut Result<bool>);
pub fn cv_detail_restoreImageFromLaplacePyrGpu_vectorLUMatGR(pyr: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_restoreImageFromLaplacePyr_vectorLUMatGR(pyr: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_resultRoiIntersection_const_vectorLPointGR_const_vectorLSizeGR(corners: *const c_void, sizes: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_resultRoi_const_vectorLPointGR_const_vectorLSizeGR(corners: *const c_void, sizes: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_resultRoi_const_vectorLPointGR_const_vectorLUMatGR(corners: *const c_void, images: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_resultTl_const_vectorLPointGR(corners: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_selectRandomSubset_int_int_vectorLintGR(count: i32, size: i32, subset: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_stitchingLogLevel(ocvrs_return: *mut Result<i32>);
pub fn cv_detail_waveCorrect_vectorLMatGR_WaveCorrectKind(rmats: *mut c_void, kind: crate::stitching::Detail_WaveCorrectKind, ocvrs_return: *mut ResultVoid);
pub fn cv_AffineWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_AffineWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_AffineWarper_delete(instance: *mut c_void);
pub fn cv_CompressedRectilinearPortraitWarper_CompressedRectilinearPortraitWarper_float_float(a: f32, b: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CompressedRectilinearPortraitWarper_CompressedRectilinearPortraitWarper(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CompressedRectilinearPortraitWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CompressedRectilinearPortraitWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_CompressedRectilinearPortraitWarper_delete(instance: *mut c_void);
pub fn cv_CompressedRectilinearWarper_CompressedRectilinearWarper_float_float(a: f32, b: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CompressedRectilinearWarper_CompressedRectilinearWarper(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CompressedRectilinearWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CompressedRectilinearWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_CompressedRectilinearWarper_delete(instance: *mut c_void);
pub fn cv_CylindricalWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CylindricalWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_CylindricalWarper_delete(instance: *mut c_void);
pub fn cv_FisheyeWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_FisheyeWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_FisheyeWarper_delete(instance: *mut c_void);
pub fn cv_MercatorWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_MercatorWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_MercatorWarper_delete(instance: *mut c_void);
pub fn cv_PaniniPortraitWarper_PaniniPortraitWarper_float_float(a: f32, b: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PaniniPortraitWarper_PaniniPortraitWarper(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PaniniPortraitWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PaniniPortraitWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PaniniPortraitWarper_delete(instance: *mut c_void);
pub fn cv_PaniniWarper_PaniniWarper_float_float(a: f32, b: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PaniniWarper_PaniniWarper(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PaniniWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PaniniWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PaniniWarper_delete(instance: *mut c_void);
pub fn cv_PlaneWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PlaneWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_PlaneWarper_delete(instance: *mut c_void);
pub fn cv_PyRotationWarper_PyRotationWarper_String_float(typ: *const c_char, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PyRotationWarper_PyRotationWarper(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_PyRotationWarper_warpPoint_const_Point2fR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_PyRotationWarper_warpPointBackward_const_Point2fR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_PyRotationWarper_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_PyRotationWarper_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_PyRotationWarper_warpBackward_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_Size_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst_size: *const core::Size, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_PyRotationWarper_warpRoi_Size_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_PyRotationWarper_getScale_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_PyRotationWarper_setScale_float(instance: *mut c_void, unnamed: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_PyRotationWarper_delete(instance: *mut c_void);
pub fn cv_SphericalWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_SphericalWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_SphericalWarper_delete(instance: *mut c_void);
pub fn cv_StereographicWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_StereographicWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_StereographicWarper_delete(instance: *mut c_void);
pub fn cv_Stitcher_create_Mode(mode: crate::stitching::Stitcher_Mode, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_registrationResol_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_Stitcher_setRegistrationResol_double(instance: *mut c_void, resol_mpx: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_seamEstimationResol_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_Stitcher_setSeamEstimationResol_double(instance: *mut c_void, resol_mpx: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_compositingResol_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_Stitcher_setCompositingResol_double(instance: *mut c_void, resol_mpx: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_panoConfidenceThresh_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_Stitcher_setPanoConfidenceThresh_double(instance: *mut c_void, conf_thresh: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_waveCorrection_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_Stitcher_setWaveCorrection_bool(instance: *mut c_void, flag: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_interpolationFlags_const(instance: *const c_void, ocvrs_return: *mut Result<crate::imgproc::InterpolationFlags>);
pub fn cv_Stitcher_setInterpolationFlags_InterpolationFlags(instance: *mut c_void, interp_flags: crate::imgproc::InterpolationFlags, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_waveCorrectKind_const(instance: *const c_void, ocvrs_return: *mut Result<crate::stitching::Detail_WaveCorrectKind>);
pub fn cv_Stitcher_setWaveCorrectKind_WaveCorrectKind(instance: *mut c_void, kind: crate::stitching::Detail_WaveCorrectKind, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_featuresFinder(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_featuresFinder_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setFeaturesFinder_PtrLFeature2DG(instance: *mut c_void, features_finder: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_featuresMatcher(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_featuresMatcher_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setFeaturesMatcher_PtrLFeaturesMatcherG(instance: *mut c_void, features_matcher: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_matchingMask_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setMatchingMask_const_UMatR(instance: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_bundleAdjuster(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_bundleAdjuster_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setBundleAdjuster_PtrLBundleAdjusterBaseG(instance: *mut c_void, bundle_adjuster: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_estimator(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_estimator_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setEstimator_PtrLEstimatorG(instance: *mut c_void, estimator: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_warper(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_warper_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setWarper_PtrLWarperCreatorG(instance: *mut c_void, creator: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_exposureCompensator(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_exposureCompensator_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setExposureCompensator_PtrLExposureCompensatorG(instance: *mut c_void, exposure_comp: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_seamFinder(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_seamFinder_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setSeamFinder_PtrLSeamFinderG(instance: *mut c_void, seam_finder: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_blender(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_blender_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_setBlender_PtrLBlenderG(instance: *mut c_void, b: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Stitcher_estimateTransform_const__InputArrayR_const__InputArrayR(instance: *mut c_void, images: *const c_void, masks: *const c_void, ocvrs_return: *mut Result<crate::stitching::Stitcher_Status>);
pub fn cv_Stitcher_estimateTransform_const__InputArrayR(instance: *mut c_void, images: *const c_void, ocvrs_return: *mut Result<crate::stitching::Stitcher_Status>);
pub fn cv_Stitcher_setTransform_const__InputArrayR_const_vectorLCameraParamsGR_const_vectorLintGR(instance: *mut c_void, images: *const c_void, cameras: *const c_void, component: *const c_void, ocvrs_return: *mut Result<crate::stitching::Stitcher_Status>);
pub fn cv_Stitcher_setTransform_const__InputArrayR_const_vectorLCameraParamsGR(instance: *mut c_void, images: *const c_void, cameras: *const c_void, ocvrs_return: *mut Result<crate::stitching::Stitcher_Status>);
pub fn cv_Stitcher_composePanorama_const__OutputArrayR(instance: *mut c_void, pano: *const c_void, ocvrs_return: *mut Result<crate::stitching::Stitcher_Status>);
pub fn cv_Stitcher_composePanorama_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, images: *const c_void, pano: *const c_void, ocvrs_return: *mut Result<crate::stitching::Stitcher_Status>);
pub fn cv_Stitcher_stitch_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, images: *const c_void, pano: *const c_void, ocvrs_return: *mut Result<crate::stitching::Stitcher_Status>);
pub fn cv_Stitcher_stitch_const__InputArrayR_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, images: *const c_void, masks: *const c_void, pano: *const c_void, ocvrs_return: *mut Result<crate::stitching::Stitcher_Status>);
pub fn cv_Stitcher_component_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_cameras_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_workScale_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_Stitcher_resultMask_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_Stitcher_delete(instance: *mut c_void);
pub fn cv_TransverseMercatorWarper_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_TransverseMercatorWarper_to_WarperCreator(instance: *mut c_void) -> *mut c_void;
pub fn cv_TransverseMercatorWarper_delete(instance: *mut c_void);
pub fn cv_WarperCreator_create_const_float(instance: *const c_void, scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_WarperCreator_to_AffineWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_CompressedRectilinearPortraitWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_CompressedRectilinearWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_CylindricalWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_FisheyeWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_MercatorWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_PaniniPortraitWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_PaniniWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_PlaneWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_SphericalWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_StereographicWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_to_TransverseMercatorWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_WarperCreator_delete(instance: *mut c_void);
pub fn cv_detail_AffineBasedEstimator_AffineBasedEstimator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_AffineBasedEstimator_to_Detail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_AffineBasedEstimator_delete(instance: *mut c_void);
pub fn cv_detail_AffineBestOf2NearestMatcher_AffineBestOf2NearestMatcher_bool_bool_float_int(full_affine: bool, try_use_gpu: bool, match_conf: f32, num_matches_thresh1: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_AffineBestOf2NearestMatcher_AffineBestOf2NearestMatcher(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_AffineBestOf2NearestMatcher_to_Detail_BestOf2NearestMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_AffineBestOf2NearestMatcher_to_Detail_FeaturesMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_AffineBestOf2NearestMatcher_delete(instance: *mut c_void);
pub fn cv_detail_AffineWarper_AffineWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_AffineWarper_AffineWarper(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_AffineWarper_warpPoint_const_Point2fR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, h: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_detail_AffineWarper_warpPointBackward_const_Point2fR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, h: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_detail_AffineWarper_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, h: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_AffineWarper_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, h: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_AffineWarper_warpRoi_Size_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, h: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_AffineWarper_to_Detail_PlaneWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_AffineWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_AffineWarper_delete(instance: *mut c_void);
pub fn cv_detail_BestOf2NearestMatcher_BestOf2NearestMatcher_bool_float_int_int(try_use_gpu: bool, match_conf: f32, num_matches_thresh1: i32, num_matches_thresh2: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BestOf2NearestMatcher_BestOf2NearestMatcher(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BestOf2NearestMatcher_collectGarbage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BestOf2NearestMatcher_create_bool_float_int_int(try_use_gpu: bool, match_conf: f32, num_matches_thresh1: i32, num_matches_thresh2: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BestOf2NearestMatcher_create(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BestOf2NearestMatcher_to_Detail_AffineBestOf2NearestMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BestOf2NearestMatcher_to_Detail_BestOf2NearestRangeMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BestOf2NearestMatcher_to_Detail_FeaturesMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BestOf2NearestMatcher_delete(instance: *mut c_void);
pub fn cv_detail_BestOf2NearestRangeMatcher_BestOf2NearestRangeMatcher_int_bool_float_int_int(range_width: i32, try_use_gpu: bool, match_conf: f32, num_matches_thresh1: i32, num_matches_thresh2: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BestOf2NearestRangeMatcher_BestOf2NearestRangeMatcher(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BestOf2NearestRangeMatcher_operator___const_vectorLImageFeaturesGR_vectorLMatchesInfoGR_const_UMatR(instance: *mut c_void, features: *const c_void, pairwise_matches: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BestOf2NearestRangeMatcher_operator___const_vectorLImageFeaturesGR_vectorLMatchesInfoGR(instance: *mut c_void, features: *const c_void, pairwise_matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BestOf2NearestRangeMatcher_to_Detail_BestOf2NearestMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BestOf2NearestRangeMatcher_to_Detail_FeaturesMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BestOf2NearestRangeMatcher_delete(instance: *mut c_void);
pub fn cv_detail_Blender_createDefault_int_bool(typ: i32, try_gpu: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_Blender_createDefault_int(typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_Blender_prepare_const_vectorLPointGR_const_vectorLSizeGR(instance: *mut c_void, corners: *const c_void, sizes: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_Blender_prepare_Rect(instance: *mut c_void, dst_roi: *const core::Rect, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_Blender_feed_const__InputArrayR_const__InputArrayR_Point(instance: *mut c_void, img: *const c_void, mask: *const c_void, tl: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_Blender_blend_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, dst: *const c_void, dst_mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_Blender_to_Detail_FeatherBlender(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Blender_to_Detail_MultiBandBlender(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Blender_delete(instance: *mut c_void);
pub fn cv_detail_BlocksChannelsCompensator_BlocksChannelsCompensator_int_int_int(bl_width: i32, bl_height: i32, nr_feeds: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BlocksChannelsCompensator_BlocksChannelsCompensator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BlocksChannelsCompensator_feed_const_vectorLPointGR_const_vectorLUMatGR_const_vectorLpairLcv_UMat__unsigned_charGGR(instance: *mut c_void, corners: *const c_void, images: *const c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksChannelsCompensator_to_Detail_BlocksCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BlocksChannelsCompensator_to_Detail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BlocksChannelsCompensator_delete(instance: *mut c_void);
pub fn cv_detail_BlocksCompensator_apply_int_Point_const__InputOutputArrayR_const__InputArrayR(instance: *mut c_void, index: i32, corner: *const core::Point, image: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksCompensator_getMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksCompensator_setMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksCompensator_setNrFeeds_int(instance: *mut c_void, nr_feeds: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksCompensator_getNrFeeds(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_detail_BlocksCompensator_setSimilarityThreshold_double(instance: *mut c_void, similarity_threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksCompensator_getSimilarityThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_detail_BlocksCompensator_setBlockSize_int_int(instance: *mut c_void, width: i32, height: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksCompensator_setBlockSize_Size(instance: *mut c_void, size: *const core::Size, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksCompensator_getBlockSize_const(instance: *const c_void, ocvrs_return: *mut Result<core::Size>);
pub fn cv_detail_BlocksCompensator_setNrGainsFilteringIterations_int(instance: *mut c_void, nr_iterations: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksCompensator_getNrGainsFilteringIterations_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_detail_BlocksCompensator_to_Detail_BlocksChannelsCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BlocksCompensator_to_Detail_BlocksGainCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BlocksCompensator_to_Detail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BlocksCompensator_delete(instance: *mut c_void);
pub fn cv_detail_BlocksGainCompensator_BlocksGainCompensator_int_int(bl_width: i32, bl_height: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BlocksGainCompensator_BlocksGainCompensator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BlocksGainCompensator_BlocksGainCompensator_int_int_int(bl_width: i32, bl_height: i32, nr_feeds: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BlocksGainCompensator_feed_const_vectorLPointGR_const_vectorLUMatGR_const_vectorLpairLcv_UMat__unsigned_charGGR(instance: *mut c_void, corners: *const c_void, images: *const c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksGainCompensator_apply_int_Point_const__InputOutputArrayR_const__InputArrayR(instance: *mut c_void, index: i32, corner: *const core::Point, image: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksGainCompensator_getMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksGainCompensator_setMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BlocksGainCompensator_to_Detail_BlocksCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BlocksGainCompensator_to_Detail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BlocksGainCompensator_delete(instance: *mut c_void);
pub fn cv_detail_BundleAdjusterAffine_BundleAdjusterAffine(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BundleAdjusterAffine_to_Detail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterAffine_to_Detail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterAffine_delete(instance: *mut c_void);
pub fn cv_detail_BundleAdjusterAffinePartial_BundleAdjusterAffinePartial(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BundleAdjusterAffinePartial_to_Detail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterAffinePartial_to_Detail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterAffinePartial_delete(instance: *mut c_void);
pub fn cv_detail_BundleAdjusterBase_refinementMask_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BundleAdjusterBase_setRefinementMask_const_MatR(instance: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BundleAdjusterBase_confThresh_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_detail_BundleAdjusterBase_setConfThresh_double(instance: *mut c_void, conf_thresh: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BundleAdjusterBase_termCriteria(instance: *mut c_void, ocvrs_return: *mut Result<core::TermCriteria>);
pub fn cv_detail_BundleAdjusterBase_setTermCriteria_const_TermCriteriaR(instance: *mut c_void, term_criteria: *const core::TermCriteria, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_BundleAdjusterBase_to_Detail_BundleAdjusterAffine(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterBase_to_Detail_BundleAdjusterAffinePartial(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterBase_to_Detail_BundleAdjusterRay(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterBase_to_Detail_BundleAdjusterReproj(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterBase_to_Detail_NoBundleAdjuster(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterBase_to_Detail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterBase_delete(instance: *mut c_void);
pub fn cv_detail_BundleAdjusterRay_BundleAdjusterRay(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BundleAdjusterRay_to_Detail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterRay_to_Detail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterRay_delete(instance: *mut c_void);
pub fn cv_detail_BundleAdjusterReproj_BundleAdjusterReproj(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_BundleAdjusterReproj_to_Detail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterReproj_to_Detail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_BundleAdjusterReproj_delete(instance: *mut c_void);
pub fn cv_detail_CameraParams_CameraParams(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CameraParams_CameraParams_const_CameraParamsR(other: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CameraParams_operatorST_const_CameraParamsR(instance: *mut c_void, other: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CameraParams_K_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CameraParams_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CameraParams_propFocal_const(instance: *const c_void) -> f64;
pub fn cv_detail_CameraParams_propFocal_const_double(instance: *mut c_void, val: f64);
pub fn cv_detail_CameraParams_propAspect_const(instance: *const c_void) -> f64;
pub fn cv_detail_CameraParams_propAspect_const_double(instance: *mut c_void, val: f64);
pub fn cv_detail_CameraParams_propPpx_const(instance: *const c_void) -> f64;
pub fn cv_detail_CameraParams_propPpx_const_double(instance: *mut c_void, val: f64);
pub fn cv_detail_CameraParams_propPpy_const(instance: *const c_void) -> f64;
pub fn cv_detail_CameraParams_propPpy_const_double(instance: *mut c_void, val: f64);
pub fn cv_detail_CameraParams_propR_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CameraParams_propR_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_CameraParams_propT_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_CameraParams_propT_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_CameraParams_delete(instance: *mut c_void);
pub fn cv_detail_ChannelsCompensator_ChannelsCompensator_int(nr_feeds: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_ChannelsCompensator_ChannelsCompensator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_ChannelsCompensator_feed_const_vectorLPointGR_const_vectorLUMatGR_const_vectorLpairLcv_UMat__unsigned_charGGR(instance: *mut c_void, corners: *const c_void, images: *const c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ChannelsCompensator_apply_int_Point_const__InputOutputArrayR_const__InputArrayR(instance: *mut c_void, index: i32, corner: *const core::Point, image: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ChannelsCompensator_getMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ChannelsCompensator_setMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ChannelsCompensator_setNrFeeds_int(instance: *mut c_void, nr_feeds: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ChannelsCompensator_getNrFeeds(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_detail_ChannelsCompensator_setSimilarityThreshold_double(instance: *mut c_void, similarity_threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ChannelsCompensator_getSimilarityThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_detail_ChannelsCompensator_gains_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_ChannelsCompensator_to_Detail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_ChannelsCompensator_delete(instance: *mut c_void);
pub fn cv_detail_CompressedRectilinearPortraitProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CompressedRectilinearPortraitProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CompressedRectilinearPortraitProjector_propA_const(instance: *const c_void) -> f32;
pub fn cv_detail_CompressedRectilinearPortraitProjector_propA_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_CompressedRectilinearPortraitProjector_propB_const(instance: *const c_void) -> f32;
pub fn cv_detail_CompressedRectilinearPortraitProjector_propB_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_CompressedRectilinearPortraitProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CompressedRectilinearPortraitProjector_delete(instance: *mut c_void);
pub fn cv_detail_CompressedRectilinearPortraitWarper_CompressedRectilinearPortraitWarper_float_float_float(scale: f32, a: f32, b: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CompressedRectilinearPortraitWarper_CompressedRectilinearPortraitWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CompressedRectilinearPortraitWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CompressedRectilinearPortraitWarper_delete(instance: *mut c_void);
pub fn cv_detail_CompressedRectilinearProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CompressedRectilinearProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CompressedRectilinearProjector_propA_const(instance: *const c_void) -> f32;
pub fn cv_detail_CompressedRectilinearProjector_propA_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_CompressedRectilinearProjector_propB_const(instance: *const c_void) -> f32;
pub fn cv_detail_CompressedRectilinearProjector_propB_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_CompressedRectilinearProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CompressedRectilinearProjector_delete(instance: *mut c_void);
pub fn cv_detail_CompressedRectilinearWarper_CompressedRectilinearWarper_float_float_float(scale: f32, a: f32, b: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CompressedRectilinearWarper_CompressedRectilinearWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CompressedRectilinearWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CompressedRectilinearWarper_delete(instance: *mut c_void);
pub fn cv_detail_CylindricalPortraitProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CylindricalPortraitProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CylindricalPortraitProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CylindricalPortraitProjector_delete(instance: *mut c_void);
pub fn cv_detail_CylindricalPortraitWarper_CylindricalPortraitWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CylindricalPortraitWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CylindricalPortraitWarper_delete(instance: *mut c_void);
pub fn cv_detail_CylindricalProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CylindricalProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_CylindricalProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CylindricalProjector_delete(instance: *mut c_void);
pub fn cv_detail_CylindricalWarper_CylindricalWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CylindricalWarper_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_CylindricalWarper_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_CylindricalWarper_to_Detail_CylindricalWarperGpu(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CylindricalWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CylindricalWarper_delete(instance: *mut c_void);
pub fn cv_detail_CylindricalWarperGpu_CylindricalWarperGpu_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_CylindricalWarperGpu_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_CylindricalWarperGpu_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_CylindricalWarperGpu_buildMaps_Size_const__InputArrayR_const__InputArrayR_GpuMatR_GpuMatR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *mut c_void, ymap: *mut c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_CylindricalWarperGpu_warp_const_GpuMatR_const__InputArrayR_const__InputArrayR_int_int_GpuMatR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *mut c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_CylindricalWarperGpu_to_Detail_CylindricalWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CylindricalWarperGpu_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_CylindricalWarperGpu_delete(instance: *mut c_void);
pub fn cv_detail_DisjointSets_DisjointSets_int(elem_count: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_DisjointSets_DisjointSets(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_DisjointSets_createOneElemSets_int(instance: *mut c_void, elem_count: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_DisjointSets_findSetByElem_int(instance: *mut c_void, elem: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_detail_DisjointSets_mergeSets_int_int(instance: *mut c_void, set1: i32, set2: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_detail_DisjointSets_propParent_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_DisjointSets_propParent_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_DisjointSets_propSize_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_DisjointSets_propSize_const_vectorLintG(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_DisjointSets_delete(instance: *mut c_void);
pub fn cv_detail_DpSeamFinder_DpSeamFinder_CostFunction(cost_func: crate::stitching::Detail_DpSeamFinder_CostFunction, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_DpSeamFinder_DpSeamFinder(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_DpSeamFinder_DpSeamFinder_String(cost_func: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_DpSeamFinder_costFunction_const(instance: *const c_void, ocvrs_return: *mut Result<crate::stitching::Detail_DpSeamFinder_CostFunction>);
pub fn cv_detail_DpSeamFinder_setCostFunction_CostFunction(instance: *mut c_void, val: crate::stitching::Detail_DpSeamFinder_CostFunction, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_DpSeamFinder_setCostFunction_String(instance: *mut c_void, val: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_DpSeamFinder_find_const_vectorLUMatGR_const_vectorLPointGR_vectorLUMatGR(instance: *mut c_void, src: *const c_void, corners: *const c_void, masks: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_DpSeamFinder_to_Detail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_DpSeamFinder_delete(instance: *mut c_void);
pub fn cv_detail_Estimator_operator___const_vectorLImageFeaturesGR_const_vectorLMatchesInfoGR_vectorLCameraParamsGR(instance: *mut c_void, features: *const c_void, pairwise_matches: *const c_void, cameras: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_detail_Estimator_to_Detail_AffineBasedEstimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Estimator_to_Detail_BundleAdjusterAffine(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Estimator_to_Detail_BundleAdjusterAffinePartial(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Estimator_to_Detail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Estimator_to_Detail_BundleAdjusterRay(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Estimator_to_Detail_BundleAdjusterReproj(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Estimator_to_Detail_HomographyBasedEstimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Estimator_to_Detail_NoBundleAdjuster(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_Estimator_delete(instance: *mut c_void);
pub fn cv_detail_ExposureCompensator_createDefault_int(typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_ExposureCompensator_feed_const_vectorLPointGR_const_vectorLUMatGR_const_vectorLUMatGR(instance: *mut c_void, corners: *const c_void, images: *const c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ExposureCompensator_feed_const_vectorLPointGR_const_vectorLUMatGR_const_vectorLpairLcv_UMat__unsigned_charGGR(instance: *mut c_void, corners: *const c_void, images: *const c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ExposureCompensator_apply_int_Point_const__InputOutputArrayR_const__InputArrayR(instance: *mut c_void, index: i32, corner: *const core::Point, image: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ExposureCompensator_getMatGains_vectorLMatGR(instance: *mut c_void, unnamed: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ExposureCompensator_setMatGains_vectorLMatGR(instance: *mut c_void, unnamed: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ExposureCompensator_setUpdateGain_bool(instance: *mut c_void, b: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ExposureCompensator_getUpdateGain(instance: *mut c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_detail_ExposureCompensator_to_Detail_BlocksChannelsCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_ExposureCompensator_to_Detail_BlocksCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_ExposureCompensator_to_Detail_BlocksGainCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_ExposureCompensator_to_Detail_ChannelsCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_ExposureCompensator_to_Detail_GainCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_ExposureCompensator_to_Detail_NoExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_ExposureCompensator_delete(instance: *mut c_void);
pub fn cv_detail_FeatherBlender_FeatherBlender_float(sharpness: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_FeatherBlender_FeatherBlender(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_FeatherBlender_sharpness_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_detail_FeatherBlender_setSharpness_float(instance: *mut c_void, val: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FeatherBlender_prepare_Rect(instance: *mut c_void, dst_roi: *const core::Rect, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FeatherBlender_feed_const__InputArrayR_const__InputArrayR_Point(instance: *mut c_void, img: *const c_void, mask: *const c_void, tl: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FeatherBlender_blend_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, dst: *const c_void, dst_mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FeatherBlender_createWeightMaps_const_vectorLUMatGR_const_vectorLPointGR_vectorLUMatGR(instance: *mut c_void, masks: *const c_void, corners: *const c_void, weight_maps: *mut c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_FeatherBlender_to_Detail_Blender(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_FeatherBlender_delete(instance: *mut c_void);
pub fn cv_detail_FeaturesMatcher_operator___const_ImageFeaturesR_const_ImageFeaturesR_MatchesInfoR(instance: *mut c_void, features1: *const c_void, features2: *const c_void, matches_info: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FeaturesMatcher_operator___const_vectorLImageFeaturesGR_vectorLMatchesInfoGR_const_UMatR(instance: *mut c_void, features: *const c_void, pairwise_matches: *mut c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FeaturesMatcher_operator___const_vectorLImageFeaturesGR_vectorLMatchesInfoGR(instance: *mut c_void, features: *const c_void, pairwise_matches: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FeaturesMatcher_isThreadSafe_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_detail_FeaturesMatcher_collectGarbage(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FeaturesMatcher_to_Detail_AffineBestOf2NearestMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_FeaturesMatcher_to_Detail_BestOf2NearestMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_FeaturesMatcher_to_Detail_BestOf2NearestRangeMatcher(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_FeaturesMatcher_delete(instance: *mut c_void);
pub fn cv_detail_FisheyeProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FisheyeProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_FisheyeProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_FisheyeProjector_delete(instance: *mut c_void);
pub fn cv_detail_FisheyeWarper_FisheyeWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_FisheyeWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_FisheyeWarper_delete(instance: *mut c_void);
pub fn cv_detail_GainCompensator_GainCompensator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_GainCompensator_GainCompensator_int(nr_feeds: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_GainCompensator_feed_const_vectorLPointGR_const_vectorLUMatGR_const_vectorLpairLcv_UMat__unsigned_charGGR(instance: *mut c_void, corners: *const c_void, images: *const c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GainCompensator_singleFeed_const_vectorLPointGR_const_vectorLUMatGR_const_vectorLpairLcv_UMat__unsigned_charGGR(instance: *mut c_void, corners: *const c_void, images: *const c_void, masks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GainCompensator_apply_int_Point_const__InputOutputArrayR_const__InputArrayR(instance: *mut c_void, index: i32, corner: *const core::Point, image: *const c_void, mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GainCompensator_getMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GainCompensator_setMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GainCompensator_setNrFeeds_int(instance: *mut c_void, nr_feeds: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GainCompensator_getNrFeeds(instance: *mut c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_detail_GainCompensator_setSimilarityThreshold_double(instance: *mut c_void, similarity_threshold: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GainCompensator_getSimilarityThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_detail_GainCompensator_prepareSimilarityMask_const_vectorLPointGR_const_vectorLUMatGR(instance: *mut c_void, corners: *const c_void, images: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GainCompensator_gains_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_GainCompensator_to_Detail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_GainCompensator_delete(instance: *mut c_void);
pub fn cv_detail_Graph_Graph_int(num_vertices: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_Graph_Graph(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_Graph_create_int(instance: *mut c_void, num_vertices: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_Graph_numVertices_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_detail_Graph_addEdge_int_int_float(instance: *mut c_void, from: i32, to: i32, weight: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_Graph_delete(instance: *mut c_void);
pub fn cv_detail_GraphCutSeamFinder_GraphCutSeamFinder_int_float_float(cost_type: i32, terminal_cost: f32, bad_region_penalty: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_GraphCutSeamFinder_GraphCutSeamFinder(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_GraphCutSeamFinder_GraphCutSeamFinder_String_float_float(cost_type: *const c_char, terminal_cost: f32, bad_region_penalty: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_GraphCutSeamFinder_GraphCutSeamFinder_String(cost_type: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_GraphCutSeamFinder_find_const_vectorLUMatGR_const_vectorLPointGR_vectorLUMatGR(instance: *mut c_void, src: *const c_void, corners: *const c_void, masks: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_GraphCutSeamFinder_to_Detail_GraphCutSeamFinderBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_GraphCutSeamFinder_to_Detail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_GraphCutSeamFinder_delete(instance: *mut c_void);
pub fn cv_detail_GraphCutSeamFinderBase_delete(instance: *mut c_void);
pub fn cv_detail_GraphEdge_GraphEdge_int_int_float(from: i32, to: i32, weight: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_GraphEdge_operatorL_const_const_GraphEdgeR(instance: *const c_void, other: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_detail_GraphEdge_operatorG_const_const_GraphEdgeR(instance: *const c_void, other: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_detail_GraphEdge_propFrom_const(instance: *const c_void) -> i32;
pub fn cv_detail_GraphEdge_propFrom_const_int(instance: *mut c_void, val: i32);
pub fn cv_detail_GraphEdge_propTo_const(instance: *const c_void) -> i32;
pub fn cv_detail_GraphEdge_propTo_const_int(instance: *mut c_void, val: i32);
pub fn cv_detail_GraphEdge_propWeight_const(instance: *const c_void) -> f32;
pub fn cv_detail_GraphEdge_propWeight_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_GraphEdge_delete(instance: *mut c_void);
pub fn cv_detail_HomographyBasedEstimator_HomographyBasedEstimator_bool(is_focals_estimated: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_HomographyBasedEstimator_HomographyBasedEstimator(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_HomographyBasedEstimator_to_Detail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_HomographyBasedEstimator_delete(instance: *mut c_void);
pub fn cv_detail_ImageFeatures_getKeypoints(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_ImageFeatures_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_ImageFeatures_defaultNew_const() -> *mut c_void;
pub fn cv_detail_ImageFeatures_propImg_idx_const(instance: *const c_void) -> i32;
pub fn cv_detail_ImageFeatures_propImg_idx_const_int(instance: *mut c_void, val: i32);
pub fn cv_detail_ImageFeatures_propImg_size_const(instance: *const c_void, ocvrs_return: *mut core::Size);
pub fn cv_detail_ImageFeatures_propImg_size_const_Size(instance: *mut c_void, val: *const core::Size);
pub fn cv_detail_ImageFeatures_propKeypoints_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_ImageFeatures_propKeypoints_const_vectorLKeyPointG(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_ImageFeatures_propDescriptors_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_ImageFeatures_propDescriptors_const_UMat(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_ImageFeatures_delete(instance: *mut c_void);
pub fn cv_detail_MatchesInfo_MatchesInfo(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_MatchesInfo_MatchesInfo_const_MatchesInfoR(other: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_MatchesInfo_operatorST_const_MatchesInfoR(instance: *mut c_void, other: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_MatchesInfo_getMatches(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_MatchesInfo_getInliers(instance: *mut c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_MatchesInfo_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_MatchesInfo_propSrc_img_idx_const(instance: *const c_void) -> i32;
pub fn cv_detail_MatchesInfo_propSrc_img_idx_const_int(instance: *mut c_void, val: i32);
pub fn cv_detail_MatchesInfo_propDst_img_idx_const(instance: *const c_void) -> i32;
pub fn cv_detail_MatchesInfo_propDst_img_idx_const_int(instance: *mut c_void, val: i32);
pub fn cv_detail_MatchesInfo_propMatches_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_MatchesInfo_propMatches_const_vectorLDMatchG(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_MatchesInfo_propInliers_mask_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_MatchesInfo_propInliers_mask_const_vectorLunsigned_charG(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_MatchesInfo_propNum_inliers_const(instance: *const c_void) -> i32;
pub fn cv_detail_MatchesInfo_propNum_inliers_const_int(instance: *mut c_void, val: i32);
pub fn cv_detail_MatchesInfo_propH_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_MatchesInfo_propH_const_Mat(instance: *mut c_void, val: *const c_void);
pub fn cv_detail_MatchesInfo_propConfidence_const(instance: *const c_void) -> f64;
pub fn cv_detail_MatchesInfo_propConfidence_const_double(instance: *mut c_void, val: f64);
pub fn cv_detail_MatchesInfo_delete(instance: *mut c_void);
pub fn cv_detail_MercatorProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_MercatorProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_MercatorProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_MercatorProjector_delete(instance: *mut c_void);
pub fn cv_detail_MercatorWarper_MercatorWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_MercatorWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_MercatorWarper_delete(instance: *mut c_void);
pub fn cv_detail_MultiBandBlender_MultiBandBlender_int_int_int(try_gpu: i32, num_bands: i32, weight_type: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_MultiBandBlender_MultiBandBlender(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_MultiBandBlender_numBands_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_detail_MultiBandBlender_setNumBands_int(instance: *mut c_void, val: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_MultiBandBlender_prepare_Rect(instance: *mut c_void, dst_roi: *const core::Rect, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_MultiBandBlender_feed_const__InputArrayR_const__InputArrayR_Point(instance: *mut c_void, img: *const c_void, mask: *const c_void, tl: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_MultiBandBlender_blend_const__InputOutputArrayR_const__InputOutputArrayR(instance: *mut c_void, dst: *const c_void, dst_mask: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_MultiBandBlender_to_Detail_Blender(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_MultiBandBlender_delete(instance: *mut c_void);
pub fn cv_detail_NoBundleAdjuster_NoBundleAdjuster(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_NoBundleAdjuster_to_Detail_BundleAdjusterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_NoBundleAdjuster_to_Detail_Estimator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_NoBundleAdjuster_delete(instance: *mut c_void);
pub fn cv_detail_NoExposureCompensator_feed_const_vectorLPointGR_const_vectorLUMatGR_const_vectorLpairLcv_UMat__unsigned_charGGR(instance: *mut c_void, unnamed: *const c_void, unnamed_1: *const c_void, unnamed_2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_NoExposureCompensator_apply_int_Point_const__InputOutputArrayR_const__InputArrayR(instance: *mut c_void, unnamed: i32, unnamed_1: *const core::Point, unnamed_2: *const c_void, unnamed_3: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_NoExposureCompensator_getMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_NoExposureCompensator_setMatGains_vectorLMatGR(instance: *mut c_void, umv: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_NoExposureCompensator_to_Detail_ExposureCompensator(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_NoExposureCompensator_delete(instance: *mut c_void);
pub fn cv_detail_NoSeamFinder_find_const_vectorLUMatGR_const_vectorLPointGR_vectorLUMatGR(instance: *mut c_void, unnamed: *const c_void, unnamed_1: *const c_void, unnamed_2: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_NoSeamFinder_to_Detail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_NoSeamFinder_delete(instance: *mut c_void);
pub fn cv_detail_PairwiseSeamFinder_find_const_vectorLUMatGR_const_vectorLPointGR_vectorLUMatGR(instance: *mut c_void, src: *const c_void, corners: *const c_void, masks: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PairwiseSeamFinder_to_Detail_VoronoiSeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PairwiseSeamFinder_to_Detail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PairwiseSeamFinder_delete(instance: *mut c_void);
pub fn cv_detail_PaniniPortraitProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PaniniPortraitProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PaniniPortraitProjector_propA_const(instance: *const c_void) -> f32;
pub fn cv_detail_PaniniPortraitProjector_propA_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_PaniniPortraitProjector_propB_const(instance: *const c_void) -> f32;
pub fn cv_detail_PaniniPortraitProjector_propB_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_PaniniPortraitProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PaniniPortraitProjector_delete(instance: *mut c_void);
pub fn cv_detail_PaniniPortraitWarper_PaniniPortraitWarper_float_float_float(scale: f32, a: f32, b: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PaniniPortraitWarper_PaniniPortraitWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PaniniPortraitWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PaniniPortraitWarper_delete(instance: *mut c_void);
pub fn cv_detail_PaniniProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PaniniProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PaniniProjector_propA_const(instance: *const c_void) -> f32;
pub fn cv_detail_PaniniProjector_propA_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_PaniniProjector_propB_const(instance: *const c_void) -> f32;
pub fn cv_detail_PaniniProjector_propB_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_PaniniProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PaniniProjector_delete(instance: *mut c_void);
pub fn cv_detail_PaniniWarper_PaniniWarper_float_float_float(scale: f32, a: f32, b: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PaniniWarper_PaniniWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PaniniWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PaniniWarper_delete(instance: *mut c_void);
pub fn cv_detail_PlanePortraitProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PlanePortraitProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PlanePortraitProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PlanePortraitProjector_delete(instance: *mut c_void);
pub fn cv_detail_PlanePortraitWarper_PlanePortraitWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PlanePortraitWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PlanePortraitWarper_delete(instance: *mut c_void);
pub fn cv_detail_PlaneProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PlaneProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_PlaneProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PlaneProjector_delete(instance: *mut c_void);
pub fn cv_detail_PlaneWarper_PlaneWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PlaneWarper_PlaneWarper(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PlaneWarper_warpPoint_const_Point2fR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_detail_PlaneWarper_warpPoint_const_Point2fR_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, r: *const c_void, t: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_detail_PlaneWarper_warpPointBackward_const_Point2fR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_detail_PlaneWarper_warpPointBackward_const_Point2fR_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, r: *const c_void, t: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_detail_PlaneWarper_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, t: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_PlaneWarper_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_PlaneWarper_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_PlaneWarper_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, t: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_PlaneWarper_warpRoi_Size_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_PlaneWarper_warpRoi_Size_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, t: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_PlaneWarper_to_Detail_AffineWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PlaneWarper_to_Detail_PlaneWarperGpu(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PlaneWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PlaneWarper_delete(instance: *mut c_void);
pub fn cv_detail_PlaneWarperGpu_PlaneWarperGpu_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PlaneWarperGpu_PlaneWarperGpu(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_PlaneWarperGpu_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_PlaneWarperGpu_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, t: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_PlaneWarperGpu_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_PlaneWarperGpu_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, t: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_PlaneWarperGpu_buildMaps_Size_const__InputArrayR_const__InputArrayR_GpuMatR_GpuMatR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *mut c_void, ymap: *mut c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_PlaneWarperGpu_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__InputArrayR_GpuMatR_GpuMatR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, t: *const c_void, xmap: *mut c_void, ymap: *mut c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_PlaneWarperGpu_warp_const_GpuMatR_const__InputArrayR_const__InputArrayR_int_int_GpuMatR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *mut c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_PlaneWarperGpu_warp_const_GpuMatR_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_GpuMatR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, t: *const c_void, interp_mode: i32, border_mode: i32, dst: *mut c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_PlaneWarperGpu_to_Detail_PlaneWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PlaneWarperGpu_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_PlaneWarperGpu_delete(instance: *mut c_void);
pub fn cv_detail_ProjectorBase_setCameraParams_const__InputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, k: *const c_void, r: *const c_void, t: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ProjectorBase_setCameraParams(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_ProjectorBase_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_ProjectorBase_defaultNew_const() -> *mut c_void;
pub fn cv_detail_ProjectorBase_propScale_const(instance: *const c_void) -> f32;
pub fn cv_detail_ProjectorBase_propScale_const_float(instance: *mut c_void, val: f32);
pub fn cv_detail_ProjectorBase_propK_const(instance: *const c_void) -> *const [f32; 9];
pub fn cv_detail_ProjectorBase_propK(instance: *mut c_void) -> *mut [f32; 9];
pub fn cv_detail_ProjectorBase_propRinv_const(instance: *const c_void) -> *const [f32; 9];
pub fn cv_detail_ProjectorBase_propRinv(instance: *mut c_void) -> *mut [f32; 9];
pub fn cv_detail_ProjectorBase_propR_kinv_const(instance: *const c_void) -> *const [f32; 9];
pub fn cv_detail_ProjectorBase_propR_kinv(instance: *mut c_void) -> *mut [f32; 9];
pub fn cv_detail_ProjectorBase_propK_rinv_const(instance: *const c_void) -> *const [f32; 9];
pub fn cv_detail_ProjectorBase_propK_rinv(instance: *mut c_void) -> *mut [f32; 9];
pub fn cv_detail_ProjectorBase_propT_const(instance: *const c_void) -> *const [f32; 3];
pub fn cv_detail_ProjectorBase_propT(instance: *mut c_void) -> *mut [f32; 3];
pub fn cv_detail_ProjectorBase_delete(instance: *mut c_void);
pub fn cv_detail_RotationWarper_warpPoint_const_Point2fR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_detail_RotationWarper_warpPointBackward_const_Point2fR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, pt: *const core::Point2f, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Point2f>);
pub fn cv_detail_RotationWarper_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_RotationWarper_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_RotationWarper_warpBackward_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_Size_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst_size: *const core::Size, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_RotationWarper_warpRoi_Size_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_RotationWarper_getScale_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_detail_RotationWarper_setScale_float(instance: *mut c_void, unnamed: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_RotationWarper_delete(instance: *mut c_void);
pub fn cv_detail_SeamFinder_find_const_vectorLUMatGR_const_vectorLPointGR_vectorLUMatGR(instance: *mut c_void, src: *const c_void, corners: *const c_void, masks: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_SeamFinder_createDefault_int(typ: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_SeamFinder_to_Detail_DpSeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SeamFinder_to_Detail_GraphCutSeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SeamFinder_to_Detail_NoSeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SeamFinder_to_Detail_PairwiseSeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SeamFinder_to_Detail_VoronoiSeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SeamFinder_delete(instance: *mut c_void);
pub fn cv_detail_SphericalPortraitProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_SphericalPortraitProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_SphericalPortraitProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SphericalPortraitProjector_delete(instance: *mut c_void);
pub fn cv_detail_SphericalPortraitWarper_SphericalPortraitWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_SphericalPortraitWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SphericalPortraitWarper_delete(instance: *mut c_void);
pub fn cv_detail_SphericalProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_SphericalProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_SphericalProjector_implicitClone_const(instance: *const c_void) -> *mut c_void;
pub fn cv_detail_SphericalProjector_defaultNew_const() -> *mut c_void;
pub fn cv_detail_SphericalProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SphericalProjector_delete(instance: *mut c_void);
pub fn cv_detail_SphericalWarper_SphericalWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_SphericalWarper_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_SphericalWarper_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_SphericalWarper_to_Detail_SphericalWarperGpu(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SphericalWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SphericalWarper_delete(instance: *mut c_void);
pub fn cv_detail_SphericalWarperGpu_SphericalWarperGpu_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_SphericalWarperGpu_buildMaps_Size_const__InputArrayR_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *const c_void, ymap: *const c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_SphericalWarperGpu_warp_const__InputArrayR_const__InputArrayR_const__InputArrayR_int_int_const__OutputArrayR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_SphericalWarperGpu_buildMaps_Size_const__InputArrayR_const__InputArrayR_GpuMatR_GpuMatR(instance: *mut c_void, src_size: *const core::Size, k: *const c_void, r: *const c_void, xmap: *mut c_void, ymap: *mut c_void, ocvrs_return: *mut Result<core::Rect>);
pub fn cv_detail_SphericalWarperGpu_warp_const_GpuMatR_const__InputArrayR_const__InputArrayR_int_int_GpuMatR(instance: *mut c_void, src: *const c_void, k: *const c_void, r: *const c_void, interp_mode: i32, border_mode: i32, dst: *mut c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_detail_SphericalWarperGpu_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SphericalWarperGpu_to_Detail_SphericalWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_SphericalWarperGpu_delete(instance: *mut c_void);
pub fn cv_detail_StereographicProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_StereographicProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_StereographicProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_StereographicProjector_delete(instance: *mut c_void);
pub fn cv_detail_StereographicWarper_StereographicWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_StereographicWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_StereographicWarper_delete(instance: *mut c_void);
pub fn cv_detail_TransverseMercatorProjector_mapForward_float_float_floatR_floatR(instance: *mut c_void, x: f32, y: f32, u: *mut f32, v: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_TransverseMercatorProjector_mapBackward_float_float_floatR_floatR(instance: *mut c_void, u: f32, v: f32, x: *mut f32, y: *mut f32, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_TransverseMercatorProjector_to_Detail_ProjectorBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_TransverseMercatorProjector_delete(instance: *mut c_void);
pub fn cv_detail_TransverseMercatorWarper_TransverseMercatorWarper_float(scale: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_detail_TransverseMercatorWarper_to_Detail_RotationWarper(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_TransverseMercatorWarper_delete(instance: *mut c_void);
pub fn cv_detail_VoronoiSeamFinder_find_const_vectorLUMatGR_const_vectorLPointGR_vectorLUMatGR(instance: *mut c_void, src: *const c_void, corners: *const c_void, masks: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_VoronoiSeamFinder_find_const_vectorLSizeGR_const_vectorLPointGR_vectorLUMatGR(instance: *mut c_void, size: *const c_void, corners: *const c_void, masks: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detail_VoronoiSeamFinder_to_Detail_PairwiseSeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_VoronoiSeamFinder_to_Detail_SeamFinder(instance: *mut c_void) -> *mut c_void;
pub fn cv_detail_VoronoiSeamFinder_delete(instance: *mut c_void);
