pub fn cv_colorChange_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src: *const c_void, mask: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_colorChange_const__InputArrayR_const__InputArrayR_const__OutputArrayR_float_float_float(src: *const c_void, mask: *const c_void, dst: *const c_void, red_mul: f32, green_mul: f32, blue_mul: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_createAlignMTB(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createAlignMTB_int_int_bool(max_bits: i32, exclude_range: i32, cut: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createCalibrateDebevec(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createCalibrateDebevec_int_float_bool(samples: i32, lambda: f32, random: bool, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createCalibrateRobertson(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createCalibrateRobertson_int_float(max_iter: i32, threshold: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createMergeDebevec(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createMergeMertens(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createMergeMertens_float_float_float(contrast_weight: f32, saturation_weight: f32, exposure_weight: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createMergeRobertson(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createTonemap(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createTonemapDrago(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createTonemapDrago_float_float_float(gamma: f32, saturation: f32, bias: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createTonemapMantiuk(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createTonemapMantiuk_float_float_float(gamma: f32, scale: f32, saturation: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createTonemapReinhard(ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createTonemapReinhard_float_float_float_float(gamma: f32, intensity: f32, light_adapt: f32, color_adapt: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_createTonemap_float(gamma: f32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_cuda_fastNlMeansDenoisingColored_const__InputArrayR_const__OutputArrayR_float_float(src: *const c_void, dst: *const c_void, h_luminance: f32, photo_render: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_fastNlMeansDenoisingColored_const__InputArrayR_const__OutputArrayR_float_float_int_int_StreamR(src: *const c_void, dst: *const c_void, h_luminance: f32, photo_render: f32, search_window: i32, block_size: i32, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_fastNlMeansDenoising_const__InputArrayR_const__OutputArrayR_float(src: *const c_void, dst: *const c_void, h: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_fastNlMeansDenoising_const__InputArrayR_const__OutputArrayR_float_int_int_StreamR(src: *const c_void, dst: *const c_void, h: f32, search_window: i32, block_size: i32, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_nonLocalMeans_const__InputArrayR_const__OutputArrayR_float(src: *const c_void, dst: *const c_void, h: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_cuda_nonLocalMeans_const__InputArrayR_const__OutputArrayR_float_int_int_int_StreamR(src: *const c_void, dst: *const c_void, h: f32, search_window: i32, block_size: i32, border_mode: i32, stream: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_decolor_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, grayscale: *const c_void, color_boost: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_denoise_TVL1_const_vectorLMatGR_MatR(observations: *const c_void, result: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_denoise_TVL1_const_vectorLMatGR_MatR_double_int(observations: *const c_void, result: *mut c_void, lambda: f64, niters: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_detailEnhance_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_detailEnhance_const__InputArrayR_const__OutputArrayR_float_float(src: *const c_void, dst: *const c_void, sigma_s: f32, sigma_r: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_edgePreservingFilter_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_edgePreservingFilter_const__InputArrayR_const__OutputArrayR_int_float_float(src: *const c_void, dst: *const c_void, flags: i32, sigma_s: f32, sigma_r: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoisingColoredMulti_const__InputArrayR_const__OutputArrayR_int_int(src_imgs: *const c_void, dst: *const c_void, img_to_denoise_index: i32, temporal_window_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoisingColoredMulti_const__InputArrayR_const__OutputArrayR_int_int_float_float_int_int(src_imgs: *const c_void, dst: *const c_void, img_to_denoise_index: i32, temporal_window_size: i32, h: f32, h_color: f32, template_window_size: i32, search_window_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoisingColored_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoisingColored_const__InputArrayR_const__OutputArrayR_float_float_int_int(src: *const c_void, dst: *const c_void, h: f32, h_color: f32, template_window_size: i32, search_window_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoisingMulti_const__InputArrayR_const__OutputArrayR_int_int(src_imgs: *const c_void, dst: *const c_void, img_to_denoise_index: i32, temporal_window_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoisingMulti_const__InputArrayR_const__OutputArrayR_int_int_const_vectorLfloatGR(src_imgs: *const c_void, dst: *const c_void, img_to_denoise_index: i32, temporal_window_size: i32, h: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoisingMulti_const__InputArrayR_const__OutputArrayR_int_int_const_vectorLfloatGR_int_int_int(src_imgs: *const c_void, dst: *const c_void, img_to_denoise_index: i32, temporal_window_size: i32, h: *const c_void, template_window_size: i32, search_window_size: i32, norm_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoisingMulti_const__InputArrayR_const__OutputArrayR_int_int_float_int_int(src_imgs: *const c_void, dst: *const c_void, img_to_denoise_index: i32, temporal_window_size: i32, h: f32, template_window_size: i32, search_window_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoising_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoising_const__InputArrayR_const__OutputArrayR_const_vectorLfloatGR(src: *const c_void, dst: *const c_void, h: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoising_const__InputArrayR_const__OutputArrayR_const_vectorLfloatGR_int_int_int(src: *const c_void, dst: *const c_void, h: *const c_void, template_window_size: i32, search_window_size: i32, norm_type: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_fastNlMeansDenoising_const__InputArrayR_const__OutputArrayR_float_int_int(src: *const c_void, dst: *const c_void, h: f32, template_window_size: i32, search_window_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_illuminationChange_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src: *const c_void, mask: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_illuminationChange_const__InputArrayR_const__InputArrayR_const__OutputArrayR_float_float(src: *const c_void, mask: *const c_void, dst: *const c_void, alpha: f32, beta: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_inpaint_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double_int(src: *const c_void, inpaint_mask: *const c_void, dst: *const c_void, inpaint_radius: f64, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_pencilSketch_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(src: *const c_void, dst1: *const c_void, dst2: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_pencilSketch_const__InputArrayR_const__OutputArrayR_const__OutputArrayR_float_float_float(src: *const c_void, dst1: *const c_void, dst2: *const c_void, sigma_s: f32, sigma_r: f32, shade_factor: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_seamlessClone_const__InputArrayR_const__InputArrayR_const__InputArrayR_Point_const__OutputArrayR_int(src: *const c_void, dst: *const c_void, mask: *const c_void, p: *const core::Point, blend: *const c_void, flags: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_stylization_const__InputArrayR_const__OutputArrayR(src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_stylization_const__InputArrayR_const__OutputArrayR_float_float(src: *const c_void, dst: *const c_void, sigma_s: f32, sigma_r: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_textureFlattening_const__InputArrayR_const__InputArrayR_const__OutputArrayR(src: *const c_void, mask: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_textureFlattening_const__InputArrayR_const__InputArrayR_const__OutputArrayR_float_float_int(src: *const c_void, mask: *const c_void, dst: *const c_void, low_threshold: f32, high_threshold: f32, kernel_size: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignExposures_process_const__InputArrayR_vectorLMatGR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *mut c_void, times: *const c_void, response: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignExposures_to_AlignMTB(instance: *mut c_void) -> *mut c_void;
pub fn cv_AlignExposures_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_AlignExposures_delete(instance: *mut c_void);
pub fn cv_AlignMTB_process_const__InputArrayR_vectorLMatGR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *mut c_void, times: *const c_void, response: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignMTB_process_const__InputArrayR_vectorLMatGR(instance: *mut c_void, src: *const c_void, dst: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignMTB_calculateShift_const__InputArrayR_const__InputArrayR(instance: *mut c_void, img0: *const c_void, img1: *const c_void, ocvrs_return: *mut Result<core::Point>);
pub fn cv_AlignMTB_shiftMat_const__InputArrayR_const__OutputArrayR_const_Point(instance: *mut c_void, src: *const c_void, dst: *const c_void, shift: *const core::Point, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignMTB_computeBitmaps_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(instance: *mut c_void, img: *const c_void, tb: *const c_void, eb: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignMTB_getMaxBits_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_AlignMTB_setMaxBits_int(instance: *mut c_void, max_bits: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignMTB_getExcludeRange_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_AlignMTB_setExcludeRange_int(instance: *mut c_void, exclude_range: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignMTB_getCut_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_AlignMTB_setCut_bool(instance: *mut c_void, value: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_AlignMTB_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_AlignMTB_to_AlignExposures(instance: *mut c_void) -> *mut c_void;
pub fn cv_AlignMTB_delete(instance: *mut c_void);
pub fn cv_CalibrateCRF_process_const__InputArrayR_const__OutputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, times: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_CalibrateCRF_to_CalibrateDebevec(instance: *mut c_void) -> *mut c_void;
pub fn cv_CalibrateCRF_to_CalibrateRobertson(instance: *mut c_void) -> *mut c_void;
pub fn cv_CalibrateCRF_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_CalibrateCRF_delete(instance: *mut c_void);
pub fn cv_CalibrateDebevec_getLambda_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_CalibrateDebevec_setLambda_float(instance: *mut c_void, lambda: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_CalibrateDebevec_getSamples_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_CalibrateDebevec_setSamples_int(instance: *mut c_void, samples: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_CalibrateDebevec_getRandom_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_CalibrateDebevec_setRandom_bool(instance: *mut c_void, random: bool, ocvrs_return: *mut ResultVoid);
pub fn cv_CalibrateDebevec_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_CalibrateDebevec_to_CalibrateCRF(instance: *mut c_void) -> *mut c_void;
pub fn cv_CalibrateDebevec_delete(instance: *mut c_void);
pub fn cv_CalibrateRobertson_getMaxIter_const(instance: *const c_void, ocvrs_return: *mut Result<i32>);
pub fn cv_CalibrateRobertson_setMaxIter_int(instance: *mut c_void, max_iter: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_CalibrateRobertson_getThreshold_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_CalibrateRobertson_setThreshold_float(instance: *mut c_void, threshold: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_CalibrateRobertson_getRadiance_const(instance: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_CalibrateRobertson_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_CalibrateRobertson_to_CalibrateCRF(instance: *mut c_void) -> *mut c_void;
pub fn cv_CalibrateRobertson_delete(instance: *mut c_void);
pub fn cv_MergeDebevec_process_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, times: *const c_void, response: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeDebevec_process_const__InputArrayR_const__OutputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, times: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeDebevec_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeDebevec_to_MergeExposures(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeDebevec_delete(instance: *mut c_void);
pub fn cv_MergeExposures_process_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, times: *const c_void, response: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeExposures_to_MergeDebevec(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeExposures_to_MergeMertens(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeExposures_to_MergeRobertson(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeExposures_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeExposures_delete(instance: *mut c_void);
pub fn cv_MergeMertens_process_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, times: *const c_void, response: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeMertens_process_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeMertens_getContrastWeight_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_MergeMertens_setContrastWeight_float(instance: *mut c_void, contrast_weiht: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeMertens_getSaturationWeight_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_MergeMertens_setSaturationWeight_float(instance: *mut c_void, saturation_weight: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeMertens_getExposureWeight_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_MergeMertens_setExposureWeight_float(instance: *mut c_void, exposure_weight: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeMertens_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeMertens_to_MergeExposures(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeMertens_delete(instance: *mut c_void);
pub fn cv_MergeRobertson_process_const__InputArrayR_const__OutputArrayR_const__InputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, times: *const c_void, response: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeRobertson_process_const__InputArrayR_const__OutputArrayR_const__InputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, times: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_MergeRobertson_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeRobertson_to_MergeExposures(instance: *mut c_void) -> *mut c_void;
pub fn cv_MergeRobertson_delete(instance: *mut c_void);
pub fn cv_Tonemap_process_const__InputArrayR_const__OutputArrayR(instance: *mut c_void, src: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_Tonemap_getGamma_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_Tonemap_setGamma_float(instance: *mut c_void, gamma: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_Tonemap_to_TonemapDrago(instance: *mut c_void) -> *mut c_void;
pub fn cv_Tonemap_to_TonemapMantiuk(instance: *mut c_void) -> *mut c_void;
pub fn cv_Tonemap_to_TonemapReinhard(instance: *mut c_void) -> *mut c_void;
pub fn cv_Tonemap_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_Tonemap_delete(instance: *mut c_void);
pub fn cv_TonemapDrago_getSaturation_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_TonemapDrago_setSaturation_float(instance: *mut c_void, saturation: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_TonemapDrago_getBias_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_TonemapDrago_setBias_float(instance: *mut c_void, bias: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_TonemapDrago_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_TonemapDrago_to_Tonemap(instance: *mut c_void) -> *mut c_void;
pub fn cv_TonemapDrago_delete(instance: *mut c_void);
pub fn cv_TonemapMantiuk_getScale_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_TonemapMantiuk_setScale_float(instance: *mut c_void, scale: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_TonemapMantiuk_getSaturation_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_TonemapMantiuk_setSaturation_float(instance: *mut c_void, saturation: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_TonemapMantiuk_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_TonemapMantiuk_to_Tonemap(instance: *mut c_void) -> *mut c_void;
pub fn cv_TonemapMantiuk_delete(instance: *mut c_void);
pub fn cv_TonemapReinhard_getIntensity_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_TonemapReinhard_setIntensity_float(instance: *mut c_void, intensity: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_TonemapReinhard_getLightAdaptation_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_TonemapReinhard_setLightAdaptation_float(instance: *mut c_void, light_adapt: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_TonemapReinhard_getColorAdaptation_const(instance: *const c_void, ocvrs_return: *mut Result<f32>);
pub fn cv_TonemapReinhard_setColorAdaptation_float(instance: *mut c_void, color_adapt: f32, ocvrs_return: *mut ResultVoid);
pub fn cv_TonemapReinhard_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_TonemapReinhard_to_Tonemap(instance: *mut c_void) -> *mut c_void;
pub fn cv_TonemapReinhard_delete(instance: *mut c_void);
