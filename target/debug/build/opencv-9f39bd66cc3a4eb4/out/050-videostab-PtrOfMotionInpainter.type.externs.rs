pub fn cv_PtrLcv_videostab_MotionInpainterG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionInpainterG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionInpainterG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_MotionInpainterG_to_PtrOfInpainterBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MotionInpainterG_new_const_MotionInpainter(val: *mut c_void) -> *mut c_void;
