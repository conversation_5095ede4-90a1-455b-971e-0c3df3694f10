pub fn cv_quality_QualityBRISQUE_compute_const__InputArrayR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityBRISQUE_create_const_StringR_const_StringR(model_file_path: *const c_char, range_file_path: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_quality_QualityBRISQUE_create_const_PtrLSVMGR_const_MatR(model: *const c_void, range: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_quality_QualityBRISQUE_compute_const__InputArrayR_const_StringR_const_StringR(img: *const c_void, model_file_path: *const c_char, range_file_path: *const c_char, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityBRISQUE_computeFeatures_const__InputArrayR_const__OutputArrayR(img: *const c_void, features: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_quality_QualityBRISQUE_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityBRISQUE_to_QualityBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityBRISQUE_delete(instance: *mut c_void);
pub fn cv_quality_QualityBase_compute_const__InputArrayR(instance: *mut c_void, img: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityBase_getQualityMap_const_const__OutputArrayR(instance: *const c_void, dst: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_quality_QualityBase_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_quality_QualityBase_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_quality_QualityBase_to_QualityBRISQUE(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityBase_to_QualityGMSD(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityBase_to_QualityMSE(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityBase_to_QualityPSNR(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityBase_to_QualitySSIM(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityBase_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityBase_delete(instance: *mut c_void);
pub fn cv_quality_QualityGMSD_compute_const__InputArrayR(instance: *mut c_void, cmp: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityGMSD_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_quality_QualityGMSD_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_quality_QualityGMSD_create_const__InputArrayR(ref_: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_quality_QualityGMSD_compute_const__InputArrayR_const__InputArrayR_const__OutputArrayR(ref_: *const c_void, cmp: *const c_void, quality_map: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityGMSD_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityGMSD_to_QualityBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityGMSD_delete(instance: *mut c_void);
pub fn cv_quality_QualityMSE_compute_const__InputArrayR(instance: *mut c_void, cmp_imgs: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityMSE_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_quality_QualityMSE_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_quality_QualityMSE_create_const__InputArrayR(ref_: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_quality_QualityMSE_compute_const__InputArrayR_const__InputArrayR_const__OutputArrayR(ref_: *const c_void, cmp: *const c_void, quality_map: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityMSE_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityMSE_to_QualityBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityMSE_delete(instance: *mut c_void);
pub fn cv_quality_QualityPSNR_create_const__InputArrayR_double(ref_: *const c_void, max_pixel_value: f64, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_quality_QualityPSNR_create_const__InputArrayR(ref_: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_quality_QualityPSNR_compute_const__InputArrayR(instance: *mut c_void, cmp: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityPSNR_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_quality_QualityPSNR_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_quality_QualityPSNR_compute_const__InputArrayR_const__InputArrayR_const__OutputArrayR_double(ref_: *const c_void, cmp: *const c_void, quality_map: *const c_void, max_pixel_value: f64, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityPSNR_compute_const__InputArrayR_const__InputArrayR_const__OutputArrayR(ref_: *const c_void, cmp: *const c_void, quality_map: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualityPSNR_getMaxPixelValue_const(instance: *const c_void, ocvrs_return: *mut Result<f64>);
pub fn cv_quality_QualityPSNR_setMaxPixelValue_double(instance: *mut c_void, val: f64, ocvrs_return: *mut ResultVoid);
pub fn cv_quality_QualityPSNR_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityPSNR_to_QualityBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualityPSNR_delete(instance: *mut c_void);
pub fn cv_quality_QualitySSIM_compute_const__InputArrayR(instance: *mut c_void, cmp: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualitySSIM_empty_const(instance: *const c_void, ocvrs_return: *mut Result<bool>);
pub fn cv_quality_QualitySSIM_clear(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_quality_QualitySSIM_create_const__InputArrayR(ref_: *const c_void, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_quality_QualitySSIM_compute_const__InputArrayR_const__InputArrayR_const__OutputArrayR(ref_: *const c_void, cmp: *const c_void, quality_map: *const c_void, ocvrs_return: *mut Result<core::Scalar>);
pub fn cv_quality_QualitySSIM_to_Algorithm(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualitySSIM_to_QualityBase(instance: *mut c_void) -> *mut c_void;
pub fn cv_quality_QualitySSIM_delete(instance: *mut c_void);
