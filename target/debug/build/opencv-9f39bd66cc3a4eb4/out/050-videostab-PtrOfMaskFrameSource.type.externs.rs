pub fn cv_PtrLcv_videostab_MaskFrameSourceG_getInnerPtr_const(instance: *const c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MaskFrameSourceG_getInnerPtrMut(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MaskFrameSourceG_delete(instance: *mut c_void);
pub fn cv_PtrLcv_videostab_MaskFrameSourceG_to_PtrOfIFrameSource(instance: *mut c_void) -> *mut c_void;
pub fn cv_PtrLcv_videostab_MaskFrameSourceG_new_const_MaskFrameSource(val: *mut c_void) -> *mut c_void;
