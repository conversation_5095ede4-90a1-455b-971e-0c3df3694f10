pub fn cv_hdf_open_const_StringR(hdf5_filename: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hdf_HDF5_close(instance: *mut c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_grcreate_const_StringR(instance: *mut c_void, grlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_hlexists_const_const_StringR(instance: *const c_void, label: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_hdf_HDF5_atexists_const_const_StringR(instance: *const c_void, atlabel: *const c_char, ocvrs_return: *mut Result<bool>);
pub fn cv_hdf_HDF5_atdelete_const_StringR(instance: *mut c_void, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_atwrite_const_int_const_StringR(instance: *mut c_void, value: i32, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_atread_intX_const_StringR(instance: *mut c_void, value: *mut i32, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_atwrite_const_double_const_StringR(instance: *mut c_void, value: f64, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_atread_doubleX_const_StringR(instance: *mut c_void, value: *mut f64, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_atwrite_const_StringR_const_StringR(instance: *mut c_void, value: *const c_char, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_atread_StringX_const_StringR(instance: *mut c_void, value: *mut *mut c_void, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_atwrite_const__InputArrayR_const_StringR(instance: *mut c_void, value: *const c_void, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_atread_const__OutputArrayR_const_StringR(instance: *mut c_void, value: *const c_void, atlabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dscreate_const_const_int_const_int_const_int_const_StringR(instance: *const c_void, rows: i32, cols: i32, typ: i32, dslabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dscreate_const_const_int_const_int_const_int_const_StringR_const_int(instance: *const c_void, rows: i32, cols: i32, typ: i32, dslabel: *const c_char, compresslevel: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dscreate_const_const_int_const_int_const_int_const_StringR_const_int_const_vectorLintGR(instance: *const c_void, rows: i32, cols: i32, typ: i32, dslabel: *const c_char, compresslevel: i32, dims_chunks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dscreate_const_const_int_const_intX_const_int_const_StringR(instance: *const c_void, n_dims: i32, sizes: *const i32, typ: i32, dslabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dscreate_const_const_int_const_intX_const_int_const_StringR_const_int(instance: *const c_void, n_dims: i32, sizes: *const i32, typ: i32, dslabel: *const c_char, compresslevel: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dscreate_const_const_vectorLintGR_const_int_const_StringR_const_int_const_vectorLintGR(instance: *const c_void, sizes: *const c_void, typ: i32, dslabel: *const c_char, compresslevel: i32, dims_chunks: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dscreate_const_const_vectorLintGR_const_int_const_StringR(instance: *const c_void, sizes: *const c_void, typ: i32, dslabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dscreate_const_const_int_const_intX_const_int_const_StringR_const_int_const_intX(instance: *const c_void, n_dims: i32, sizes: *const i32, typ: i32, dslabel: *const c_char, compresslevel: i32, dims_chunks: *const i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dsgetsize_const_const_StringR_int(instance: *const c_void, dslabel: *const c_char, dims_flag: i32, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hdf_HDF5_dsgetsize_const_const_StringR(instance: *const c_void, dslabel: *const c_char, ocvrs_return: *mut Result<*mut c_void>);
pub fn cv_hdf_HDF5_dsgettype_const_const_StringR(instance: *const c_void, dslabel: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_hdf_HDF5_dswrite_const_const__InputArrayR_const_StringR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dswrite_const_const__InputArrayR_const_StringR_const_vectorLintGR_const_vectorLintGR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, dims_offset: *const c_void, dims_counts: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dswrite_const_const__InputArrayR_const_StringR_const_vectorLintGR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, dims_offset: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dsinsert_const_const__InputArrayR_const_StringR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dsinsert_const_const__InputArrayR_const_StringR_const_vectorLintGR_const_vectorLintGR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, dims_offset: *const c_void, dims_counts: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dsinsert_const_const__InputArrayR_const_StringR_const_vectorLintGR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, dims_offset: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dsread_const_const__OutputArrayR_const_StringR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dsread_const_const__OutputArrayR_const_StringR_const_vectorLintGR_const_vectorLintGR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, dims_offset: *const c_void, dims_counts: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_dsread_const_const__OutputArrayR_const_StringR_const_vectorLintGR(instance: *const c_void, array: *const c_void, dslabel: *const c_char, dims_offset: *const c_void, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_kpgetsize_const_const_StringR_int(instance: *const c_void, kplabel: *const c_char, dims_flag: i32, ocvrs_return: *mut Result<i32>);
pub fn cv_hdf_HDF5_kpgetsize_const_const_StringR(instance: *const c_void, kplabel: *const c_char, ocvrs_return: *mut Result<i32>);
pub fn cv_hdf_HDF5_kpcreate_const_const_int_const_StringR_const_int_const_int(instance: *const c_void, size: i32, kplabel: *const c_char, compresslevel: i32, chunks: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_kpcreate_const_const_int_const_StringR(instance: *const c_void, size: i32, kplabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_kpwrite_const_const_vectorLKeyPointG_const_StringR_const_int_const_int(instance: *const c_void, keypoints: *const c_void, kplabel: *const c_char, offset: i32, counts: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_kpwrite_const_const_vectorLKeyPointG_const_StringR(instance: *const c_void, keypoints: *const c_void, kplabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_kpinsert_const_const_vectorLKeyPointG_const_StringR_const_int_const_int(instance: *const c_void, keypoints: *const c_void, kplabel: *const c_char, offset: i32, counts: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_kpinsert_const_const_vectorLKeyPointG_const_StringR(instance: *const c_void, keypoints: *const c_void, kplabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_kpread_const_vectorLKeyPointGR_const_StringR_const_int_const_int(instance: *const c_void, keypoints: *mut c_void, kplabel: *const c_char, offset: i32, counts: i32, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_kpread_const_vectorLKeyPointGR_const_StringR(instance: *const c_void, keypoints: *mut c_void, kplabel: *const c_char, ocvrs_return: *mut ResultVoid);
pub fn cv_hdf_HDF5_delete(instance: *mut c_void);
