{"rustc": 11410426090777951712, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 8742971790538218063]], "local": [{"RerunIfChanged": {"output": "debug/build/anyhow-1fea51c98c81cce8/output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}