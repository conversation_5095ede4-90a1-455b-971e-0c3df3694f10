{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 15657897354478470176, "path": 1460799536382326687, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/weezl-012a053c40c0d537/dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}