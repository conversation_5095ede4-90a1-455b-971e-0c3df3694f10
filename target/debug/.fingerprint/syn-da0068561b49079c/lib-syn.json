{"rustc": 11410426090777951712, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 1272623275950143523, "deps": [[1988483478007900009, "unicode_ident", false, 14074177658304989436], [3060637413840920116, "proc_macro2", false, 3245656788925580797], [17990358020177143287, "quote", false, 1087743017789890080]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-da0068561b49079c/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}