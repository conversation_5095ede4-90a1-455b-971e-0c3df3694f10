{"rustc": 11410426090777951712, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2225463790103693989, "path": 12959529377469898766, "deps": [[555019317135488525, "regex_automata", false, 10322633204688594797], [2779309023524819297, "aho_corasick", false, 2327037166495210950], [9408802513701742484, "regex_syntax", false, 4219914338226270517], [15932120279885307830, "memchr", false, 9675468278688203572]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-573ac9399bb06e50/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}