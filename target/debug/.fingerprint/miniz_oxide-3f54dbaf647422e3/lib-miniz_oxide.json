{"rustc": 11410426090777951712, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 7803508964750555534, "deps": [[4018467389006652250, "simd_adler32", false, 1477657453914420286], [7911289239703230891, "adler2", false, 10305298435077796423]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-3f54dbaf647422e3/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}