{"rustc": 11410426090777951712, "features": "[\"alphamat\", \"aruco\", \"aruco_detector\", \"barcode\", \"bgsegm\", \"bioinspired\", \"calib3d\", \"ccalib\", \"cudaarithm\", \"cudabgsegm\", \"cudacodec\", \"cudafeatures2d\", \"cudafilters\", \"cudaimgproc\", \"cudaobjdetect\", \"cudaoptflow\", \"cudastereo\", \"cudawarping\", \"cvv\", \"default\", \"dnn\", \"dnn_superres\", \"dpm\", \"face\", \"features2d\", \"flann\", \"freetype\", \"fuzzy\", \"gapi\", \"hdf\", \"hfs\", \"highgui\", \"img_hash\", \"imgcodecs\", \"imgproc\", \"intensity_transform\", \"line_descriptor\", \"mcc\", \"ml\", \"objdetect\", \"optflow\", \"ovis\", \"phase_unwrapping\", \"photo\", \"plot\", \"quality\", \"rapid\", \"rgbd\", \"saliency\", \"sfm\", \"shape\", \"stereo\", \"stitching\", \"structured_light\", \"superres\", \"surface_matching\", \"text\", \"tracking\", \"video\", \"videoio\", \"videostab\", \"viz\", \"wechat_qrcode\", \"xfeatures2d\", \"ximgproc\", \"xobjdetect\", \"xphoto\"]", "declared_features": "[\"alphamat\", \"aruco\", \"aruco_detector\", \"barcode\", \"bgsegm\", \"bioinspired\", \"calib3d\", \"ccalib\", \"clang-runtime\", \"cudaarithm\", \"cudabgsegm\", \"cudacodec\", \"cudafeatures2d\", \"cudafilters\", \"cudaimgproc\", \"cudaobjdetect\", \"cudaoptflow\", \"cudastereo\", \"cudawarping\", \"cvv\", \"default\", \"dnn\", \"dnn_superres\", \"dpm\", \"face\", \"features2d\", \"flann\", \"freetype\", \"fuzzy\", \"gapi\", \"hdf\", \"hfs\", \"highgui\", \"img_hash\", \"imgcodecs\", \"imgproc\", \"intensity_transform\", \"line_descriptor\", \"mcc\", \"ml\", \"objdetect\", \"optflow\", \"ovis\", \"phase_unwrapping\", \"photo\", \"plot\", \"quality\", \"rapid\", \"rgb\", \"rgbd\", \"saliency\", \"sfm\", \"shape\", \"stereo\", \"stitching\", \"structured_light\", \"superres\", \"surface_matching\", \"text\", \"tracking\", \"video\", \"videoio\", \"videostab\", \"viz\", \"wechat_qrcode\", \"xfeatures2d\", \"ximgproc\", \"xobjdetect\", \"xphoto\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 5711659673913883100, "deps": [[3214373357989284387, "pkg_config", false, 2566221799246612010], [3722963349756955755, "once_cell", false, 16113708993531495570], [4899080583175475170, "semver", false, 17302332027734872217], [8410525223747752176, "shlex", false, 12960998724419546762], [10798972438384834726, "jobslot", false, 12711735311074462305], [11989259058781683633, "dunce", false, 16139247811605790494], [12410631062411814511, "cc", false, 660001729659525452], [12933202132622624734, "vcpkg", false, 17333571570367649196], [15103513257332197228, "opencv_binding_generator", false, 2189599511751993201]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/opencv-f91036f79ebe9a48/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}