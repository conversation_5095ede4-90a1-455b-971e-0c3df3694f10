[package]
name = "scene_detector"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A Rust-based movie scene transition detection tool"
license = "MIT"
repository = "https://github.com/yourusername/scene_detector"

[dependencies]
opencv = { version = "0.88", optional = true }  # 需要系统安装OpenCV开发包
image = "0.24"
rayon = "1.8"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
clap = { version = "4.0", features = ["derive"] }
anyhow = "1.0"
thiserror = "1.0"
indicatif = "0.17"
chrono = { version = "0.4", features = ["serde"] }
num_cpus = "1.16"
libc = "0.2"
# 用于演示的简化版本，不使用OpenCV
ffmpeg-next = { version = "7.0", optional = true }

[dev-dependencies]
tempfile = "3.0"

[[bin]]
name = "scene_detector"
path = "src/main.rs"

[features]
default = []
opencv_support = ["opencv"]

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
