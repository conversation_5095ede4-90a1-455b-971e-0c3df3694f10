use indicatif::{ProgressBar, ProgressStyle};
use std::path::Path;
use std::time::Instant;
use image::{RgbImage, ImageFormat};
use std::fs;

use crate::frame_diff::{FrameDifferenceCalculator, AdaptiveThresholdCalculator, Frame};
use crate::types::{DetectorConfig, SceneTransition, DetectionResult, Result, SceneDetectorError};

/// 主场景检测器（简化版本，用于演示）
pub struct SceneDetector {
    config: DetectorConfig,
    diff_calculator: FrameDifferenceCalculator,
    adaptive_threshold: AdaptiveThresholdCalculator,
}

impl SceneDetector {
    pub fn new(config: DetectorConfig) -> Self {
        let diff_calculator = FrameDifferenceCalculator::new(config.algorithm);
        let adaptive_threshold = AdaptiveThresholdCalculator::new(50); // 50帧窗口

        Self {
            config,
            diff_calculator,
            adaptive_threshold,
        }
    }

    /// 检测视频或图像序列中的场景转场
    pub fn detect_scenes<P: AsRef<Path>>(&mut self, input_path: P) -> Result<DetectionResult> {
        let _start_time = Instant::now();
        let input_path = input_path.as_ref();

        // 检查输入类型
        if input_path.is_dir() {
            // 图像序列处理
            self.detect_from_image_sequence(input_path)
        } else {
            // 检查是否为视频文件
            let extension = input_path.extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("")
                .to_lowercase();

            match extension.as_str() {
                "mp4" | "avi" | "mov" | "mkv" | "wmv" | "flv" | "webm" => {
                    // 使用OpenCV处理视频文件
                    self.detect_from_video(input_path)
                }
                _ => {
                    // 对于不支持的格式，返回错误
                    Err(SceneDetectorError::Io(std::io::Error::new(
                        std::io::ErrorKind::InvalidInput,
                        format!("不支持的文件格式: {}", extension)
                    )))
                }
            }
        }
    }

    /// 从视频文件检测场景转场
    fn detect_from_video<P: AsRef<Path>>(&mut self, video_path: P) -> Result<DetectionResult> {
        use opencv::{videoio, core::Mat, prelude::*};

        let video_path = video_path.as_ref();
        println!("正在处理视频文件: {}", video_path.display());

        // 打开视频文件
        let mut cap = videoio::VideoCapture::from_file(
            video_path.to_str().unwrap(),
            videoio::CAP_ANY
        ).map_err(|e| SceneDetectorError::Detection(format!("无法打开视频文件: {}", e)))?;

        if !cap.is_opened()
            .map_err(|e| SceneDetectorError::Detection(format!("视频文件打开失败: {}", e)))? {
            return Err(SceneDetectorError::InvalidVideo("无法打开视频文件".to_string()));
        }

        // 获取视频信息
        let fps = cap.get(videoio::CAP_PROP_FPS)
            .map_err(|e| SceneDetectorError::Detection(format!("无法获取FPS: {}", e)))?;
        let frame_count = cap.get(videoio::CAP_PROP_FRAME_COUNT)
            .map_err(|e| SceneDetectorError::Detection(format!("无法获取帧数: {}", e)))? as usize;

        println!("视频信息: {} 帧, {:.2} FPS", frame_count, fps);

        // 设置进度条
        let progress = if self.config.show_progress {
            let pb = ProgressBar::new(frame_count as u64);
            pb.set_style(ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta})")
                .unwrap()
                .progress_chars("#>-"));
            Some(pb)
        } else {
            None
        };

        let mut transitions: Vec<usize> = Vec::new();
        let mut prev_frame: Option<Frame> = None;
        let mut frame_index = 0;

        // 处理视频帧
        loop {
            let mut mat = Mat::default();
            let ret = cap.read(&mut mat)
                .map_err(|e| SceneDetectorError::Detection(format!("读取帧失败: {}", e)))?;

            if !ret {
                break;
            }

            // 检查帧是否为空
            if mat.empty() {
                break;
            }

            let current_frame = Frame::OpenCV(mat);

            if let Some(ref prev) = prev_frame {
                // 计算帧差异
                let difference = self.diff_calculator.calculate_difference(prev, &current_frame)?;
                let threshold = self.adaptive_threshold.calculate_threshold(self.config.threshold, 0.1);

                if difference > threshold {
                    // 记录转场点
                    transitions.push(frame_index);

                    if self.config.show_progress {
                        println!("检测到转场: 帧 {} (差异度 {:.3})", frame_index, difference);
                    }

                    // 更新自适应阈值
                    self.adaptive_threshold.add_difference(difference);
                }
            }

            prev_frame = Some(current_frame);
            frame_index += 1;

            if let Some(ref pb) = progress {
                pb.set_position(frame_index as u64);
            }
        }

        if let Some(ref pb) = progress {
            pb.finish();
        }

        // 根据转场点生成场景
        let mut scenes = Vec::new();

        if frame_count > 0 {
            let mut scene_id = 0;
            let mut start_frame = 0;

            // 为每个转场点创建场景
            for &transition_frame in &transitions {
                if transition_frame > start_frame + self.config.min_scene_length as usize {
                    scenes.push(SceneTransition::new(
                        scene_id,
                        start_frame as u32,
                        (transition_frame - 1) as u32,
                        fps,
                        0.8, // 默认置信度
                    ));
                    scene_id += 1;
                    start_frame = transition_frame;
                }
            }

            // 添加最后一个场景（从最后一个转场点到视频结束）
            if start_frame < frame_count {
                scenes.push(SceneTransition::new(
                    scene_id,
                    start_frame as u32,
                    (frame_count - 1) as u32,
                    fps,
                    0.8, // 默认置信度
                ));
            }

            // 如果没有检测到转场，创建一个包含整个视频的场景
            if scenes.is_empty() {
                scenes.push(SceneTransition::new(
                    0,
                    0,
                    (frame_count - 1) as u32,
                    fps,
                    0.8, // 默认置信度
                ));
            }
        }

        let processing_time = std::time::Instant::now().elapsed();

        println!("检测结果:");
        println!("  检测到 {} 个镜头", scenes.len());
        if !scenes.is_empty() {
            let avg_duration = scenes.iter().map(|s| s.duration).sum::<f64>() / scenes.len() as f64;
            println!("  平均镜头长度: {:.2} 秒", avg_duration);
        }
        println!("  处理时间: {:.2} 秒", processing_time.as_secs_f64());

        Ok(DetectionResult::new(
            video_path.to_path_buf(),
            frame_count as u32,
            fps,
            scenes,
            &self.config,
            processing_time.as_secs_f64(),
        ))
    }

    /// 从图像序列检测场景转场
    fn detect_from_image_sequence<P: AsRef<Path>>(&mut self, dir_path: P) -> Result<DetectionResult> {
        let start_time = Instant::now();
        let dir_path = dir_path.as_ref();

        // 查找图像文件
        let image_files = self.find_image_files(dir_path)?;
        
        if image_files.is_empty() {
            return Err(SceneDetectorError::InvalidVideo(
                "No image files found in directory".to_string()
            ));
        }

        println!("找到 {} 个图像文件", image_files.len());
        println!("开始检测场景转场...");

        // 设置进度条
        let progress = if self.config.show_progress {
            let pb = ProgressBar::new(image_files.len() as u64);
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta})")
                    .unwrap()
                    .progress_chars("#>-"),
            );
            Some(pb)
        } else {
            None
        };

        // 执行检测
        let transitions = self.detect_transitions_from_images(&image_files, &progress)?;
        
        if let Some(pb) = &progress {
            pb.finish_with_message("检测完成");
        }

        // 生成场景列表
        let fps = 30.0; // 假设30fps
        let scenes = self.generate_scenes(transitions, fps);
        
        let processing_time = start_time.elapsed().as_secs_f64();
        
        println!("检测结果:");
        println!("  检测到 {} 个镜头", scenes.len());
        if !scenes.is_empty() {
            println!("  平均镜头长度: {:.2} 秒", 
                     scenes.iter().map(|s| s.duration).sum::<f64>() / scenes.len() as f64);
        }
        println!("  处理时间: {:.2} 秒", processing_time);

        Ok(DetectionResult::new(
            dir_path.to_path_buf(),
            image_files.len() as u32,
            fps,
            scenes,
            &self.config,
            processing_time,
        ))
    }

    /// 查找目录中的图像文件
    fn find_image_files<P: AsRef<Path>>(&self, dir_path: P) -> Result<Vec<std::path::PathBuf>> {
        let mut image_files = Vec::new();
        let image_extensions = ["jpg", "jpeg", "png", "bmp", "tiff"];

        for entry in fs::read_dir(dir_path)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if let Some(ext_str) = extension.to_str() {
                        if image_extensions.contains(&ext_str.to_lowercase().as_str()) {
                            image_files.push(path);
                        }
                    }
                }
            }
        }

        image_files.sort();
        Ok(image_files)
    }

    /// 从图像序列检测转场点
    fn detect_transitions_from_images(
        &mut self,
        image_files: &[std::path::PathBuf],
        progress: &Option<ProgressBar>,
    ) -> Result<Vec<u32>> {
        let mut transitions = Vec::new();
        let mut prev_frame: Option<Frame> = None;
        
        // 重置自适应阈值计算器
        self.adaptive_threshold.reset();

        for (index, image_path) in image_files.iter().enumerate() {
            // 更新进度
            if let Some(pb) = progress {
                pb.set_position(index as u64);
            }

            // 跳帧处理
            if index % (self.config.skip_frames as usize + 1) != 0 {
                continue;
            }

            // 加载图像
            let img = image::open(image_path)
                .map_err(|e| SceneDetectorError::VideoProcessing(format!("Failed to load image: {}", e)))?
                .to_rgb8();

            let frame = Frame::from_rgb_image(&img);

            // 验证帧有效性
            if !self.diff_calculator.is_valid_frame(&frame) {
                continue;
            }

            // 预处理帧（调整大小以提高速度）
            let processed_frame = self.diff_calculator.preprocess_frame(&frame, 320)?;

            // 检查是否为极端帧（全黑或全白）
            if self.diff_calculator.is_extreme_frame(&processed_frame, 10.0)? {
                continue;
            }

            // 计算与前一帧的差异
            if let Some(ref prev) = prev_frame {
                let difference = self.diff_calculator.calculate_difference(prev, &processed_frame)?;
                
                // 添加到自适应阈值计算器
                self.adaptive_threshold.add_difference(difference);
                
                // 计算自适应阈值
                let adaptive_threshold = self.adaptive_threshold.calculate_threshold(
                    self.config.threshold, 
                    0.5 // 敏感度
                );

                // 检测转场
                if difference > adaptive_threshold {
                    // 避免连续检测到的转场点过于接近
                    if transitions.is_empty() || 
                       index as u32 - transitions.last().unwrap() > self.config.min_scene_length {
                        transitions.push(index as u32);
                        
                        if self.config.show_progress {
                            println!("检测到转场: 帧 {} (差异度 {:.3})", index, difference);
                        }
                    }
                }
            }

            prev_frame = Some(processed_frame);
        }

        // 确保最后一帧作为结束点
        if !transitions.is_empty() && transitions.last() != Some(&(image_files.len() as u32 - 1)) {
            transitions.push(image_files.len() as u32 - 1);
        }

        Ok(transitions)
    }

    /// 创建演示结果（用于视频文件）
    fn create_demo_result<P: AsRef<Path>>(&self, video_path: P) -> Result<DetectionResult> {
        let start_time = Instant::now();
        
        println!("创建演示结果...");
        
        // 模拟的视频信息
        let fps = 24.0;
        let total_frames = 7200; // 5分钟的视频
        
        // 创建一些模拟的场景转场
        let demo_transitions = vec![0, 720, 1440, 2160, 2880, 3600, 4320, 5040, 5760, 6480, 7199];
        let scenes = self.generate_scenes(demo_transitions, fps);
        
        let processing_time = start_time.elapsed().as_secs_f64();
        
        println!("演示结果:");
        println!("  模拟检测到 {} 个镜头", scenes.len());
        println!("  平均镜头长度: {:.2} 秒", 
                 scenes.iter().map(|s| s.duration).sum::<f64>() / scenes.len() as f64);
        println!("  处理时间: {:.2} 秒", processing_time);

        Ok(DetectionResult::new(
            video_path.as_ref().to_path_buf(),
            total_frames,
            fps,
            scenes,
            &self.config,
            processing_time,
        ))
    }

    /// 根据转场点生成场景列表
    fn generate_scenes(&self, transitions: Vec<u32>, fps: f64) -> Vec<SceneTransition> {
        if transitions.is_empty() {
            return Vec::new();
        }

        let mut scenes = Vec::new();
        let mut start_frame = 0u32;

        for (scene_id, &end_frame) in transitions.iter().enumerate() {
            // 检查场景长度是否满足最小要求
            if end_frame >= start_frame && end_frame - start_frame >= self.config.min_scene_length {
                let scene = SceneTransition::new(
                    scene_id,
                    start_frame,
                    end_frame,
                    fps,
                    0.8 + (scene_id as f64 * 0.02), // 模拟置信度
                );
                scenes.push(scene);
            }
            
            start_frame = end_frame + 1;
        }

        // 过滤掉过短的场景
        scenes.into_iter()
            .filter(|scene| scene.frame_count() >= self.config.min_scene_length)
            .collect()
    }

    /// 获取配置的引用
    pub fn config(&self) -> &DetectorConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: DetectorConfig) {
        self.diff_calculator = FrameDifferenceCalculator::new(config.algorithm);
        self.config = config;
    }

    /// 验证输入文件/目录
    pub fn validate_input<P: AsRef<Path>>(input_path: P) -> Result<()> {
        let path = input_path.as_ref();
        
        if !path.exists() {
            return Err(SceneDetectorError::InvalidVideo(
                format!("Input path does not exist: {}", path.display())
            ));
        }

        if path.is_dir() {
            // 检查目录中是否有图像文件
            let image_extensions = ["jpg", "jpeg", "png", "bmp", "tiff"];
            let mut has_images = false;
            
            for entry in fs::read_dir(path)? {
                let entry = entry?;
                let file_path = entry.path();
                
                if file_path.is_file() {
                    if let Some(extension) = file_path.extension() {
                        if let Some(ext_str) = extension.to_str() {
                            if image_extensions.contains(&ext_str.to_lowercase().as_str()) {
                                has_images = true;
                                break;
                            }
                        }
                    }
                }
            }
            
            if !has_images {
                return Err(SceneDetectorError::InvalidVideo(
                    "No image files found in directory".to_string()
                ));
            }
        }

        Ok(())
    }
}

/// 批量处理多个输入
pub struct BatchProcessor {
    detector_config: DetectorConfig,
}

impl BatchProcessor {
    pub fn new(config: DetectorConfig) -> Self {
        Self {
            detector_config: config,
        }
    }

    /// 批量处理输入文件/目录
    pub fn process_inputs<P: AsRef<Path>>(&self, input_paths: &[P]) -> Result<Vec<DetectionResult>> {
        let mut results = Vec::new();
        
        for (index, input_path) in input_paths.iter().enumerate() {
            println!("处理输入 {}/{}: {}", 
                     index + 1, input_paths.len(), input_path.as_ref().display());
            
            let mut detector = SceneDetector::new(self.detector_config.clone());
            match detector.detect_scenes(input_path) {
                Ok(result) => {
                    results.push(result);
                    println!("✓ 处理完成\n");
                }
                Err(e) => {
                    eprintln!("✗ 处理失败: {}\n", e);
                    continue;
                }
            }
        }

        Ok(results)
    }
}
