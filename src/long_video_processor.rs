use std::collections::VecDeque;
use std::path::{Path, PathBuf};
use std::sync::{<PERSON>, Mutex};
use std::time::{Duration, Instant};
use rayon::prelude::*;
use indicatif::{ProgressBar, ProgressStyle};

use crate::frame_diff::{Frame, FrameDifferenceCalculator};
use crate::types::{
    DetectorConfig, LongVideoConfig, SmartSkipConfig, SceneTransition,
    DetectionResult, Result, SceneDetectorError
};
use crate::resume_manager::{ResumeManager, ProcessingProgress};

/// 长视频处理器 - 专门优化2小时以上视频的场景检测
pub struct LongVideoProcessor {
    config: DetectorConfig,
    long_config: LongVideoConfig,
    diff_calculator: FrameDifferenceCalculator,
    activity_analyzer: SceneActivityAnalyzer,
    chunk_processor: VideoChunkProcessor,
    memory_monitor: MemoryMonitor,
    resume_manager: ResumeManager,
}

impl LongVideoProcessor {
    pub fn new(mut config: DetectorConfig) -> Self {
        // 如果没有长视频配置，使用默认配置
        let long_config = config.long_video_config
            .take()
            .unwrap_or_else(LongVideoConfig::default);

        let diff_calculator = FrameDifferenceCalculator::new(config.algorithm);
        let activity_analyzer = SceneActivityAnalyzer::new(
            long_config.smart_skip_config.activity_window_size
        );
        let chunk_processor = VideoChunkProcessor::new(&long_config);
        let memory_monitor = MemoryMonitor::new(long_config.max_memory_mb);
        let resume_manager = ResumeManager::new(&long_config.cache_dir, long_config.enable_resume);

        Self {
            config,
            long_config,
            diff_calculator,
            activity_analyzer,
            chunk_processor,
            memory_monitor,
            resume_manager,
        }
    }

    /// 检测长视频场景转场 - 主入口函数
    pub fn detect_scenes<P: AsRef<Path>>(&mut self, video_path: P) -> Result<DetectionResult> {
        let video_path = video_path.as_ref();
        let start_time = Instant::now();

        println!("🎬 开始处理长视频: {}", video_path.display());

        // 1. 获取视频基本信息
        let video_info = self.get_video_info(video_path)?;
        println!("📊 视频信息: 时长 {:.1}分钟, 帧率 {:.1}fps, 总帧数 {}", 
                 video_info.duration / 60.0, video_info.fps, video_info.total_frames);

        // 2. 判断是否需要长视频优化
        if video_info.duration < self.long_config.enable_threshold_seconds {
            println!("⚡ 视频较短，使用标准处理模式");
            return self.process_standard_video(video_path, &video_info);
        }

        println!("🚀 启用长视频优化模式");

        // 3. 检查是否有缓存的进度
        let mut chunks = self.chunk_processor.create_chunks(&video_info)?;
        let mut completed_results = Vec::new();

        if let Some(progress) = self.resume_manager.load_progress(video_path, &self.config)? {
            println!("🔄 从断点继续处理: {:.1}% 已完成", progress.completion_percentage());

            // 使用缓存的已完成结果
            completed_results = progress.completed_results;

            // 只处理剩余的块
            chunks = progress.get_remaining_chunks();

            if chunks.is_empty() {
                println!("✅ 所有块已完成，直接合并结果");
                let scenes = self.merge_chunk_results(completed_results, &video_info)?;
                let processing_time = 0.1; // 几乎不需要时间

                return Ok(DetectionResult::new(
                    video_path.to_path_buf(),
                    video_info.total_frames,
                    video_info.fps,
                    scenes,
                    &self.config,
                    processing_time,
                ));
            }
        } else {
            println!("📦 视频分为 {} 个块进行处理", chunks.len());
        }

        // 4. 并行处理剩余的块
        let new_chunk_results = if !chunks.is_empty() {
            self.process_chunks_parallel_with_resume(video_path, &chunks, &mut completed_results)?
        } else {
            Vec::new()
        };

        // 合并所有结果
        completed_results.extend(new_chunk_results);
        let chunk_results = completed_results;

        // 5. 合并结果
        let scenes = self.merge_chunk_results(chunk_results, &video_info)?;

        // 6. 清理缓存
        self.resume_manager.clear_progress(video_path)?;

        // 6. 生成最终结果
        let processing_time = start_time.elapsed().as_secs_f64();
        let result = DetectionResult::new(
            video_path.to_path_buf(),
            video_info.total_frames,
            video_info.fps,
            scenes,
            &self.config,
            processing_time,
        );

        self.print_optimization_stats(&result);
        Ok(result)
    }

    /// 获取视频基本信息
    fn get_video_info<P: AsRef<Path>>(&self, video_path: P) -> Result<VideoInfo> {
        #[cfg(feature = "opencv_support")]
        {
            use opencv::{videoio, prelude::*};

            let mut cap = videoio::VideoCapture::from_file(
                video_path.as_ref().to_str().unwrap(),
                videoio::CAP_ANY
            ).map_err(|e| SceneDetectorError::Detection(format!("无法打开视频: {}", e)))?;

            let fps = cap.get(videoio::CAP_PROP_FPS)
                .map_err(|e| SceneDetectorError::Detection(format!("无法获取FPS: {}", e)))?;

            let frame_count = cap.get(videoio::CAP_PROP_FRAME_COUNT)
                .map_err(|e| SceneDetectorError::Detection(format!("无法获取帧数: {}", e)))? as u32;

            let duration = frame_count as f64 / fps;

            Ok(VideoInfo {
                fps,
                total_frames: frame_count,
                duration,
            })
        }

        #[cfg(not(feature = "opencv_support"))]
        {
            // 模拟视频信息用于测试
            println!("⚠️  OpenCV支持未启用，使用模拟视频信息");
            Ok(VideoInfo {
                fps: 30.0,
                total_frames: 216000, // 2小时 @ 30fps
                duration: 7200.0,     // 2小时
            })
        }
    }

    /// 标准视频处理（回退到原有逻辑）
    fn process_standard_video<P: AsRef<Path>>(
        &mut self,
        video_path: P,
        video_info: &VideoInfo
    ) -> Result<DetectionResult> {
        #[cfg(feature = "opencv_support")]
        {
            // 使用标准的单线程处理逻辑
            use opencv::{videoio, core::Mat, prelude::*};

        let video_path = video_path.as_ref();
        let start_time = std::time::Instant::now();

        let mut cap = videoio::VideoCapture::from_file(
            video_path.to_str().unwrap(),
            videoio::CAP_ANY
        ).map_err(|e| SceneDetectorError::Detection(format!("无法打开视频: {}", e)))?;

        let mut scenes = Vec::new();
        let mut transitions = Vec::new();
        let mut frame_index = 0u32;
        let mut prev_frame: Option<crate::frame_diff::Frame> = None;

        // 创建进度条
        let progress = if self.config.show_progress {
            let pb = indicatif::ProgressBar::new(video_info.total_frames as u64);
            pb.set_style(
                indicatif::ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} 帧 ({eta})")
                    .unwrap()
            );
            Some(pb)
        } else {
            None
        };

        // 处理视频帧
        loop {
            let mut mat = Mat::default();
            let ret = cap.read(&mut mat)
                .map_err(|e| SceneDetectorError::Detection(format!("读取帧失败: {}", e)))?;

            if !ret || mat.empty() {
                break;
            }

            // 更新进度
            if let Some(ref pb) = progress {
                pb.set_position(frame_index as u64);
            }

            // 跳帧处理
            if frame_index % (self.config.skip_frames + 1) != 0 {
                frame_index += 1;
                continue;
            }

            #[cfg(feature = "opencv_support")]
            let current_frame = crate::frame_diff::Frame::OpenCV(mat);

            #[cfg(not(feature = "opencv_support"))]
            let current_frame = crate::frame_diff::Frame::Simple {
                width: 640,
                height: 480,
                data: vec![0; 640 * 480 * 3],
            };

            if let Some(ref prev) = prev_frame {
                let difference = self.diff_calculator.calculate_difference(prev, &current_frame)?;

                if difference > self.config.threshold {
                    transitions.push(frame_index);
                }
            }

            prev_frame = Some(current_frame);
            frame_index += 1;
        }

        if let Some(ref pb) = progress {
            pb.finish_with_message("✅ 标准处理完成");
        }

        // 生成场景
        scenes = self.generate_standard_scenes(transitions, video_info);

        let processing_time = start_time.elapsed().as_secs_f64();

        Ok(DetectionResult::new(
            video_path.to_path_buf(),
            video_info.total_frames,
            video_info.fps,
            scenes,
            &self.config,
            processing_time,
        ))
        }

        #[cfg(not(feature = "opencv_support"))]
        {
            // 模拟标准处理用于测试
            println!("⚠️  OpenCV支持未启用，使用模拟标准处理");
            let processing_time = 1.0; // 模拟处理时间

            // 创建一些模拟场景
            let scenes = vec![
                SceneTransition::new(0, 0, 1800, video_info.fps, 0.8),
                SceneTransition::new(1, 1801, 3600, video_info.fps, 0.9),
                SceneTransition::new(2, 3601, video_info.total_frames - 1, video_info.fps, 0.85),
            ];

            Ok(DetectionResult::new(
                video_path.as_ref().to_path_buf(),
                video_info.total_frames,
                video_info.fps,
                scenes,
                &self.config,
                processing_time,
            ))
        }
    }

    /// 为标准处理生成场景列表
    fn generate_standard_scenes(&self, transitions: Vec<u32>, video_info: &VideoInfo) -> Vec<SceneTransition> {
        let mut scenes = Vec::new();

        if transitions.is_empty() {
            // 没有转场，整个视频作为一个场景
            scenes.push(SceneTransition::new(
                0,
                0,
                video_info.total_frames - 1,
                video_info.fps,
                0.8,
            ));
            return scenes;
        }

        let mut start_frame = 0u32;
        let mut scene_id = 0;

        // 为每个转场点创建场景
        for &transition_frame in &transitions {
            if transition_frame > start_frame + self.config.min_scene_length {
                scenes.push(SceneTransition::new(
                    scene_id,
                    start_frame,
                    transition_frame - 1,
                    video_info.fps,
                    0.8,
                ));
                scene_id += 1;
                start_frame = transition_frame;
            }
        }

        // 添加最后一个场景
        if start_frame < video_info.total_frames {
            scenes.push(SceneTransition::new(
                scene_id,
                start_frame,
                video_info.total_frames - 1,
                video_info.fps,
                0.8,
            ));
        }

        scenes
    }

    /// 并行处理视频块
    fn process_chunks_parallel<P: AsRef<Path>>(
        &mut self,
        video_path: P,
        chunks: &[VideoChunk],
    ) -> Result<Vec<ChunkResult>> {
        let video_path = video_path.as_ref();

        // 设置并行线程数
        let thread_count = if self.long_config.parallel_threads == 0 {
            num_cpus::get()
        } else {
            self.long_config.parallel_threads
        };

        println!("🔧 使用 {} 个线程并行处理 {} 个视频块", thread_count, chunks.len());

        // 创建线程池
        let pool = rayon::ThreadPoolBuilder::new()
            .num_threads(thread_count)
            .build()
            .map_err(|e| SceneDetectorError::Detection(format!("创建线程池失败: {}", e)))?;

        // 创建进度条
        let progress = Arc::new(Mutex::new(ProgressBar::new(chunks.len() as u64)));
        {
            let pb = progress.lock().unwrap();
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} 块 ({eta}) [{msg}]")
                    .unwrap()
            );
        }

        // 创建共享的配置和计算器
        let config = Arc::new(self.config.clone());
        let long_config = Arc::new(self.long_config.clone());
        let video_path_arc = Arc::new(video_path.to_path_buf());

        // 并行处理块
        let results: Result<Vec<ChunkResult>> = pool.install(|| {
            chunks
                .par_iter()
                .enumerate()
                .map(|(index, chunk)| {
                    // 为每个线程创建独立的处理器组件
                    let diff_calculator = FrameDifferenceCalculator::new(config.algorithm);
                    let memory_monitor = MemoryMonitor::new(long_config.max_memory_mb);

                    let processor = ChunkProcessor {
                        config: config.clone(),
                        long_config: long_config.clone(),
                        diff_calculator,
                        memory_monitor,
                    };

                    // 处理单个块
                    let result = processor.process_chunk(&video_path_arc, chunk, index);

                    // 更新进度
                    {
                        let pb = progress.lock().unwrap();
                        pb.inc(1);
                        pb.set_message(format!("处理块 {}/{}", index + 1, chunks.len()));
                    }

                    result
                })
                .collect()
        });

        // 完成进度条
        {
            let pb = progress.lock().unwrap();
            pb.finish_with_message("✅ 所有块处理完成");
        }

        results
    }

    /// 带断点续传的并行处理视频块
    fn process_chunks_parallel_with_resume<P: AsRef<Path>>(
        &mut self,
        video_path: P,
        remaining_chunks: &[VideoChunk],
        completed_results: &mut Vec<ChunkResult>,
    ) -> Result<Vec<ChunkResult>> {
        let video_path = video_path.as_ref();

        if remaining_chunks.is_empty() {
            return Ok(Vec::new());
        }

        println!("🔄 继续处理剩余 {} 个块", remaining_chunks.len());

        // 设置并行线程数
        let thread_count = if self.long_config.parallel_threads == 0 {
            num_cpus::get()
        } else {
            self.long_config.parallel_threads
        };

        // 创建线程池
        let pool = rayon::ThreadPoolBuilder::new()
            .num_threads(thread_count)
            .build()
            .map_err(|e| SceneDetectorError::Detection(format!("创建线程池失败: {}", e)))?;

        // 创建进度条
        let progress = Arc::new(Mutex::new(ProgressBar::new(remaining_chunks.len() as u64)));
        {
            let pb = progress.lock().unwrap();
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} 块 ({eta}) [{msg}]")
                    .unwrap()
            );
        }

        // 创建共享的配置和计算器
        let config = Arc::new(self.config.clone());
        let long_config = Arc::new(self.long_config.clone());
        let video_path_arc = Arc::new(video_path.to_path_buf());

        // 用于保存进度的共享状态
        let completed_results_arc = Arc::new(Mutex::new(completed_results.clone()));
        let all_chunks = self.chunk_processor.create_chunks(&self.get_video_info(video_path)?)?;

        // 并行处理剩余块
        let new_results: Result<Vec<ChunkResult>> = pool.install(|| {
            remaining_chunks
                .par_iter()
                .enumerate()
                .map(|(index, chunk)| {
                    // 为每个线程创建独立的处理器组件
                    let diff_calculator = FrameDifferenceCalculator::new(config.algorithm);
                    let memory_monitor = MemoryMonitor::new(long_config.max_memory_mb);

                    let processor = ChunkProcessor {
                        config: config.clone(),
                        long_config: long_config.clone(),
                        diff_calculator,
                        memory_monitor,
                    };

                    // 处理单个块
                    let result = processor.process_chunk(&video_path_arc, chunk, chunk.chunk_id);

                    // 更新进度并保存
                    if let Ok(ref chunk_result) = result {
                        // 更新共享的完成结果
                        {
                            let mut completed = completed_results_arc.lock().unwrap();
                            completed.push(chunk_result.clone());
                        }

                        // 定期保存进度（每5个块保存一次）
                        if index % 5 == 0 {
                            let completed = completed_results_arc.lock().unwrap();
                            // 注意：这里需要创建一个临时的ResumeManager实例
                            // 因为我们在并行环境中无法访问self
                            let temp_resume_manager = crate::resume_manager::ResumeManager::new(
                                &long_config.cache_dir,
                                long_config.enable_resume
                            );
                            if let Err(e) = temp_resume_manager.save_progress(
                                &video_path_arc,
                                &config,
                                &all_chunks,
                                &completed,
                            ) {
                                eprintln!("⚠️  保存进度失败: {}", e);
                            }
                        }
                    }

                    // 更新进度条
                    {
                        let pb = progress.lock().unwrap();
                        pb.inc(1);
                        pb.set_message(format!("处理块 {}/{}", index + 1, remaining_chunks.len()));
                    }

                    result
                })
                .collect()
        });

        // 完成进度条
        {
            let pb = progress.lock().unwrap();
            pb.finish_with_message("✅ 剩余块处理完成");
        }

        new_results
    }

    /// 处理单个视频块
    fn process_single_chunk<P: AsRef<Path>>(
        &self,
        video_path: P,
        chunk: &VideoChunk,
        chunk_index: usize,
    ) -> Result<ChunkResult> {
        use opencv::{videoio, core::Mat, prelude::*};
        use crate::smart_skip::AdaptiveFrameSkipper;

        let video_path = video_path.as_ref();
        let start_time = std::time::Instant::now();

        // 打开视频文件
        let mut cap = videoio::VideoCapture::from_file(
            video_path.to_str().unwrap(),
            videoio::CAP_ANY
        ).map_err(|e| SceneDetectorError::Detection(format!("无法打开视频: {}", e)))?;

        // 跳转到块的起始位置
        cap.set(videoio::CAP_PROP_POS_FRAMES, chunk.start_frame as f64)
            .map_err(|e| SceneDetectorError::Detection(format!("无法跳转到帧位置: {}", e)))?;

        // 创建智能跳帧器
        let mut frame_skipper = AdaptiveFrameSkipper::new(
            self.long_config.smart_skip_config.clone()
        );

        let mut scenes = Vec::new();
        let mut transitions = Vec::new();
        let mut current_frame_index = chunk.start_frame;
        let mut prev_frame: Option<crate::frame_diff::Frame> = None;
        let mut frames_processed = 0u32;
        let mut frames_skipped = 0u32;

        // 处理块内的帧
        while current_frame_index <= chunk.end_frame {
            // 检查内存使用
            self.memory_monitor.check_memory_usage()?;

            let mut mat = Mat::default();
            let ret = cap.read(&mut mat)
                .map_err(|e| SceneDetectorError::Detection(format!("读取帧失败: {}", e)))?;

            if !ret || mat.empty() {
                break;
            }

            let current_frame = crate::frame_diff::Frame::OpenCV(mat);
            frames_processed += 1;

            // 智能跳帧决策
            let skip_frames = if self.long_config.enable_smart_skip {
                frame_skipper.decide_skip_frames(&current_frame, prev_frame.as_ref())?
            } else {
                self.config.skip_frames
            };

            // 场景检测
            if let Some(ref prev) = prev_frame {
                let difference = self.diff_calculator.calculate_difference(prev, &current_frame)?;

                if difference > self.config.threshold {
                    // 记录转场点（相对于块的起始位置）
                    transitions.push(current_frame_index - chunk.start_frame);
                }
            }

            prev_frame = Some(current_frame);

            // 跳帧
            frames_skipped += skip_frames;
            current_frame_index += skip_frames + 1;

            // 跳转到下一个处理帧
            if skip_frames > 0 {
                cap.set(videoio::CAP_PROP_POS_FRAMES, current_frame_index as f64)
                    .map_err(|e| SceneDetectorError::Detection(format!("跳帧失败: {}", e)))?;
            }
        }

        // 根据转场点生成场景
        scenes = self.generate_chunk_scenes(transitions, chunk);

        let processing_time = start_time.elapsed().as_secs_f64();

        Ok(ChunkResult {
            chunk: chunk.clone(),
            scenes,
            processing_time,
            frames_processed,
            frames_skipped,
        })
    }

    /// 为单个块生成场景列表
    fn generate_chunk_scenes(&self, transitions: Vec<u32>, chunk: &VideoChunk) -> Vec<SceneTransition> {
        let mut scenes = Vec::new();
        let fps = 30.0; // 这里应该从视频信息中获取，暂时硬编码

        if transitions.is_empty() {
            // 没有转场，整个块作为一个场景
            scenes.push(SceneTransition::new(
                0,
                0, // 相对于块的起始位置
                chunk.end_frame - chunk.start_frame,
                fps,
                0.8,
            ));
            return scenes;
        }

        let mut start_frame = 0u32;
        let mut scene_id = 0;

        // 为每个转场点创建场景
        for &transition_frame in &transitions {
            if transition_frame > start_frame + self.config.min_scene_length {
                scenes.push(SceneTransition::new(
                    scene_id,
                    start_frame,
                    transition_frame - 1,
                    fps,
                    0.8,
                ));
                scene_id += 1;
                start_frame = transition_frame;
            }
        }

        // 添加最后一个场景
        let chunk_length = chunk.end_frame - chunk.start_frame;
        if start_frame < chunk_length {
            scenes.push(SceneTransition::new(
                scene_id,
                start_frame,
                chunk_length,
                fps,
                0.8,
            ));
        }

        scenes
    }

    /// 合并各块的检测结果
    fn merge_chunk_results(
        &self,
        chunk_results: Vec<ChunkResult>,
        video_info: &VideoInfo,
    ) -> Result<Vec<SceneTransition>> {
        let mut all_scenes = Vec::new();
        let mut scene_id = 0;

        for chunk_result in chunk_results {
            for mut scene in chunk_result.scenes {
                // 调整场景ID和时间偏移
                scene.scene_id = scene_id;
                scene.start_frame += chunk_result.chunk.start_frame;
                scene.end_frame += chunk_result.chunk.start_frame;
                scene.start_time = scene.start_frame as f64 / video_info.fps;
                scene.end_time = scene.end_frame as f64 / video_info.fps;
                scene.duration = scene.end_time - scene.start_time;

                all_scenes.push(scene);
                scene_id += 1;
            }
        }

        // 处理块边界的重复场景
        self.deduplicate_boundary_scenes(&mut all_scenes);

        Ok(all_scenes)
    }

    /// 去除块边界的重复场景
    fn deduplicate_boundary_scenes(&self, scenes: &mut Vec<SceneTransition>) {
        // 简化实现：移除时间重叠的场景
        scenes.sort_by(|a, b| a.start_time.partial_cmp(&b.start_time).unwrap());
        
        let mut i = 0;
        while i + 1 < scenes.len() {
            if scenes[i].end_time > scenes[i + 1].start_time {
                // 有重叠，合并或移除
                if scenes[i + 1].end_time > scenes[i].end_time {
                    scenes[i].end_time = scenes[i + 1].end_time;
                    scenes[i].end_frame = scenes[i + 1].end_frame;
                }
                scenes.remove(i + 1);
            } else {
                i += 1;
            }
        }
    }

    /// 打印优化统计信息
    fn print_optimization_stats(&self, result: &DetectionResult) {
        println!("\n📈 长视频优化统计:");
        println!("  🎯 检测到 {} 个场景", result.scenes.len());
        println!("  ⏱️  处理时间: {:.1} 秒", result.processing_time);
        println!("  🚀 平均处理速度: {:.1}x 实时速度", 
                 result.duration / result.processing_time);
        
        if let Some(avg_duration) = result.scenes.iter()
            .map(|s| s.duration)
            .reduce(|a, b| a + b)
            .map(|sum| sum / result.scenes.len() as f64) {
            println!("  📊 平均场景长度: {:.1} 秒", avg_duration);
        }
    }
}

/// 视频基本信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct VideoInfo {
    pub fps: f64,
    pub total_frames: u32,
    pub duration: f64,
}

/// 视频块信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct VideoChunk {
    pub chunk_id: usize,
    pub start_frame: u32,
    pub end_frame: u32,
    pub start_time: f64,
    pub end_time: f64,
}

/// 块处理结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ChunkResult {
    pub chunk: VideoChunk,
    pub scenes: Vec<SceneTransition>,
    pub processing_time: f64,
    pub frames_processed: u32,
    pub frames_skipped: u32,
}

/// 场景活动度分析器 - 用于智能跳帧
pub struct SceneActivityAnalyzer {
    activity_history: VecDeque<f64>,
    window_size: usize,
}

impl SceneActivityAnalyzer {
    pub fn new(window_size: usize) -> Self {
        Self {
            activity_history: VecDeque::with_capacity(window_size),
            window_size,
        }
    }

    /// 计算当前帧的活动度
    pub fn calculate_activity(&mut self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        // 简化的活动度计算：基于像素差异
        let pixel_diff = self.calculate_pixel_difference(frame1, frame2)?;
        
        // 添加到历史记录
        if self.activity_history.len() >= self.window_size {
            self.activity_history.pop_front();
        }
        self.activity_history.push_back(pixel_diff);

        Ok(pixel_diff)
    }

    /// 获取平均活动度
    pub fn get_average_activity(&self) -> f64 {
        if self.activity_history.is_empty() {
            0.0
        } else {
            self.activity_history.iter().sum::<f64>() / self.activity_history.len() as f64
        }
    }

    /// 简化的像素差异计算
    fn calculate_pixel_difference(&self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        // 这里应该实现具体的像素差异计算
        // 为了简化，返回模拟值
        Ok(0.3) // 占位符
    }
}

/// 视频块处理器
pub struct VideoChunkProcessor {
    chunk_duration: f64,
    overlap_duration: f64,
}

impl VideoChunkProcessor {
    pub fn new(config: &LongVideoConfig) -> Self {
        Self {
            chunk_duration: config.chunk_duration_seconds,
            overlap_duration: config.chunk_overlap_seconds,
        }
    }

    /// 创建视频块
    pub fn create_chunks(&self, video_info: &VideoInfo) -> Result<Vec<VideoChunk>> {
        let mut chunks = Vec::new();
        let mut current_time = 0.0;
        let mut chunk_id = 0;

        while current_time < video_info.duration {
            let start_time = current_time;
            let end_time = (current_time + self.chunk_duration).min(video_info.duration);
            
            let start_frame = (start_time * video_info.fps) as u32;
            let end_frame = (end_time * video_info.fps) as u32;

            chunks.push(VideoChunk {
                chunk_id,
                start_frame,
                end_frame,
                start_time,
                end_time,
            });

            // 下一个块的开始时间（考虑重叠）
            current_time = end_time - self.overlap_duration;
            chunk_id += 1;
        }

        Ok(chunks)
    }
}

/// 内存监控器
pub struct MemoryMonitor {
    max_memory_mb: usize,
    warning_threshold: f64,  // 警告阈值（比例）
    critical_threshold: f64, // 临界阈值（比例）
    check_interval: usize,   // 检查间隔（帧数）
    frame_counter: std::cell::Cell<usize>,
}

impl MemoryMonitor {
    pub fn new(max_memory_mb: usize) -> Self {
        Self {
            max_memory_mb,
            warning_threshold: 0.8,   // 80%时警告
            critical_threshold: 0.95, // 95%时强制清理
            check_interval: 100,      // 每100帧检查一次
            frame_counter: std::cell::Cell::new(0),
        }
    }

    /// 检查内存使用情况
    pub fn check_memory_usage(&self) -> Result<()> {
        // 增加帧计数器
        let count = self.frame_counter.get() + 1;
        self.frame_counter.set(count);

        // 只在指定间隔检查内存
        if count % self.check_interval != 0 {
            return Ok(());
        }

        // 获取当前内存使用情况
        let current_memory_mb = self.get_current_memory_usage()?;
        let usage_ratio = current_memory_mb as f64 / self.max_memory_mb as f64;

        if usage_ratio > self.critical_threshold {
            // 临界状态：强制垃圾回收
            self.force_garbage_collection();

            // 再次检查
            let after_gc_memory = self.get_current_memory_usage()?;
            let after_gc_ratio = after_gc_memory as f64 / self.max_memory_mb as f64;

            if after_gc_ratio > self.critical_threshold {
                return Err(SceneDetectorError::Detection(
                    format!("内存使用过高: {:.1}MB / {}MB ({:.1}%), 请减少块大小或增加内存限制",
                            after_gc_memory, self.max_memory_mb, after_gc_ratio * 100.0)
                ));
            }

            println!("⚠️  内存使用过高，已执行垃圾回收: {:.1}MB → {:.1}MB",
                     current_memory_mb, after_gc_memory);

        } else if usage_ratio > self.warning_threshold {
            // 警告状态：提示用户
            println!("💾 内存使用警告: {:.1}MB / {}MB ({:.1}%)",
                     current_memory_mb, self.max_memory_mb, usage_ratio * 100.0);
        }

        Ok(())
    }

    /// 获取当前内存使用情况（MB）
    fn get_current_memory_usage(&self) -> Result<f64> {
        // 在实际实现中，这里应该调用系统API获取内存使用情况
        // 为了简化，我们使用一个模拟的实现

        #[cfg(target_os = "linux")]
        {
            self.get_linux_memory_usage()
        }

        #[cfg(target_os = "windows")]
        {
            self.get_windows_memory_usage()
        }

        #[cfg(target_os = "macos")]
        {
            self.get_macos_memory_usage()
        }

        #[cfg(not(any(target_os = "linux", target_os = "windows", target_os = "macos")))]
        {
            // 其他系统：返回模拟值
            Ok(512.0) // 模拟512MB使用
        }
    }

    #[cfg(target_os = "linux")]
    fn get_linux_memory_usage(&self) -> Result<f64> {
        use std::fs;

        // 读取 /proc/self/status 获取内存使用情况
        let status = fs::read_to_string("/proc/self/status")
            .map_err(|e| SceneDetectorError::Detection(format!("无法读取内存状态: {}", e)))?;

        for line in status.lines() {
            if line.starts_with("VmRSS:") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    let kb: f64 = parts[1].parse()
                        .map_err(|e| SceneDetectorError::Detection(format!("解析内存使用失败: {}", e)))?;
                    return Ok(kb / 1024.0); // 转换为MB
                }
            }
        }

        Ok(512.0) // 默认值
    }

    #[cfg(target_os = "windows")]
    fn get_windows_memory_usage(&self) -> Result<f64> {
        // Windows实现：使用GetProcessMemoryInfo
        // 为了简化，返回模拟值
        Ok(512.0)
    }

    #[cfg(target_os = "macos")]
    fn get_macos_memory_usage(&self) -> Result<f64> {
        // macOS实现：使用task_info
        // 为了简化，返回模拟值
        Ok(512.0)
    }

    /// 强制垃圾回收
    fn force_garbage_collection(&self) {
        // 在Rust中，我们不能直接控制垃圾回收
        // 但可以尝试一些内存优化策略

        // 1. 建议系统回收内存
        #[cfg(unix)]
        {
            unsafe {
                libc::malloc_trim(0);
            }
        }

        // 2. 强制执行任何待处理的析构函数
        std::thread::yield_now();

        println!("🧹 执行内存清理操作");
    }

    /// 获取内存使用统计
    pub fn get_memory_stats(&self) -> Result<MemoryStats> {
        let current_mb = self.get_current_memory_usage()?;
        let usage_ratio = current_mb / self.max_memory_mb as f64;

        Ok(MemoryStats {
            current_mb,
            max_mb: self.max_memory_mb as f64,
            usage_ratio,
            is_warning: usage_ratio > self.warning_threshold,
            is_critical: usage_ratio > self.critical_threshold,
        })
    }
}

/// 内存使用统计
#[derive(Debug, Clone)]
pub struct MemoryStats {
    pub current_mb: f64,
    pub max_mb: f64,
    pub usage_ratio: f64,
    pub is_warning: bool,
    pub is_critical: bool,
}

/// 块处理器 - 用于并行处理单个视频块
pub struct ChunkProcessor {
    config: Arc<DetectorConfig>,
    long_config: Arc<LongVideoConfig>,
    diff_calculator: FrameDifferenceCalculator,
    memory_monitor: MemoryMonitor,
}

impl ChunkProcessor {
    /// 处理单个视频块
    pub fn process_chunk<P: AsRef<Path>>(
        &self,
        video_path: P,
        chunk: &VideoChunk,
        chunk_index: usize,
    ) -> Result<ChunkResult> {
        #[cfg(feature = "opencv_support")]
        {
            use opencv::{videoio, core::Mat, prelude::*};
            use crate::smart_skip::AdaptiveFrameSkipper;

        let video_path = video_path.as_ref();
        let start_time = std::time::Instant::now();

        // 打开视频文件
        let mut cap = videoio::VideoCapture::from_file(
            video_path.to_str().unwrap(),
            videoio::CAP_ANY
        ).map_err(|e| SceneDetectorError::Detection(format!("块{}: 无法打开视频: {}", chunk_index, e)))?;

        // 跳转到块的起始位置
        cap.set(videoio::CAP_PROP_POS_FRAMES, chunk.start_frame as f64)
            .map_err(|e| SceneDetectorError::Detection(format!("块{}: 无法跳转到帧位置: {}", chunk_index, e)))?;

        // 创建智能跳帧器
        let mut frame_skipper = AdaptiveFrameSkipper::new(
            self.long_config.smart_skip_config.clone()
        );

        let mut scenes = Vec::new();
        let mut transitions = Vec::new();
        let mut current_frame_index = chunk.start_frame;
        let mut prev_frame: Option<crate::frame_diff::Frame> = None;
        let mut frames_processed = 0u32;
        let mut frames_skipped = 0u32;

        // 处理块内的帧
        while current_frame_index <= chunk.end_frame {
            // 检查内存使用
            self.memory_monitor.check_memory_usage()?;

            let mut mat = Mat::default();
            let ret = cap.read(&mut mat)
                .map_err(|e| SceneDetectorError::Detection(format!("块{}: 读取帧失败: {}", chunk_index, e)))?;

            if !ret || mat.empty() {
                break;
            }

            #[cfg(feature = "opencv_support")]
            let current_frame = crate::frame_diff::Frame::OpenCV(mat);

            #[cfg(not(feature = "opencv_support"))]
            let current_frame = crate::frame_diff::Frame::Simple {
                width: 640,
                height: 480,
                data: vec![0; 640 * 480 * 3],
            };
            frames_processed += 1;

            // 智能跳帧决策
            let skip_frames = if self.long_config.enable_smart_skip {
                frame_skipper.decide_skip_frames(&current_frame, prev_frame.as_ref())?
            } else {
                self.config.skip_frames
            };

            // 场景检测
            if let Some(ref prev) = prev_frame {
                let difference = self.diff_calculator.calculate_difference(prev, &current_frame)?;

                if difference > self.config.threshold {
                    // 记录转场点（相对于块的起始位置）
                    transitions.push(current_frame_index - chunk.start_frame);
                }
            }

            prev_frame = Some(current_frame);

            // 跳帧
            frames_skipped += skip_frames;
            current_frame_index += skip_frames + 1;

            // 跳转到下一个处理帧
            if skip_frames > 0 {
                cap.set(videoio::CAP_PROP_POS_FRAMES, current_frame_index as f64)
                    .map_err(|e| SceneDetectorError::Detection(format!("块{}: 跳帧失败: {}", chunk_index, e)))?;
            }
        }

        // 根据转场点生成场景
        scenes = self.generate_chunk_scenes(transitions, chunk);

        let processing_time = start_time.elapsed().as_secs_f64();

            Ok(ChunkResult {
                chunk: chunk.clone(),
                scenes,
                processing_time,
                frames_processed,
                frames_skipped,
            })
        }

        #[cfg(not(feature = "opencv_support"))]
        {
            // 模拟块处理用于测试
            use std::thread;
            use std::time::Duration;

            let start_time = std::time::Instant::now();

            // 模拟处理时间
            thread::sleep(Duration::from_millis(100));

            // 创建模拟场景
            let scenes = vec![
                SceneTransition::new(
                    0,
                    0,
                    chunk.end_frame - chunk.start_frame,
                    30.0, // 模拟fps
                    0.8,
                )
            ];

            let processing_time = start_time.elapsed().as_secs_f64();

            Ok(ChunkResult {
                chunk: chunk.clone(),
                scenes,
                processing_time,
                frames_processed: 100, // 模拟处理的帧数
                frames_skipped: 50,    // 模拟跳过的帧数
            })
        }
    }

    /// 为单个块生成场景列表
    fn generate_chunk_scenes(&self, transitions: Vec<u32>, chunk: &VideoChunk) -> Vec<SceneTransition> {
        let mut scenes = Vec::new();
        let fps = 30.0; // 这里应该从视频信息中获取，暂时硬编码

        if transitions.is_empty() {
            // 没有转场，整个块作为一个场景
            scenes.push(SceneTransition::new(
                0,
                0, // 相对于块的起始位置
                chunk.end_frame - chunk.start_frame,
                fps,
                0.8,
            ));
            return scenes;
        }

        let mut start_frame = 0u32;
        let mut scene_id = 0;

        // 为每个转场点创建场景
        for &transition_frame in &transitions {
            if transition_frame > start_frame + self.config.min_scene_length {
                scenes.push(SceneTransition::new(
                    scene_id,
                    start_frame,
                    transition_frame - 1,
                    fps,
                    0.8,
                ));
                scene_id += 1;
                start_frame = transition_frame;
            }
        }

        // 添加最后一个场景
        let chunk_length = chunk.end_frame - chunk.start_frame;
        if start_frame < chunk_length {
            scenes.push(SceneTransition::new(
                scene_id,
                start_frame,
                chunk_length,
                fps,
                0.8,
            ));
        }

        scenes
    }
}
