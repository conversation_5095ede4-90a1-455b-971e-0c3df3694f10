use std::collections::VecDeque;
use crate::frame_diff::Frame;
use crate::types::{SmartSkipConfig, Result, SceneDetectorError};

/// 智能跳帧决策器 - 根据场景活动度自动调整采样密度
pub struct AdaptiveFrameSkipper {
    config: SmartSkipConfig,
    activity_analyzer: SceneActivityAnalyzer,
    transition_predictor: TransitionPredictor,
    skip_stats: SkipStatistics,
}

impl AdaptiveFrameSkipper {
    pub fn new(config: SmartSkipConfig) -> Self {
        let activity_analyzer = SceneActivityAnalyzer::new(config.activity_window_size);
        let transition_predictor = TransitionPredictor::new(20); // 20帧预测窗口
        let skip_stats = SkipStatistics::new();

        Self {
            config,
            activity_analyzer,
            transition_predictor,
            skip_stats,
        }
    }

    /// 决定下一帧的跳帧数量
    pub fn decide_skip_frames(
        &mut self, 
        current_frame: &Frame, 
        prev_frame: Option<&Frame>
    ) -> Result<u32> {
        // 如果没有前一帧，使用基础跳帧数
        let Some(prev) = prev_frame else {
            return Ok(self.config.base_skip_frames);
        };

        // 1. 计算当前活动度
        let activity = self.activity_analyzer.calculate_activity(prev, current_frame)?;
        
        // 2. 预测是否可能发生转场
        let frame_diff = self.calculate_frame_difference(prev, current_frame)?;
        let might_transition = self.transition_predictor.predict_transition(frame_diff);

        // 3. 根据情况决定跳帧策略
        let skip_frames = if might_transition {
            // 可能转场：使用最小跳帧数，确保不错过转场
            self.config.min_skip_frames
        } else {
            // 正常情况：根据活动度决定
            self.calculate_skip_by_activity(activity)
        };

        // 4. 更新统计信息
        self.skip_stats.record_skip(skip_frames, activity);

        Ok(skip_frames)
    }

    /// 根据活动度计算跳帧数
    fn calculate_skip_by_activity(&self, activity: f64) -> u32 {
        if activity < self.config.low_activity_threshold {
            // 低活动度场景：激进跳帧（如静态风景）
            self.config.max_skip_frames
        } else if activity > self.config.high_activity_threshold {
            // 高活动度场景：保守跳帧（如动作场面）
            self.config.min_skip_frames
        } else {
            // 中等活动度：线性插值
            let ratio = (activity - self.config.low_activity_threshold) / 
                       (self.config.high_activity_threshold - self.config.low_activity_threshold);
            
            let skip = self.config.max_skip_frames as f64 - 
                      ratio * (self.config.max_skip_frames - self.config.min_skip_frames) as f64;
            
            skip.round().max(self.config.min_skip_frames as f64) as u32
        }
    }

    /// 简化的帧差异计算
    fn calculate_frame_difference(&self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        // 这里应该调用实际的帧差异计算
        // 为了简化，使用模拟计算
        match (frame1, frame2) {
            (Frame::OpenCV(_), Frame::OpenCV(_)) => {
                // 实际应该计算OpenCV帧的差异
                Ok(0.3) // 占位符
            }
            (Frame::Simple { data: data1, .. }, Frame::Simple { data: data2, .. }) => {
                let mut total_diff = 0.0;
                let len = data1.len().min(data2.len());
                
                for i in 0..len {
                    let diff = (data1[i] as f64 - data2[i] as f64).abs();
                    total_diff += diff;
                }
                
                Ok(total_diff / (len as f64 * 255.0)) // 归一化
            }
            #[cfg(feature = "opencv_support")]
            _ => Err(SceneDetectorError::Detection(
                "Frame types must match".to_string()
            ))
        }
    }

    /// 获取跳帧统计信息
    pub fn get_statistics(&self) -> &SkipStatistics {
        &self.skip_stats
    }

    /// 重置统计信息
    pub fn reset_statistics(&mut self) {
        self.skip_stats.reset();
    }
}

/// 场景活动度分析器
pub struct SceneActivityAnalyzer {
    activity_history: VecDeque<f64>,
    window_size: usize,
    current_average: f64,
}

impl SceneActivityAnalyzer {
    pub fn new(window_size: usize) -> Self {
        Self {
            activity_history: VecDeque::with_capacity(window_size),
            window_size,
            current_average: 0.0,
        }
    }

    /// 计算两帧之间的活动度
    pub fn calculate_activity(&mut self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        // 1. 计算像素变化程度
        let pixel_change = self.calculate_pixel_variance(frame1, frame2)?;
        
        // 2. 计算边缘变化（简化版）
        let edge_change = self.calculate_edge_variance(frame1, frame2)?;
        
        // 3. 组合活动度指标
        let activity = 0.7 * pixel_change + 0.3 * edge_change;
        
        // 4. 更新历史记录
        self.update_activity_history(activity);
        
        Ok(activity)
    }

    /// 计算像素变化程度
    fn calculate_pixel_variance(&self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        match (frame1, frame2) {
            (Frame::Simple { data: data1, width: w1, height: h1 }, 
             Frame::Simple { data: data2, width: w2, height: h2 }) => {
                
                if w1 != w2 || h1 != h2 {
                    return Err(SceneDetectorError::Detection(
                        "Frame dimensions must match".to_string()
                    ));
                }

                let mut variance_sum = 0.0;
                let len = data1.len().min(data2.len());
                
                // 计算像素差异的方差
                for i in 0..len {
                    let diff = (data1[i] as f64 - data2[i] as f64).abs();
                    variance_sum += diff * diff;
                }
                
                let variance = variance_sum / len as f64;
                Ok((variance / (255.0 * 255.0)).sqrt()) // 归一化并开方
            }
            _ => {
                // 对于OpenCV帧，使用简化计算
                Ok(0.3) // 占位符
            }
        }
    }

    /// 计算边缘变化程度（简化版）
    fn calculate_edge_variance(&self, _frame1: &Frame, _frame2: &Frame) -> Result<f64> {
        // 简化实现：实际应该进行边缘检测和比较
        Ok(0.2) // 占位符
    }

    /// 更新活动度历史记录
    fn update_activity_history(&mut self, activity: f64) {
        if self.activity_history.len() >= self.window_size {
            self.activity_history.pop_front();
        }
        self.activity_history.push_back(activity);
        
        // 更新平均值
        self.current_average = self.activity_history.iter().sum::<f64>() 
                              / self.activity_history.len() as f64;
    }

    /// 获取当前平均活动度
    pub fn get_average_activity(&self) -> f64 {
        self.current_average
    }

    /// 判断当前是否为静态场景
    pub fn is_static_scene(&self, threshold: f64) -> bool {
        self.current_average < threshold
    }

    /// 判断当前是否为动态场景
    pub fn is_dynamic_scene(&self, threshold: f64) -> bool {
        self.current_average > threshold
    }
}

/// 转场预测器 - 预测即将发生的转场
pub struct TransitionPredictor {
    difference_history: VecDeque<f64>,
    window_size: usize,
    trend_threshold: f64,
    variance_threshold: f64,
}

impl TransitionPredictor {
    pub fn new(window_size: usize) -> Self {
        Self {
            difference_history: VecDeque::with_capacity(window_size),
            window_size,
            trend_threshold: 0.1,    // 趋势阈值
            variance_threshold: 0.05, // 方差阈值
        }
    }

    /// 预测是否即将发生转场
    pub fn predict_transition(&mut self, current_diff: f64) -> bool {
        self.difference_history.push_back(current_diff);
        
        if self.difference_history.len() < self.window_size {
            return false;
        }

        // 保持窗口大小
        if self.difference_history.len() > self.window_size {
            self.difference_history.pop_front();
        }

        // 计算趋势和方差
        let trend = self.calculate_trend();
        let variance = self.calculate_variance();

        // 如果差异值快速上升且方差增大，可能即将转场
        trend > self.trend_threshold && variance > self.variance_threshold
    }

    /// 计算差异值的上升趋势
    fn calculate_trend(&self) -> f64 {
        if self.difference_history.len() < 3 {
            return 0.0;
        }

        let data: Vec<f64> = self.difference_history.iter().cloned().collect();
        let n = data.len() as f64;
        
        // 简单线性回归计算斜率
        let sum_x: f64 = (0..data.len()).map(|i| i as f64).sum();
        let sum_y: f64 = data.iter().sum();
        let sum_xy: f64 = data.iter().enumerate()
            .map(|(i, &y)| i as f64 * y)
            .sum();
        let sum_x2: f64 = (0..data.len()).map(|i| (i as f64) * (i as f64)).sum();

        let slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x);
        slope.max(0.0) // 只关心上升趋势
    }

    /// 计算差异值的方差
    fn calculate_variance(&self) -> f64 {
        if self.difference_history.len() < 2 {
            return 0.0;
        }

        let mean: f64 = self.difference_history.iter().sum::<f64>() 
                       / self.difference_history.len() as f64;
        
        let variance: f64 = self.difference_history.iter()
            .map(|&x| (x - mean) * (x - mean))
            .sum::<f64>() / self.difference_history.len() as f64;

        variance
    }
}

/// 跳帧统计信息
#[derive(Debug, Clone)]
pub struct SkipStatistics {
    pub total_frames_processed: u32,
    pub total_frames_skipped: u32,
    pub skip_distribution: [u32; 11], // 跳帧数分布 (0-10帧)
    pub average_activity: f64,
    pub static_scene_ratio: f64,
    pub dynamic_scene_ratio: f64,
}

impl SkipStatistics {
    pub fn new() -> Self {
        Self {
            total_frames_processed: 0,
            total_frames_skipped: 0,
            skip_distribution: [0; 11],
            average_activity: 0.0,
            static_scene_ratio: 0.0,
            dynamic_scene_ratio: 0.0,
        }
    }

    /// 记录一次跳帧决策
    pub fn record_skip(&mut self, skip_frames: u32, activity: f64) {
        self.total_frames_processed += 1;
        self.total_frames_skipped += skip_frames;
        
        // 更新跳帧分布
        let index = skip_frames.min(10) as usize;
        self.skip_distribution[index] += 1;
        
        // 更新平均活动度
        let total = self.total_frames_processed as f64;
        self.average_activity = (self.average_activity * (total - 1.0) + activity) / total;
        
        // 更新场景类型比例
        if activity < 0.1 {
            self.static_scene_ratio = (self.static_scene_ratio * (total - 1.0) + 1.0) / total;
        } else if activity > 0.7 {
            self.dynamic_scene_ratio = (self.dynamic_scene_ratio * (total - 1.0) + 1.0) / total;
        }
    }

    /// 重置统计信息
    pub fn reset(&mut self) {
        *self = Self::new();
    }

    /// 计算平均跳帧数
    pub fn average_skip_frames(&self) -> f64 {
        if self.total_frames_processed == 0 {
            0.0
        } else {
            self.total_frames_skipped as f64 / self.total_frames_processed as f64
        }
    }

    /// 计算处理效率提升比例
    pub fn efficiency_gain(&self) -> f64 {
        if self.total_frames_processed == 0 {
            1.0
        } else {
            (self.total_frames_processed + self.total_frames_skipped) as f64 
            / self.total_frames_processed as f64
        }
    }
}
