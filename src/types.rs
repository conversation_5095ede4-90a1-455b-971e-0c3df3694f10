use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 镜头转场信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SceneTransition {
    /// 镜头ID
    pub scene_id: usize,
    /// 起始帧号
    pub start_frame: u32,
    /// 结束帧号
    pub end_frame: u32,
    /// 起始时间戳（秒）
    pub start_time: f64,
    /// 结束时间戳（秒）
    pub end_time: f64,
    /// 镜头持续时间（秒）
    pub duration: f64,
    /// 转场检测置信度
    pub confidence: f64,
    /// 首帧文件路径
    pub first_frame_path: Option<PathBuf>,
    /// 尾帧文件路径
    pub last_frame_path: Option<PathBuf>,
    /// 关键帧文件路径列表
    pub keyframes_paths: Vec<PathBuf>,
    /// 视频片段文件路径
    pub clip_path: Option<String>,
}

impl SceneTransition {
    pub fn new(
        scene_id: usize,
        start_frame: u32,
        end_frame: u32,
        fps: f64,
        confidence: f64,
    ) -> Self {
        let start_time = start_frame as f64 / fps;
        let end_time = end_frame as f64 / fps;
        let duration = end_time - start_time;

        Self {
            scene_id,
            start_frame,
            end_frame,
            start_time,
            end_time,
            duration,
            confidence,
            first_frame_path: None,
            last_frame_path: None,
            keyframes_paths: Vec::new(),
            clip_path: None,
        }
    }

    /// 获取镜头帧数
    pub fn frame_count(&self) -> u32 {
        self.end_frame - self.start_frame + 1
    }
}

/// 检测器配置参数
#[derive(Debug, Clone)]
pub struct DetectorConfig {
    /// 转场检测阈值 (0.0-1.0)
    pub threshold: f64,
    /// 最小镜头长度（帧数）
    pub min_scene_length: u32,
    /// 跳帧数（用于加速处理）
    pub skip_frames: u32,
    /// 检测算法类型
    pub algorithm: DetectionAlgorithm,
    /// 是否提取关键帧
    pub extract_keyframes: bool,
    /// 关键帧提取间隔（帧数）
    pub keyframe_interval: u32,
    /// 输出目录
    pub output_dir: PathBuf,
    /// 是否显示进度条
    pub show_progress: bool,
    /// 是否提取视频片段
    pub extract_clips: bool,
    /// 长视频优化配置
    pub long_video_config: Option<LongVideoConfig>,
}

/// 长视频处理优化配置
#[derive(Debug, Clone)]
pub struct LongVideoConfig {
    /// 启用长视频优化（视频时长超过此值时启用，单位：秒）
    pub enable_threshold_seconds: f64,
    /// 每个处理块的时长（秒）
    pub chunk_duration_seconds: f64,
    /// 块间重叠时长（秒，用于避免边界转场丢失）
    pub chunk_overlap_seconds: f64,
    /// 最大内存使用限制（MB）
    pub max_memory_mb: usize,
    /// 并行处理线程数（0表示自动检测）
    pub parallel_threads: usize,
    /// 启用智能跳帧
    pub enable_smart_skip: bool,
    /// 智能跳帧配置
    pub smart_skip_config: SmartSkipConfig,
    /// 启用断点续传
    pub enable_resume: bool,
    /// 缓存目录
    pub cache_dir: PathBuf,
}

/// 智能跳帧配置
#[derive(Debug, Clone)]
pub struct SmartSkipConfig {
    /// 基础跳帧数
    pub base_skip_frames: u32,
    /// 最大跳帧数（静态场景）
    pub max_skip_frames: u32,
    /// 最小跳帧数（动态场景）
    pub min_skip_frames: u32,
    /// 低活动度阈值
    pub low_activity_threshold: f64,
    /// 高活动度阈值
    pub high_activity_threshold: f64,
    /// 活动度分析窗口大小（帧数）
    pub activity_window_size: usize,
}

impl Default for DetectorConfig {
    fn default() -> Self {
        Self {
            threshold: 0.3,
            min_scene_length: 30,
            skip_frames: 1,
            algorithm: DetectionAlgorithm::Histogram,
            extract_keyframes: false,
            keyframe_interval: 30,
            output_dir: PathBuf::from("output"),
            show_progress: true,
            extract_clips: false,
            long_video_config: None,
        }
    }
}

impl Default for LongVideoConfig {
    fn default() -> Self {
        Self {
            enable_threshold_seconds: 1800.0, // 30分钟以上启用优化
            chunk_duration_seconds: 600.0,    // 每块10分钟
            chunk_overlap_seconds: 30.0,      // 30秒重叠
            max_memory_mb: 2048,               // 2GB内存限制
            parallel_threads: 0,               // 自动检测CPU核心数
            enable_smart_skip: true,
            smart_skip_config: SmartSkipConfig::default(),
            enable_resume: true,
            cache_dir: PathBuf::from("cache"),
        }
    }
}

impl Default for SmartSkipConfig {
    fn default() -> Self {
        Self {
            base_skip_frames: 2,
            max_skip_frames: 10,      // 静态场景最多跳10帧
            min_skip_frames: 1,       // 动态场景最少跳1帧
            low_activity_threshold: 0.1,
            high_activity_threshold: 0.7,
            activity_window_size: 30, // 30帧窗口分析活动度
        }
    }
}

/// 检测算法类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DetectionAlgorithm {
    /// 直方图差异法（推荐）
    Histogram,
    /// 像素差异法
    Pixel,
    /// 边缘差异法
    Edge,
    /// 组合算法
    Combined,
}

impl DetectionAlgorithm {
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "histogram" | "hist" => Some(Self::Histogram),
            "pixel" | "pix" => Some(Self::Pixel),
            "edge" => Some(Self::Edge),
            "combined" | "combo" => Some(Self::Combined),
            _ => None,
        }
    }

    pub fn as_str(&self) -> &'static str {
        match self {
            Self::Histogram => "histogram",
            Self::Pixel => "pixel",
            Self::Edge => "edge",
            Self::Combined => "combined",
        }
    }
}

/// 检测结果
#[derive(Debug, Serialize, Deserialize)]
pub struct DetectionResult {
    /// 输入视频文件路径
    pub input_file: PathBuf,
    /// 视频总帧数
    pub total_frames: u32,
    /// 视频帧率
    pub fps: f64,
    /// 视频总时长（秒）
    pub duration: f64,
    /// 检测到的镜头列表
    pub scenes: Vec<SceneTransition>,
    /// 检测配置
    pub config: DetectorConfigSummary,
    /// 处理时间戳
    pub processed_at: DateTime<Utc>,
    /// 处理耗时（秒）
    pub processing_time: f64,
}

impl DetectionResult {
    pub fn new(
        input_file: PathBuf,
        total_frames: u32,
        fps: f64,
        scenes: Vec<SceneTransition>,
        config: &DetectorConfig,
        processing_time: f64,
    ) -> Self {
        let duration = total_frames as f64 / fps;
        
        Self {
            input_file,
            total_frames,
            fps,
            duration,
            scenes,
            config: DetectorConfigSummary::from(config),
            processed_at: Utc::now(),
            processing_time,
        }
    }

    /// 获取镜头总数
    pub fn scene_count(&self) -> usize {
        self.scenes.len()
    }

    /// 获取平均镜头长度（秒）
    pub fn average_scene_duration(&self) -> f64 {
        if self.scenes.is_empty() {
            0.0
        } else {
            self.scenes.iter().map(|s| s.duration).sum::<f64>() / self.scenes.len() as f64
        }
    }
}

/// 检测器配置摘要（用于序列化）
#[derive(Debug, Serialize, Deserialize)]
pub struct DetectorConfigSummary {
    pub threshold: f64,
    pub min_scene_length: u32,
    pub skip_frames: u32,
    pub algorithm: String,
    pub extract_keyframes: bool,
    pub keyframe_interval: u32,
}

impl From<&DetectorConfig> for DetectorConfigSummary {
    fn from(config: &DetectorConfig) -> Self {
        Self {
            threshold: config.threshold,
            min_scene_length: config.min_scene_length,
            skip_frames: config.skip_frames,
            algorithm: config.algorithm.as_str().to_string(),
            extract_keyframes: config.extract_keyframes,
            keyframe_interval: config.keyframe_interval,
        }
    }
}

/// 错误类型定义
#[derive(thiserror::Error, Debug)]
pub enum SceneDetectorError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Image processing error: {0}")]
    Image(#[from] image::ImageError),

    #[error("JSON serialization error: {0}")]
    Json(#[from] serde_json::Error),

    #[error("Invalid video file: {0}")]
    InvalidVideo(String),

    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),

    #[error("Frame extraction error: {0}")]
    FrameExtraction(String),

    #[error("Detection error: {0}")]
    Detection(String),

    #[error("Video processing error: {0}")]
    VideoProcessing(String),
}

pub type Result<T> = std::result::Result<T, SceneDetectorError>;
