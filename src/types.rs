use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 镜头转场信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SceneTransition {
    /// 镜头ID
    pub scene_id: usize,
    /// 起始帧号
    pub start_frame: u32,
    /// 结束帧号
    pub end_frame: u32,
    /// 起始时间戳（秒）
    pub start_time: f64,
    /// 结束时间戳（秒）
    pub end_time: f64,
    /// 镜头持续时间（秒）
    pub duration: f64,
    /// 转场检测置信度
    pub confidence: f64,
    /// 首帧文件路径
    pub first_frame_path: Option<PathBuf>,
    /// 尾帧文件路径
    pub last_frame_path: Option<PathBuf>,
    /// 关键帧文件路径列表
    pub keyframes_paths: Vec<PathBuf>,
    /// 视频片段文件路径
    pub clip_path: Option<String>,
}

impl SceneTransition {
    pub fn new(
        scene_id: usize,
        start_frame: u32,
        end_frame: u32,
        fps: f64,
        confidence: f64,
    ) -> Self {
        let start_time = start_frame as f64 / fps;
        let end_time = end_frame as f64 / fps;
        let duration = end_time - start_time;

        Self {
            scene_id,
            start_frame,
            end_frame,
            start_time,
            end_time,
            duration,
            confidence,
            first_frame_path: None,
            last_frame_path: None,
            keyframes_paths: Vec::new(),
            clip_path: None,
        }
    }

    /// 获取镜头帧数
    pub fn frame_count(&self) -> u32 {
        self.end_frame - self.start_frame + 1
    }
}

/// 检测器配置参数
#[derive(Debug, Clone)]
pub struct DetectorConfig {
    /// 转场检测阈值 (0.0-1.0)
    pub threshold: f64,
    /// 最小镜头长度（帧数）
    pub min_scene_length: u32,
    /// 跳帧数（用于加速处理）
    pub skip_frames: u32,
    /// 检测算法类型
    pub algorithm: DetectionAlgorithm,
    /// 是否提取关键帧
    pub extract_keyframes: bool,
    /// 关键帧提取间隔（帧数）
    pub keyframe_interval: u32,
    /// 输出目录
    pub output_dir: PathBuf,
    /// 是否显示进度条
    pub show_progress: bool,
    /// 是否提取视频片段
    pub extract_clips: bool,
}

impl Default for DetectorConfig {
    fn default() -> Self {
        Self {
            threshold: 0.3,
            min_scene_length: 30,
            skip_frames: 1,
            algorithm: DetectionAlgorithm::Histogram,
            extract_keyframes: false,
            keyframe_interval: 30,
            output_dir: PathBuf::from("output"),
            show_progress: true,
            extract_clips: false,
        }
    }
}

/// 检测算法类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DetectionAlgorithm {
    /// 直方图差异法（推荐）
    Histogram,
    /// 像素差异法
    Pixel,
    /// 边缘差异法
    Edge,
    /// 组合算法
    Combined,
}

impl DetectionAlgorithm {
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "histogram" | "hist" => Some(Self::Histogram),
            "pixel" | "pix" => Some(Self::Pixel),
            "edge" => Some(Self::Edge),
            "combined" | "combo" => Some(Self::Combined),
            _ => None,
        }
    }

    pub fn as_str(&self) -> &'static str {
        match self {
            Self::Histogram => "histogram",
            Self::Pixel => "pixel",
            Self::Edge => "edge",
            Self::Combined => "combined",
        }
    }
}

/// 检测结果
#[derive(Debug, Serialize, Deserialize)]
pub struct DetectionResult {
    /// 输入视频文件路径
    pub input_file: PathBuf,
    /// 视频总帧数
    pub total_frames: u32,
    /// 视频帧率
    pub fps: f64,
    /// 视频总时长（秒）
    pub duration: f64,
    /// 检测到的镜头列表
    pub scenes: Vec<SceneTransition>,
    /// 检测配置
    pub config: DetectorConfigSummary,
    /// 处理时间戳
    pub processed_at: DateTime<Utc>,
    /// 处理耗时（秒）
    pub processing_time: f64,
}

impl DetectionResult {
    pub fn new(
        input_file: PathBuf,
        total_frames: u32,
        fps: f64,
        scenes: Vec<SceneTransition>,
        config: &DetectorConfig,
        processing_time: f64,
    ) -> Self {
        let duration = total_frames as f64 / fps;
        
        Self {
            input_file,
            total_frames,
            fps,
            duration,
            scenes,
            config: DetectorConfigSummary::from(config),
            processed_at: Utc::now(),
            processing_time,
        }
    }

    /// 获取镜头总数
    pub fn scene_count(&self) -> usize {
        self.scenes.len()
    }

    /// 获取平均镜头长度（秒）
    pub fn average_scene_duration(&self) -> f64 {
        if self.scenes.is_empty() {
            0.0
        } else {
            self.scenes.iter().map(|s| s.duration).sum::<f64>() / self.scenes.len() as f64
        }
    }
}

/// 检测器配置摘要（用于序列化）
#[derive(Debug, Serialize, Deserialize)]
pub struct DetectorConfigSummary {
    pub threshold: f64,
    pub min_scene_length: u32,
    pub skip_frames: u32,
    pub algorithm: String,
    pub extract_keyframes: bool,
    pub keyframe_interval: u32,
}

impl From<&DetectorConfig> for DetectorConfigSummary {
    fn from(config: &DetectorConfig) -> Self {
        Self {
            threshold: config.threshold,
            min_scene_length: config.min_scene_length,
            skip_frames: config.skip_frames,
            algorithm: config.algorithm.as_str().to_string(),
            extract_keyframes: config.extract_keyframes,
            keyframe_interval: config.keyframe_interval,
        }
    }
}

/// 错误类型定义
#[derive(thiserror::Error, Debug)]
pub enum SceneDetectorError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Image processing error: {0}")]
    Image(#[from] image::ImageError),

    #[error("JSON serialization error: {0}")]
    Json(#[from] serde_json::Error),

    #[error("Invalid video file: {0}")]
    InvalidVideo(String),

    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),

    #[error("Frame extraction error: {0}")]
    FrameExtraction(String),

    #[error("Detection error: {0}")]
    Detection(String),

    #[error("Video processing error: {0}")]
    VideoProcessing(String),
}

pub type Result<T> = std::result::Result<T, SceneDetectorError>;
