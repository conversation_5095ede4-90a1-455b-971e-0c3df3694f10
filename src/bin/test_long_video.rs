use scene_detector::{
    DetectorConfig, LongVideoConfig, SmartSkipConfig, 
    SceneDetector, DetectionAlgorithm
};
use std::path::PathBuf;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎬 长视频优化测试程序");

    // 测试基本配置
    test_basic_configuration()?;
    
    // 测试预设优化
    test_preset_optimizations()?;
    
    // 测试自定义配置
    test_custom_configuration()?;

    println!("✅ 所有测试通过！");
    Ok(())
}

fn test_basic_configuration() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📋 测试1: 基本配置");
    
    let config = DetectorConfig::default();
    let detector = SceneDetector::new(config);
    
    println!("  ✓ 默认配置创建成功");
    println!("  ✓ 检测器初始化成功");
    
    Ok(())
}

fn test_preset_optimizations() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🎯 测试2: 预设优化");
    
    let mut detector = SceneDetector::new(DetectorConfig::default());
    
    // 测试电影优化
    detector.optimize_for_movie();
    println!("  ✓ 电影优化预设配置成功");
    
    // 测试纪录片优化
    detector.optimize_for_documentary();
    println!("  ✓ 纪录片优化预设配置成功");
    
    // 测试直播优化
    detector.optimize_for_livestream();
    println!("  ✓ 直播优化预设配置成功");
    
    Ok(())
}

fn test_custom_configuration() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n⚙️  测试3: 自定义配置");
    
    // 创建自定义长视频配置
    let long_config = LongVideoConfig {
        enable_threshold_seconds: 1800.0,
        chunk_duration_seconds: 600.0,
        chunk_overlap_seconds: 30.0,
        max_memory_mb: 2048,
        parallel_threads: 4,
        enable_smart_skip: true,
        smart_skip_config: SmartSkipConfig {
            base_skip_frames: 2,
            max_skip_frames: 10,
            min_skip_frames: 1,
            low_activity_threshold: 0.1,
            high_activity_threshold: 0.7,
            activity_window_size: 30,
        },
        enable_resume: true,
        cache_dir: PathBuf::from("test_cache"),
    };
    
    let config = DetectorConfig {
        threshold: 0.3,
        min_scene_length: 30,
        skip_frames: 1,
        algorithm: DetectionAlgorithm::Combined,
        extract_keyframes: false,
        keyframe_interval: 30,
        output_dir: PathBuf::from("test_output"),
        show_progress: true,
        extract_clips: false,
        long_video_config: Some(long_config),
    };
    
    let detector = SceneDetector::new(config);
    println!("  ✓ 自定义长视频配置创建成功");
    
    // 测试配置参数
    let detector_config = detector.config();
    if let Some(ref long_cfg) = detector_config.long_video_config {
        println!("  ✓ 长视频配置验证:");
        println!("    - 启用阈值: {:.0}秒", long_cfg.enable_threshold_seconds);
        println!("    - 块大小: {:.0}秒", long_cfg.chunk_duration_seconds);
        println!("    - 内存限制: {}MB", long_cfg.max_memory_mb);
        println!("    - 并行线程: {}", long_cfg.parallel_threads);
        println!("    - 智能跳帧: {}", long_cfg.enable_smart_skip);
        println!("    - 断点续传: {}", long_cfg.enable_resume);
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = DetectorConfig::default();
        assert_eq!(config.threshold, 0.3);
        assert_eq!(config.min_scene_length, 30);
        assert_eq!(config.skip_frames, 1);
    }

    #[test]
    fn test_long_video_config() {
        let long_config = LongVideoConfig::default();
        assert_eq!(long_config.enable_threshold_seconds, 1800.0);
        assert_eq!(long_config.chunk_duration_seconds, 600.0);
        assert!(long_config.enable_smart_skip);
        assert!(long_config.enable_resume);
    }

    #[test]
    fn test_smart_skip_config() {
        let skip_config = SmartSkipConfig::default();
        assert_eq!(skip_config.base_skip_frames, 2);
        assert_eq!(skip_config.max_skip_frames, 10);
        assert_eq!(skip_config.min_skip_frames, 1);
    }

    #[test]
    fn test_detector_creation() {
        let config = DetectorConfig::default();
        let detector = SceneDetector::new(config);
        
        // 基本验证
        assert_eq!(detector.config().threshold, 0.3);
    }

    #[test]
    fn test_preset_optimizations() {
        let mut detector = SceneDetector::new(DetectorConfig::default());
        
        // 测试所有预设都能正常调用
        detector.optimize_for_movie();
        detector.optimize_for_documentary();
        detector.optimize_for_livestream();
        
        // 验证配置已更新
        assert!(detector.config().long_video_config.is_some());
    }

    #[test]
    fn test_custom_long_video_config() {
        let mut detector = SceneDetector::new(DetectorConfig::default());
        
        detector.configure_long_video(15.0, 3.0, true);
        
        let config = detector.config();
        if let Some(ref long_cfg) = config.long_video_config {
            assert_eq!(long_cfg.chunk_duration_seconds, 15.0 * 60.0);
            assert_eq!(long_cfg.max_memory_mb, 3072); // 3GB in MB
            assert!(long_cfg.enable_smart_skip);
        } else {
            panic!("Long video config should be set");
        }
    }

    #[test]
    fn test_algorithm_types() {
        use DetectionAlgorithm::*;
        
        let algorithms = vec![Histogram, Pixel, Edge, Combined];
        
        for algorithm in algorithms {
            let mut config = DetectorConfig::default();
            config.algorithm = algorithm;
            
            let detector = SceneDetector::new(config);
            assert_eq!(detector.config().algorithm, algorithm);
        }
    }

    #[test]
    fn test_memory_and_performance_settings() {
        let long_config = LongVideoConfig {
            enable_threshold_seconds: 3600.0, // 1小时
            chunk_duration_seconds: 300.0,    // 5分钟块
            chunk_overlap_seconds: 15.0,      // 15秒重叠
            max_memory_mb: 1024,               // 1GB
            parallel_threads: 8,               // 8线程
            enable_smart_skip: true,
            smart_skip_config: SmartSkipConfig::default(),
            enable_resume: true,
            cache_dir: PathBuf::from("performance_test_cache"),
        };

        // 验证所有设置都正确
        assert_eq!(long_config.enable_threshold_seconds, 3600.0);
        assert_eq!(long_config.chunk_duration_seconds, 300.0);
        assert_eq!(long_config.max_memory_mb, 1024);
        assert_eq!(long_config.parallel_threads, 8);
    }
}
