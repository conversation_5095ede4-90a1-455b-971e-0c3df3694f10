use crate::types::{DetectionAlgorithm, Result, SceneDetectorError};

#[cfg(feature = "opencv_support")]
use opencv::{
    core::{Mat, CV_8UC3},
    imgproc,
    prelude::*,
};

use image::{<PERSON>Buffer, Rgb, RgbImage};

/// 帧数据结构 - 支持OpenCV Mat和简化版本
#[derive(Clone)]
pub enum Frame {
    #[cfg(feature = "opencv_support")]
    OpenCV(Mat),
    Simple {
        width: u32,
        height: u32,
        data: Vec<u8>, // RGB数据
    },
}

impl Frame {
    pub fn new_simple(width: u32, height: u32, data: Vec<u8>) -> Self {
        Self::Simple { width, height, data }
    }

    pub fn from_rgb_image(img: &RgbImage) -> Self {
        Self::Simple {
            width: img.width(),
            height: img.height(),
            data: img.as_raw().clone(),
        }
    }

    pub fn from_opencv_mat(mat: Mat) -> Self {
        Self::OpenCV(mat)
    }

    pub fn is_empty(&self) -> bool {
        match self {
            Self::OpenCV(mat) => mat.empty(),
            Self::Simple { data, .. } => data.is_empty(),
        }
    }

    pub fn channels(&self) -> u32 {
        match self {
            Self::OpenCV(mat) => mat.channels() as u32,
            Self::Simple { .. } => 3, // RGB
        }
    }

    pub fn pixel_count(&self) -> usize {
        match self {
            Self::OpenCV(mat) => (mat.rows() * mat.cols()) as usize,
            Self::Simple { width, height, .. } => (*width * *height) as usize,
        }
    }

    pub fn width(&self) -> u32 {
        match self {
            Self::OpenCV(mat) => mat.cols() as u32,
            Self::Simple { width, .. } => *width,
        }
    }

    pub fn height(&self) -> u32 {
        match self {
            Self::OpenCV(mat) => mat.rows() as u32,
            Self::Simple { height, .. } => *height,
        }
    }
}

/// 帧差异计算器
pub struct FrameDifferenceCalculator {
    algorithm: DetectionAlgorithm,
    hist_bins: usize,
}

impl FrameDifferenceCalculator {
    pub fn new(algorithm: DetectionAlgorithm) -> Self {
        Self {
            algorithm,
            hist_bins: 64, // 简化的直方图bins数量
        }
    }

    /// 计算两帧之间的差异度
    pub fn calculate_difference(&self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        if frame1.width() != frame2.width() || frame1.height() != frame2.height() {
            return Err(SceneDetectorError::Detection(
                "Frame dimensions do not match".to_string()
            ));
        }

        match self.algorithm {
            DetectionAlgorithm::Histogram => self.histogram_difference(frame1, frame2),
            DetectionAlgorithm::Pixel => self.pixel_difference(frame1, frame2),
            DetectionAlgorithm::Edge => self.edge_difference(frame1, frame2),
            DetectionAlgorithm::Combined => self.combined_difference(frame1, frame2),
        }
    }

    /// 直方图差异法 - 推荐方法，对光照变化鲁棒
    fn histogram_difference(&self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        match (frame1, frame2) {
            (Frame::OpenCV(mat1), Frame::OpenCV(mat2)) => {
                self.opencv_histogram_difference(mat1, mat2)
            }
            _ => {
                let hist1 = self.calculate_rgb_histogram(frame1)?;
                let hist2 = self.calculate_rgb_histogram(frame2)?;
                let correlation = self.calculate_histogram_correlation(&hist1, &hist2);
                Ok(1.0 - correlation.max(0.0))
            }
        }
    }

    /// 使用OpenCV计算直方图差异
    fn opencv_histogram_difference(&self, mat1: &Mat, mat2: &Mat) -> Result<f64> {
        use opencv::{core, imgproc};

        // 计算直方图
        let hist_size = core::Vector::<i32>::from_iter([self.hist_bins as i32]);
        let ranges = core::Vector::<f32>::from_iter([0.0f32, 256.0f32]);
        let channels = core::Vector::<i32>::from_iter([0i32]);

        let mut hist1 = Mat::default();
        let mut hist2 = Mat::default();

        // 创建Mat向量
        let mut mats1 = core::Vector::<Mat>::new();
        mats1.push(mat1.clone());
        let mut mats2 = core::Vector::<Mat>::new();
        mats2.push(mat2.clone());

        imgproc::calc_hist(
            &mats1,
            &channels,
            &core::no_array(),
            &mut hist1,
            &hist_size,
            &ranges,
            false,
        ).map_err(|e| SceneDetectorError::Detection(format!("OpenCV histogram error: {}", e)))?;

        imgproc::calc_hist(
            &mats2,
            &channels,
            &core::no_array(),
            &mut hist2,
            &hist_size,
            &ranges,
            false,
        ).map_err(|e| SceneDetectorError::Detection(format!("OpenCV histogram error: {}", e)))?;

        // 计算相关性
        let correlation = imgproc::compare_hist(&hist1, &hist2, imgproc::HISTCMP_CORREL)
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV correlation error: {}", e)))?;

        Ok(1.0 - correlation.max(0.0))
    }

    /// 计算RGB直方图（简化版本）
    fn calculate_rgb_histogram(&self, frame: &Frame) -> Result<Vec<Vec<f64>>> {
        match frame {
            Frame::Simple { data, .. } => {
                let mut histograms = vec![vec![0.0; self.hist_bins]; 3]; // R, G, B
                let bin_size = 256.0 / self.hist_bins as f64;

                for i in (0..data.len()).step_by(3) {
                    let r = data[i] as f64;
                    let g = data[i + 1] as f64;
                    let b = data[i + 2] as f64;

                    let r_bin = ((r / bin_size) as usize).min(self.hist_bins - 1);
                    let g_bin = ((g / bin_size) as usize).min(self.hist_bins - 1);
                    let b_bin = ((b / bin_size) as usize).min(self.hist_bins - 1);

                    histograms[0][r_bin] += 1.0;
                    histograms[1][g_bin] += 1.0;
                    histograms[2][b_bin] += 1.0;
                }

                // 归一化
                let pixel_count = frame.pixel_count() as f64;
                for channel in &mut histograms {
                    for bin in channel {
                        *bin /= pixel_count;
                    }
                }

                Ok(histograms)
            }
            Frame::OpenCV(_) => {
                Err(SceneDetectorError::Detection(
                    "Use opencv_histogram_difference for OpenCV frames".to_string()
                ))
            }
        }
    }

    /// 计算直方图相关性
    fn calculate_histogram_correlation(&self, hist1: &[Vec<f64>], hist2: &[Vec<f64>]) -> f64 {
        let mut total_correlation = 0.0;

        for channel in 0..3 {
            let correlation = self.calculate_channel_correlation(&hist1[channel], &hist2[channel]);
            total_correlation += correlation;
        }

        total_correlation / 3.0
    }

    /// 计算单通道相关性
    fn calculate_channel_correlation(&self, hist1: &[f64], hist2: &[f64]) -> f64 {
        let mean1 = hist1.iter().sum::<f64>() / hist1.len() as f64;
        let mean2 = hist2.iter().sum::<f64>() / hist2.len() as f64;

        let mut numerator = 0.0;
        let mut sum_sq1 = 0.0;
        let mut sum_sq2 = 0.0;

        for i in 0..hist1.len() {
            let diff1 = hist1[i] - mean1;
            let diff2 = hist2[i] - mean2;
            
            numerator += diff1 * diff2;
            sum_sq1 += diff1 * diff1;
            sum_sq2 += diff2 * diff2;
        }

        let denominator = (sum_sq1 * sum_sq2).sqrt();
        if denominator == 0.0 {
            1.0 // 完全相同
        } else {
            numerator / denominator
        }
    }

    /// 像素差异法 - 快速但精度较低
    fn pixel_difference(&self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        match (frame1, frame2) {
            (Frame::OpenCV(mat1), Frame::OpenCV(mat2)) => {
                self.opencv_pixel_difference(mat1, mat2)
            }
            (Frame::Simple { data: data1, .. }, Frame::Simple { data: data2, .. }) => {
                let mut total_diff = 0.0;

                for i in 0..data1.len() {
                    let diff = (data1[i] as f64 - data2[i] as f64).abs();
                    total_diff += diff;
                }

                let avg_diff = total_diff / data1.len() as f64;
                Ok(avg_diff / 255.0) // 归一化到0-1
            }
            _ => Err(SceneDetectorError::Detection(
                "Frame types must match".to_string()
            ))
        }
    }

    /// 使用OpenCV计算像素差异
    fn opencv_pixel_difference(&self, mat1: &Mat, mat2: &Mat) -> Result<f64> {
        use opencv::core;

        let mut diff = Mat::default();
        core::absdiff(mat1, mat2, &mut diff)
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV absdiff error: {}", e)))?;

        let mean = core::mean(&diff, &core::no_array())
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV mean error: {}", e)))?;

        // 计算所有通道的平均值
        let avg_diff = (mean[0] + mean[1] + mean[2]) / 3.0;
        Ok(avg_diff / 255.0) // 归一化到0-1
    }

    /// 边缘差异法 - 对光照变化鲁棒
    fn edge_difference(&self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        match (frame1, frame2) {
            (Frame::OpenCV(mat1), Frame::OpenCV(mat2)) => {
                self.opencv_edge_difference(mat1, mat2)
            }
            _ => {
                let edges1 = self.simple_edge_detection(frame1)?;
                let edges2 = self.simple_edge_detection(frame2)?;

                let mut diff_count = 0;
                for i in 0..edges1.len() {
                    if edges1[i] != edges2[i] {
                        diff_count += 1;
                    }
                }

                Ok(diff_count as f64 / edges1.len() as f64)
            }
        }
    }

    /// 使用OpenCV计算边缘差异
    fn opencv_edge_difference(&self, mat1: &Mat, mat2: &Mat) -> Result<f64> {
        use opencv::{core, imgproc};

        // 转换为灰度图
        let mut gray1 = Mat::default();
        let mut gray2 = Mat::default();
        imgproc::cvt_color(mat1, &mut gray1, imgproc::COLOR_BGR2GRAY, 0)
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV color conversion error: {}", e)))?;
        imgproc::cvt_color(mat2, &mut gray2, imgproc::COLOR_BGR2GRAY, 0)
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV color conversion error: {}", e)))?;

        // Canny边缘检测
        let mut edges1 = Mat::default();
        let mut edges2 = Mat::default();
        imgproc::canny(&gray1, &mut edges1, 50.0, 150.0, 3, false)
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV Canny error: {}", e)))?;
        imgproc::canny(&gray2, &mut edges2, 50.0, 150.0, 3, false)
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV Canny error: {}", e)))?;

        // 计算差异
        let mut diff = Mat::default();
        core::absdiff(&edges1, &edges2, &mut diff)
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV absdiff error: {}", e)))?;

        let mean = core::mean(&diff, &core::no_array())
            .map_err(|e| SceneDetectorError::Detection(format!("OpenCV mean error: {}", e)))?;

        Ok(mean[0] / 255.0) // 归一化到0-1
    }

    /// 简单的边缘检测（Sobel算子简化版）
    fn simple_edge_detection(&self, frame: &Frame) -> Result<Vec<bool>> {
        match frame {
            Frame::Simple { width, height, data } => {
                let mut edges = vec![false; frame.pixel_count()];
                let width = *width as usize;
                let height = *height as usize;

                // 转换为灰度
                let gray = self.rgb_to_grayscale(frame)?;

                for y in 1..height - 1 {
                    for x in 1..width - 1 {
                        let idx = y * width + x;

                        // 简化的Sobel算子
                        let gx = gray[(y - 1) * width + (x + 1)] as f64
                            + 2.0 * gray[y * width + (x + 1)] as f64
                            + gray[(y + 1) * width + (x + 1)] as f64
                            - gray[(y - 1) * width + (x - 1)] as f64
                            - 2.0 * gray[y * width + (x - 1)] as f64
                            - gray[(y + 1) * width + (x - 1)] as f64;

                        let gy = gray[(y + 1) * width + (x - 1)] as f64
                            + 2.0 * gray[(y + 1) * width + x] as f64
                            + gray[(y + 1) * width + (x + 1)] as f64
                            - gray[(y - 1) * width + (x - 1)] as f64
                            - 2.0 * gray[(y - 1) * width + x] as f64
                            - gray[(y - 1) * width + (x + 1)] as f64;

                        let magnitude = (gx * gx + gy * gy).sqrt();
                        edges[idx] = magnitude > 50.0; // 阈值
                    }
                }

                Ok(edges)
            }
            Frame::OpenCV(_) => {
                Err(SceneDetectorError::Detection(
                    "Use opencv_edge_difference for OpenCV frames".to_string()
                ))
            }
        }
    }

    /// RGB转灰度
    fn rgb_to_grayscale(&self, frame: &Frame) -> Result<Vec<u8>> {
        match frame {
            Frame::Simple { data, .. } => {
                let mut gray = Vec::with_capacity(frame.pixel_count());

                for i in (0..data.len()).step_by(3) {
                    let r = data[i] as f64;
                    let g = data[i + 1] as f64;
                    let b = data[i + 2] as f64;

                    // 标准灰度转换公式
                    let gray_value = (0.299 * r + 0.587 * g + 0.114 * b) as u8;
                    gray.push(gray_value);
                }

                Ok(gray)
            }
            Frame::OpenCV(_) => {
                Err(SceneDetectorError::Detection(
                    "Use OpenCV methods for OpenCV frames".to_string()
                ))
            }
        }
    }

    /// 组合算法 - 结合多种方法
    fn combined_difference(&self, frame1: &Frame, frame2: &Frame) -> Result<f64> {
        let hist_diff = self.histogram_difference(frame1, frame2)?;
        let pixel_diff = self.pixel_difference(frame1, frame2)?;
        let edge_diff = self.edge_difference(frame1, frame2)?;

        // 加权组合 (直方图权重最高)
        let combined = 0.5 * hist_diff + 0.3 * edge_diff + 0.2 * pixel_diff;
        Ok(combined)
    }

    /// 预处理帧 - 调整大小以提高处理速度
    pub fn preprocess_frame(&self, frame: &Frame, target_width: u32) -> Result<Frame> {
        if frame.width() <= target_width {
            return Ok(frame.clone());
        }

        let scale = target_width as f64 / frame.width() as f64;
        let target_height = (frame.height() as f64 * scale) as u32;

        // 简单的最近邻缩放
        self.resize_frame(frame, target_width, target_height)
    }

    /// 简单的帧缩放
    fn resize_frame(&self, frame: &Frame, new_width: u32, new_height: u32) -> Result<Frame> {
        let mut new_data = Vec::with_capacity((new_width * new_height * 3) as usize);
        
        match frame {
            Frame::Simple { data, .. } => {
                let x_ratio = frame.width() as f64 / new_width as f64;
                let y_ratio = frame.height() as f64 / new_height as f64;

                for y in 0..new_height {
                    for x in 0..new_width {
                        let src_x = (x as f64 * x_ratio) as u32;
                        let src_y = (y as f64 * y_ratio) as u32;

                        let src_idx = ((src_y * frame.width() + src_x) * 3) as usize;

                        if src_idx + 2 < data.len() {
                            new_data.push(data[src_idx]);     // R
                            new_data.push(data[src_idx + 1]); // G
                            new_data.push(data[src_idx + 2]); // B
                        } else {
                            new_data.extend_from_slice(&[0, 0, 0]); // 黑色填充
                        }
                    }
                }

                Ok(Frame::new_simple(new_width, new_height, new_data))
            }
            Frame::OpenCV(_) => {
                Err(SceneDetectorError::Detection(
                    "OpenCV frame resizing not implemented".to_string()
                ))
            }
        }
    }

    /// 检查帧是否有效
    pub fn is_valid_frame(&self, frame: &Frame) -> bool {
        match frame {
            Frame::Simple { data, .. } => {
                !frame.is_empty() && frame.channels() == 3 && data.len() == (frame.width() * frame.height() * 3) as usize
            }
            Frame::OpenCV(_) => {
                !frame.is_empty() && frame.channels() == 3
            }
        }
    }

    /// 计算帧的亮度
    pub fn calculate_brightness(&self, frame: &Frame) -> Result<f64> {
        let gray = self.rgb_to_grayscale(frame)?;
        let sum: u64 = gray.iter().map(|&x| x as u64).sum();
        Ok(sum as f64 / gray.len() as f64)
    }

    /// 检测是否为黑帧或白帧
    pub fn is_extreme_frame(&self, frame: &Frame, threshold: f64) -> Result<bool> {
        let brightness = self.calculate_brightness(frame)?;
        Ok(brightness < threshold || brightness > (255.0 - threshold))
    }
}

/// 自适应阈值计算器
pub struct AdaptiveThresholdCalculator {
    window_size: usize,
    differences: Vec<f64>,
}

impl AdaptiveThresholdCalculator {
    pub fn new(window_size: usize) -> Self {
        Self {
            window_size,
            differences: Vec::with_capacity(window_size),
        }
    }

    /// 添加新的差异值
    pub fn add_difference(&mut self, diff: f64) {
        self.differences.push(diff);
        if self.differences.len() > self.window_size {
            self.differences.remove(0);
        }
    }

    /// 计算自适应阈值
    pub fn calculate_threshold(&self, base_threshold: f64, sensitivity: f64) -> f64 {
        if self.differences.is_empty() {
            return base_threshold;
        }

        let mean = self.differences.iter().sum::<f64>() / self.differences.len() as f64;
        let variance = self.differences
            .iter()
            .map(|&x| (x - mean).powi(2))
            .sum::<f64>() / self.differences.len() as f64;
        let std_dev = variance.sqrt();

        // 基于标准差调整阈值
        base_threshold + sensitivity * std_dev
    }

    /// 重置计算器
    pub fn reset(&mut self) {
        self.differences.clear();
    }
}
