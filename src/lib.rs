pub mod types;
pub mod frame_diff;
pub mod detector;
pub mod extractor;

pub use types::{
    DetectorConfig, DetectionAlgorithm, SceneTransition, 
    DetectionResult, Result, SceneDetectorError
};
pub use detector::SceneDetector;
pub use extractor::FrameExtractor;
pub use frame_diff::FrameDifferenceCalculator;

#[cfg(test)]
mod integration_tests {
    use super::*;
    use std::path::PathBuf;
    use tempfile::TempDir;

    #[test]
    fn test_detector_config_default() {
        let config = DetectorConfig::default();
        assert_eq!(config.threshold, 0.3);
        assert_eq!(config.min_scene_length, 30);
        assert_eq!(config.skip_frames, 1);
        assert_eq!(config.algorithm, DetectionAlgorithm::Histogram);
        assert!(!config.extract_keyframes);
        assert_eq!(config.keyframe_interval, 30);
        assert!(config.show_progress);
    }

    #[test]
    fn test_detection_algorithm_from_str() {
        assert_eq!(DetectionAlgorithm::from_str("histogram"), Some(DetectionAlgorithm::Histogram));
        assert_eq!(DetectionAlgorithm::from_str("pixel"), Some(DetectionAlgorithm::Pixel));
        assert_eq!(DetectionAlgorithm::from_str("edge"), Some(DetectionAlgorithm::Edge));
        assert_eq!(DetectionAlgorithm::from_str("combined"), Some(DetectionAlgorithm::Combined));
        assert_eq!(DetectionAlgorithm::from_str("invalid"), None);
    }

    #[test]
    fn test_scene_transition_creation() {
        let scene = SceneTransition::new(0, 100, 200, 30.0, 0.8);
        
        assert_eq!(scene.scene_id, 0);
        assert_eq!(scene.start_frame, 100);
        assert_eq!(scene.end_frame, 200);
        assert_eq!(scene.frame_count(), 101);
        assert!((scene.start_time - 100.0/30.0).abs() < 0.001);
        assert!((scene.end_time - 200.0/30.0).abs() < 0.001);
        assert!((scene.duration - (200.0-100.0)/30.0).abs() < 0.001);
        assert_eq!(scene.confidence, 0.8);
    }

    #[test]
    fn test_detector_creation() {
        let config = DetectorConfig::default();
        let detector = SceneDetector::new(config.clone());
        assert_eq!(detector.config().threshold, config.threshold);
        assert_eq!(detector.config().algorithm, config.algorithm);
    }

    #[test]
    fn test_frame_extractor_creation() {
        let config = DetectorConfig::default();
        let extractor = FrameExtractor::new(config.clone());
        assert_eq!(extractor.config.threshold, config.threshold);
    }

    #[test]
    fn test_output_directory_structure() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = DetectorConfig::default();
        config.output_dir = temp_dir.path().to_path_buf();
        config.extract_keyframes = true;

        let extractor = FrameExtractor::new(config);
        extractor.create_output_directories().unwrap();

        assert!(temp_dir.path().join("frames").exists());
        assert!(temp_dir.path().join("keyframes").exists());
    }

    #[test]
    fn test_detection_result_creation() {
        let scenes = vec![
            SceneTransition::new(0, 0, 100, 30.0, 0.8),
            SceneTransition::new(1, 101, 200, 30.0, 0.9),
        ];
        
        let config = DetectorConfig::default();
        let result = DetectionResult::new(
            PathBuf::from("test.mp4"),
            300,
            30.0,
            scenes,
            &config,
            10.5,
        );

        assert_eq!(result.scene_count(), 2);
        assert_eq!(result.total_frames, 300);
        assert_eq!(result.fps, 30.0);
        assert!((result.duration - 10.0).abs() < 0.001);
        assert!((result.processing_time - 10.5).abs() < 0.001);
    }

    #[test]
    fn test_average_scene_duration() {
        let scenes = vec![
            SceneTransition::new(0, 0, 59, 30.0, 0.8),    // 2.0 seconds
            SceneTransition::new(1, 60, 149, 30.0, 0.9),  // 3.0 seconds
            SceneTransition::new(2, 150, 209, 30.0, 0.7), // 2.0 seconds
        ];
        
        let config = DetectorConfig::default();
        let result = DetectionResult::new(
            PathBuf::from("test.mp4"),
            300,
            30.0,
            scenes,
            &config,
            10.5,
        );

        let avg_duration = result.average_scene_duration();
        assert!((avg_duration - 2.333).abs() < 0.01); // (2+3+2)/3 ≈ 2.333
    }

    #[test]
    fn test_empty_scenes_average_duration() {
        let config = DetectorConfig::default();
        let result = DetectionResult::new(
            PathBuf::from("test.mp4"),
            300,
            30.0,
            vec![],
            &config,
            10.5,
        );

        assert_eq!(result.average_scene_duration(), 0.0);
    }
}
