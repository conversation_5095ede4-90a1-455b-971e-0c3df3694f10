mod types;
mod frame_diff;
mod detector;
mod extractor;

use clap::{Parser, ValueEnum};
use std::path::PathBuf;
use std::fs;
use std::time::Instant;

use types::{DetectorConfig, DetectionAlgorithm, Result, SceneDetectorError};
use detector::SceneDetector;
use extractor::FrameExtractor;

/// Rust电影镜头转场识别工具
#[derive(Parser)]
#[command(name = "scene_detector")]
#[command(about = "A Rust-based movie scene transition detection tool")]
#[command(version = "0.1.0")]
struct Cli {
    /// 输入视频文件路径
    #[arg(short, long, value_name = "FILE", required_unless_present = "create_test")]
    input: Option<PathBuf>,

    /// 输出目录路径
    #[arg(short, long, value_name = "DIR", default_value = "output")]
    output: PathBuf,

    /// 转场检测阈值 (0.0-1.0)
    #[arg(short, long, default_value = "0.1")]
    threshold: f64,

    /// 最小镜头长度（帧数）
    #[arg(short = 'm', long, default_value = "1")]
    min_length: u32,

    /// 跳帧数（用于加速处理）
    #[arg(short = 's', long, default_value = "1")]
    skip: u32,

    /// 检测算法类型
    #[arg(short, long, value_enum, default_value = "combined")]
    algorithm: AlgorithmChoice,

    /// 是否提取关键帧
    #[arg(short = 'k', long)]
    keyframes: bool,

    /// 关键帧提取间隔（帧数）
    #[arg(long, default_value = "30")]
    keyframe_interval: u32,

    /// 是否生成缩略图
    #[arg(long)]
    thumbnails: bool,

    /// 缩略图尺寸 (宽x高)
    #[arg(long, default_value = "160x120")]
    thumbnail_size: String,

    /// 是否生成场景预览图
    #[arg(long)]
    previews: bool,

    /// 预览图包含的帧数
    #[arg(long, default_value = "4")]
    preview_frames: usize,

    /// 创建测试图像序列
    #[arg(long)]
    create_test: bool,

    /// 是否显示详细输出
    #[arg(short, long)]
    verbose: bool,

    /// 是否隐藏进度条
    #[arg(long)]
    no_progress: bool,

    /// 批量处理模式（输入为包含视频文件的目录）
    #[arg(short, long)]
    batch: bool,

    /// 输出JSON文件名
    #[arg(long, default_value = "scenes.json")]
    json_output: String,

    /// 是否提取视频片段
    #[arg(long)]
    extract_clips: bool,
}

#[derive(Clone, ValueEnum)]
enum AlgorithmChoice {
    /// 直方图差异法（推荐）
    Histogram,
    /// 像素差异法
    Pixel,
    /// 边缘差异法
    Edge,
    /// 组合算法
    Combined,
}

impl From<AlgorithmChoice> for DetectionAlgorithm {
    fn from(choice: AlgorithmChoice) -> Self {
        match choice {
            AlgorithmChoice::Histogram => DetectionAlgorithm::Histogram,
            AlgorithmChoice::Pixel => DetectionAlgorithm::Pixel,
            AlgorithmChoice::Edge => DetectionAlgorithm::Edge,
            AlgorithmChoice::Combined => DetectionAlgorithm::Combined,
        }
    }
}

fn main() -> Result<()> {
    let cli = Cli::parse();

    // 如果是创建测试模式
    if cli.create_test {
        return create_test_images();
    }

    // 验证输入参数
    if let Some(ref input) = cli.input {
        validate_args(&cli)?;
    }

    // 创建检测器配置
    let config = create_detector_config(&cli)?;

    if cli.verbose {
        print_config(&config);
    }

    // 执行检测
    if cli.batch {
        process_batch(&cli, config)?;
    } else {
        process_single_video(&cli, config)?;
    }

    Ok(())
}

fn validate_args(cli: &Cli) -> Result<()> {
    // 验证输入文件/目录
    if let Some(ref input) = cli.input {
        if !input.exists() {
            return Err(SceneDetectorError::InvalidConfig(
                format!("输入路径不存在: {}", input.display())
            ));
        }
    }

    if let Some(ref input) = cli.input {
        if cli.batch && !input.is_dir() {
            return Err(SceneDetectorError::InvalidConfig(
                "批量模式需要输入目录路径".to_string()
            ));
        }

        if !cli.batch && !input.is_file() && !input.is_dir() {
            return Err(SceneDetectorError::InvalidConfig(
                "单文件模式需要输入文件路径或图像目录".to_string()
            ));
        }
    }

    // 验证阈值范围
    if cli.threshold < 0.0 || cli.threshold > 1.0 {
        return Err(SceneDetectorError::InvalidConfig(
            "阈值必须在0.0-1.0范围内".to_string()
        ));
    }

    // 验证最小镜头长度
    if cli.min_length == 0 {
        return Err(SceneDetectorError::InvalidConfig(
            "最小镜头长度必须大于0".to_string()
        ));
    }

    // 验证缩略图尺寸格式
    if cli.thumbnails {
        parse_thumbnail_size(&cli.thumbnail_size)?;
    }

    Ok(())
}

fn parse_thumbnail_size(size_str: &str) -> Result<(i32, i32)> {
    let parts: Vec<&str> = size_str.split('x').collect();
    if parts.len() != 2 {
        return Err(SceneDetectorError::InvalidConfig(
            "缩略图尺寸格式错误，应为 '宽x高'".to_string()
        ));
    }

    let width = parts[0].parse::<i32>().map_err(|_| {
        SceneDetectorError::InvalidConfig("无效的宽度值".to_string())
    })?;

    let height = parts[1].parse::<i32>().map_err(|_| {
        SceneDetectorError::InvalidConfig("无效的高度值".to_string())
    })?;

    if width <= 0 || height <= 0 {
        return Err(SceneDetectorError::InvalidConfig(
            "缩略图尺寸必须大于0".to_string()
        ));
    }

    Ok((width, height))
}

fn create_detector_config(cli: &Cli) -> Result<DetectorConfig> {
    Ok(DetectorConfig {
        threshold: cli.threshold,
        min_scene_length: cli.min_length,
        skip_frames: cli.skip,
        algorithm: cli.algorithm.clone().into(),
        extract_keyframes: cli.keyframes,
        keyframe_interval: cli.keyframe_interval,
        output_dir: cli.output.clone(),
        show_progress: !cli.no_progress,
        extract_clips: cli.extract_clips,
    })
}

fn print_config(config: &DetectorConfig) {
    println!("检测器配置:");
    println!("  算法: {}", config.algorithm.as_str());
    println!("  阈值: {}", config.threshold);
    println!("  最小镜头长度: {} 帧", config.min_scene_length);
    println!("  跳帧数: {}", config.skip_frames);
    println!("  提取关键帧: {}", config.extract_keyframes);
    if config.extract_keyframes {
        println!("  关键帧间隔: {} 帧", config.keyframe_interval);
    }
    println!("  输出目录: {}", config.output_dir.display());
    println!();
}

fn process_single_video(cli: &Cli, config: DetectorConfig) -> Result<()> {
    let start_time = Instant::now();

    let input_path = cli.input.as_ref().unwrap();

    // 验证输入文件
    println!("验证输入文件...");
    SceneDetector::validate_input(input_path)?;
    println!("✓ 输入文件有效");

    // 创建检测器并执行检测
    let mut detector = SceneDetector::new(config.clone());
    let mut result = detector.detect_scenes(input_path)?;

    // 提取帧
    if !result.scenes.is_empty() {
        let extractor = FrameExtractor::new(config.clone());
        extractor.extract_frames(input_path, &mut result.scenes)?;

        // 生成缩略图
        if cli.thumbnails {
            let thumbnail_size = parse_thumbnail_size(&cli.thumbnail_size)?;
            let thumbnail_size_u32 = (thumbnail_size.0 as u32, thumbnail_size.1 as u32);
            let thumbnail_paths = extractor.extract_thumbnails(input_path, &result.scenes, thumbnail_size_u32)?;
            println!("✓ 生成了 {} 个缩略图", thumbnail_paths.len());
        }

        // 生成预览图（简化版本暂不支持）
        if cli.previews {
            println!("注意：预览图功能在简化版本中暂不支持");
        }
    }

    // 保存结果到JSON
    save_results_to_json(&result, &config.output_dir, &cli.json_output)?;

    let total_time = start_time.elapsed();
    println!("\n总处理时间: {:.2} 秒", total_time.as_secs_f64());

    Ok(())
}

fn process_batch(cli: &Cli, config: DetectorConfig) -> Result<()> {
    println!("批量处理模式");

    let input_path = cli.input.as_ref().unwrap();

    // 查找目录中的视频文件
    let video_files = find_video_files(input_path)?;

    if video_files.is_empty() {
        println!("在目录中未找到视频文件: {}", input_path.display());
        return Ok(());
    }

    println!("找到 {} 个视频文件", video_files.len());

    let mut all_results = Vec::new();

    for (index, video_file) in video_files.iter().enumerate() {
        println!("\n处理视频 {}/{}: {}", index + 1, video_files.len(), video_file.display());
        
        match process_single_video_file(video_file, &config) {
            Ok(result) => {
                all_results.push(result);
                println!("✓ 处理完成");
            }
            Err(e) => {
                eprintln!("✗ 处理失败: {}", e);
                continue;
            }
        }
    }

    // 保存批量处理结果
    save_batch_results(&all_results, &config.output_dir)?;

    println!("\n批量处理完成，成功处理 {}/{} 个视频", all_results.len(), video_files.len());

    Ok(())
}

fn find_video_files(dir: &PathBuf) -> Result<Vec<PathBuf>> {
    let mut video_files = Vec::new();
    let video_extensions = ["mp4", "avi", "mov", "mkv", "wmv", "flv", "webm"];

    for entry in fs::read_dir(dir)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_file() {
            if let Some(extension) = path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    if video_extensions.contains(&ext_str.to_lowercase().as_str()) {
                        video_files.push(path);
                    }
                }
            }
        }
    }

    video_files.sort();
    Ok(video_files)
}

fn process_single_video_file(video_path: &PathBuf, config: &DetectorConfig) -> Result<types::DetectionResult> {
    let mut detector = SceneDetector::new(config.clone());
    let mut result = detector.detect_scenes(video_path)?;

    // 提取帧
    if !result.scenes.is_empty() {
        let extractor = FrameExtractor::new(config.clone());
        extractor.extract_frames(video_path, &mut result.scenes)?;
    }

    Ok(result)
}

fn save_results_to_json(
    result: &types::DetectionResult,
    output_dir: &PathBuf,
    filename: &str,
) -> Result<()> {
    fs::create_dir_all(output_dir)?;
    let json_path = output_dir.join(filename);
    
    let json_content = serde_json::to_string_pretty(result)?;
    fs::write(&json_path, json_content)?;
    
    println!("✓ 结果已保存到: {}", json_path.display());
    Ok(())
}

fn save_batch_results(
    results: &[types::DetectionResult],
    output_dir: &PathBuf,
) -> Result<()> {
    let batch_json_path = output_dir.join("batch_results.json");
    let json_content = serde_json::to_string_pretty(results)?;
    fs::write(&batch_json_path, json_content)?;
    
    println!("✓ 批量处理结果已保存到: {}", batch_json_path.display());
    Ok(())
}

/// 创建测试图像序列
fn create_test_images() -> Result<()> {
    use image::{RgbImage, ImageFormat};
    use std::fs;

    let output_dir = "test_images";
    fs::create_dir_all(output_dir)?;

    println!("创建测试图像序列...");

    // 创建5个不同颜色的场景，每个场景30帧
    let scenes = [
        (0, 30, [255, 100, 100]),   // 红色场景
        (30, 60, [100, 255, 100]),  // 绿色场景
        (60, 90, [100, 100, 255]),  // 蓝色场景
        (90, 120, [255, 255, 100]), // 黄色场景
        (120, 150, [255, 100, 255]), // 紫色场景
    ];

    for (start, end, color) in scenes {
        println!("创建场景 {} (帧 {}-{})", (start / 30) + 1, start, end - 1);

        for frame in start..end {
            let mut img = RgbImage::new(640, 480);

            // 填充基础颜色并添加变化
            let variation = (frame - start) as f32 / (end - start) as f32;

            for (x, y, pixel) in img.enumerate_pixels_mut() {
                // 添加位置相关的变化
                let x_factor = x as f32 / 640.0;
                let y_factor = y as f32 / 480.0;
                let brightness_factor = 0.7 + 0.3 * (x_factor + y_factor) / 2.0 + 0.2 * variation;

                let r = (color[0] as f32 * brightness_factor) as u8;
                let g = (color[1] as f32 * brightness_factor) as u8;
                let b = (color[2] as f32 * brightness_factor) as u8;

                *pixel = image::Rgb([r, g, b]);
            }

            let filename = format!("{}/frame_{:04}.jpg", output_dir, frame);
            img.save_with_format(&filename, ImageFormat::Jpeg)
                .map_err(|e| SceneDetectorError::FrameExtraction(format!("Failed to save test image: {}", e)))?;
        }
    }

    println!("✓ 创建了 150 个测试图像在 {} 目录中", output_dir);
    println!("这些图像模拟了 5 个不同的场景，每个场景 30 帧");
    println!();
    println!("现在可以使用以下命令测试场景检测:");
    println!("  ./target/release/scene_detector -i test_images -o demo_output --verbose");

    Ok(())
}
